package com.imile.permission.integration.pcs;

import com.github.pagehelper.PageInfo;
import com.imile.accout.AccountRpcService;
import com.imile.accout.req.AccountFilterReq;
import com.imile.accout.res.SupplierAccountRes;
import com.imile.common.page.PaginationResult;
import com.imile.enums.AbledStatusEnum;
import com.imile.permission.util.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/8
 */
@Service
public class AccountRpcServiceIntegration {

    @Reference(version = "1.0.0", retries = 0, timeout = 10000, check = false)
    private AccountRpcService accountRpcService;

    public List<SupplierAccountRes> getSubAccountList(List<String> primaryAccountCodeList) {
        if (CollectionUtils.isEmpty(primaryAccountCodeList)) {
            return Collections.emptyList();
        }
        AccountFilterReq accountFilterReq = new AccountFilterReq();
        accountFilterReq.setPrimaryAccountCodeList(primaryAccountCodeList);
        RpcResult<List<SupplierAccountRes>> byPrimaryAccountCodeList = accountRpcService.listSupplierAccount(accountFilterReq);
        List<SupplierAccountRes> process = RpcResultProcessor.process(byPrimaryAccountCodeList);
        return process;
    }

    public PaginationResult<SupplierAccountRes> pageSupplierAccount(AccountFilterReq req) {
        RpcResult<PaginationResult<SupplierAccountRes>> pageInfoRpcResult = accountRpcService.pageSupplierAccount(req);
        PaginationResult<SupplierAccountRes> process = RpcResultProcessor.process(pageInfoRpcResult);
        return process;
    }


    public List<SupplierAccountRes> getCurrentAccountDetail(List<String> accountCodeList) {
        if (CollectionUtils.isEmpty(accountCodeList)) {
            return Collections.emptyList();
        }
        AccountFilterReq accountFilterReq = new AccountFilterReq();
        accountFilterReq.setAccountCodeList(accountCodeList);
        RpcResult<List<SupplierAccountRes>> byPrimaryAccountCodeList = accountRpcService.listSupplierAccount(accountFilterReq);
        List<SupplierAccountRes> process = RpcResultProcessor.process(byPrimaryAccountCodeList);
        return process;
    }

}

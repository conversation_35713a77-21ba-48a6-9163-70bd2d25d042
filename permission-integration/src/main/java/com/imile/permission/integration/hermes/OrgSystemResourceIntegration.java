package com.imile.permission.integration.hermes;

import com.imile.hermes.resource.dto.ResResourceTreeForAuthApiDTO;
import com.imile.hermes.resource.dto.ResSystemResourceTreeDTO;
import com.imile.hermes.resource.dto.ResourceDataDTO;
import com.imile.permission.integration.hermes.client.OrgSystemResourceApiClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
@Service
public class OrgSystemResourceIntegration {


    @Autowired
    private OrgSystemResourceApiClient orgSystemResourceApiClient;

    public List<ResourceDataDTO> getByIdList(Long orgId, String lang, List<Long> menuIdList) {
        return orgSystemResourceApiClient.getByIdList(orgId, lang, menuIdList);
    }

    public List<ResResourceTreeForAuthApiDTO> getAuthTreeByResourceId(Long orgId,
                                                                      List<Long> idList,
                                                                      String system,
                                                                      List<String> systems,
                                                                      String[] nodeTypes,
                                                                      String langType,
                                                                      String parentCode
    ) {
        return orgSystemResourceApiClient.getAuthTreeByResourceId(orgId, idList, system, systems, nodeTypes, langType, parentCode);
    }

    public List<ResResourceTreeForAuthApiDTO> getAuthTreeWithDescription(Long orgId,
                                                                      List<Long> idList,
                                                                      String system,
                                                                      String[] nodeTypes,
                                                                      String langType,
                                                                      String parentCode
    ) {
        return orgSystemResourceApiClient.getAuthTreeWithDescription(orgId, idList, system, nodeTypes, langType, parentCode);
    }

    public List<ResSystemResourceTreeDTO> getMenuTree(Long orgId, Map<Long, List<Long>> menuMap, String lang) {
        return orgSystemResourceApiClient.getMenuTree(orgId, menuMap, lang);
    }
    public List<ResSystemResourceTreeDTO> getMenuTreeList(Long orgId, List<Long> menuIdList, String lang) {
        return orgSystemResourceApiClient.getMenuTreeList(orgId, menuIdList, lang);
    }

    public List<Long> convertPartially(Long orgId, List<Long> menuIdList) {
        return orgSystemResourceApiClient.convertPartially(orgId, menuIdList);
    }

    public Map<Long, String> getMenuChainMergedNameMap(Long orgId, String lang, List<Long> menuIdList) {
        return orgSystemResourceApiClient.getMenuChainMergedNameMap(orgId, lang, menuIdList);
    }

    public List<Long> getValidMenuId(Long orgId, List<Long> menuIdList) {
        return orgSystemResourceApiClient.getValidMenuId(orgId, menuIdList);
    }

    public List<Long> getParentAndChildMenuIDList(Long orgId, Long menuId) {
        return orgSystemResourceApiClient.getParentAndChildMenuIDList(orgId, menuId);
    }

    public List<Long> getParentMenuIDList(Long orgId, List<Long> menuIdList) {
        return orgSystemResourceApiClient.getParentMenuIDList(orgId, menuIdList);
    }
}

package com.imile.permission.integration.resource.client;

import com.imile.permission.util.RpcResultProcessor;
import com.imile.resource.api.dto.ResResourceTreeForAuthApiDTO;
import com.imile.resource.api.dto.ResourceUserMenuOrderQueryApiDTO;
import com.imile.resource.api.resourceUserMenuOrder.ResourceUserMenuOrderApi;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/14
 */
@Service
public class ResourceClient {

    @Reference(version = "1.0.0", check = false, timeout = 6000)
    private ResourceUserMenuOrderApi resourceUserMenuOrderApi;

    public List<ResResourceTreeForAuthApiDTO> selectOrderedMenu(ResourceUserMenuOrderQueryApiDTO param){
        return RpcResultProcessor.process(resourceUserMenuOrderApi.selectOrderedMenu(param));
    }

}

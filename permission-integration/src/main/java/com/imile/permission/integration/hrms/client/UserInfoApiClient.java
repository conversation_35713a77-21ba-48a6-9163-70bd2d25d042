package com.imile.permission.integration.hrms.client;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.user.api.UserApi;
import com.imile.hrms.api.user.api.UserInfoApi;
import com.imile.hrms.api.user.dto.UserPmsPageDTO;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.param.PmsPageParam;
import com.imile.hrms.api.user.result.UserBaseInfoDTO;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import com.imile.permission.util.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: This is a sample class.
 * @author: 
 * @date: 2023/10/24
 */
@Service
public class UserInfoApiClient {

    @Reference(version = "1.0.0")
    private UserInfoApi userInfoApi;
    @Reference(version = "1.0.0")
    private UserApi userApi;
    @Reference(version = "1.0.0")
    private com.imile.genesis.api.service.UserApi genesisUserApi;

    public PaginationResult<UserPmsPageDTO> listUserInfo(PmsPageParam param) {
        return RpcResultProcessor.process(userInfoApi.pmsPage(param));
    }

    public UserBaseInfoDTO getUserBaseInfoCache(String userCode) {
        return RpcResultProcessor.process(userApi.getUserBaseInfoCache(userCode));
    }

    public List<UserDynamicInfoDTO> getUserDynamicInfoList(List<String> userCodes, List<UserDynamicFieldEnum> dynamicFieldList) {
        return RpcResultProcessor.process(userApi.listUserDynamicInfo(userCodes, dynamicFieldList));
    }

    public UserDynamicInfoDTO getUserDynamicInfo(String userCodes, List<UserDynamicFieldEnum> dynamicFieldList) {
        return RpcResultProcessor.process(userApi.getUserDynamicInfo(userCodes, dynamicFieldList));
    }

    public com.imile.genesis.api.model.result.user.UserBaseInfoDTO getGenesisUserBaseInfoCache(String userCode){
        RpcResult<com.imile.genesis.api.model.result.user.UserBaseInfoDTO> userBaseInfoCache = genesisUserApi.getUserBaseInfoCache(userCode);
        return RpcResultProcessor.process(userBaseInfoCache);
    }
}

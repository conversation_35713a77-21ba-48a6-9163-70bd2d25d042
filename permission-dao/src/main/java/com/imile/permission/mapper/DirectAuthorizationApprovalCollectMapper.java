package com.imile.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.permission.domain.applicationApprove.query.AuthorityApprovalPageQuery;
import com.imile.permission.domain.applicationApprove.vo.DirectAuthorizationApplicationRecordVO;
import com.imile.permission.domain.applicationApprove.vo.UserDirectAuthCollectVO;
import com.imile.permission.domain.entity.DirectAuthorizationApprovalCollectDO;
import com.imile.permission.domain.permission.dto.ApprovalCollectDTO;
import com.imile.permission.domain.user.query.UserPermissionQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 直接授权申请汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-19
 */
@Mapper
public interface DirectAuthorizationApprovalCollectMapper extends BaseMapper<DirectAuthorizationApprovalCollectDO> {

    List<DirectAuthorizationApplicationRecordVO> getApplicationRecordList(@Param("query") AuthorityApprovalPageQuery query);

    Integer getApplicationRecordEffectiveCount(@Param("userCode") String userCode, @Param("now") LocalDateTime now, @Param("sourceTypeList") List<String> sourceTypeList);

    Integer getApplicationRecordLossCount(@Param("userCode") String userCode, @Param("now") LocalDateTime now, @Param("sourceTypeList") List<String> sourceTypeList);

    List<UserDirectAuthCollectVO> getUserDirectAuthCollect();

    void batchUpsert(@Param("collectDOList") List<DirectAuthorizationApprovalCollectDO> collectDOList);

    List<ApprovalCollectDTO> getApprovalCollectDTOList(@Param("query") UserPermissionQuery query);

}

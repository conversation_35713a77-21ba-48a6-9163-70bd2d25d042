package com.imile.permission.mapper;

import com.imile.permission.domain.client.dto.ClientCountRoleDTO;
import com.imile.permission.domain.client.dto.ClientRoleRecordDTO;
import com.imile.permission.domain.entity.ClientRoleAuthorityCollectDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 商家角色授权汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Mapper
public interface ClientRoleAuthorityCollectMapper extends BaseMapper<ClientRoleAuthorityCollectDO> {

    List<ClientRoleRecordDTO> getClientRole(@Param("clientCode") String clientCode, @Param("isExpired") Boolean isExpired, @Param("expirationDate") LocalDateTime expirationDate);

    Integer getRecordEffectiveCount(@Param("clientCode") String clientCode, @Param("now") LocalDateTime now);

    Integer getRecordLossCount(@Param("clientCode") String clientCode, @Param("now") LocalDateTime now);

    List<ClientCountRoleDTO> countRoleMap(@Param("roleIdList") List<Long> roleIdList, @Param("localDateTime") LocalDateTime localDateTime);

    List<String> getClientCodeByRoleId(@Param("roleId") Long roleId, @Param("now") LocalDateTime now);
}

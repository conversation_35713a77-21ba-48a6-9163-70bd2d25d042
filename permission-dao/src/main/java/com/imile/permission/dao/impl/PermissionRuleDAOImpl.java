package com.imile.permission.dao.impl;

import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.domain.entity.PermissionRuleDO;
import com.imile.permission.mapper.PermissionRuleMapper;
import com.imile.permission.dao.PermissionRuleDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 条件规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Service
public class PermissionRuleDAOImpl extends ServiceImpl<PermissionRuleMapper, PermissionRuleDO> implements PermissionRuleDAO {

    @Override
    public boolean updateDO(PermissionRuleDO permissionRuleDO) {
        return updateDO(permissionRuleDO, true);
    }

    @Override
    public boolean updateDO(PermissionRuleDO permissionRuleDO, boolean isCleanLastUpd) {
        if (Objects.isNull(permissionRuleDO) || Objects.isNull(permissionRuleDO.getId())) {
            return Boolean.FALSE;
        }
        if (isCleanLastUpd) {
            permissionRuleDO.cleanLastUpd();
        }
        return lambdaUpdate()
                .eq(PermissionRuleDO::getId, permissionRuleDO.getId())
                .update(new PermissionRuleDO());
    }

    @Override
    public List<PermissionRuleDO> listByTypeCodeList(List<String> typcCodeList) {
        if (CollectionUtils.isEmpty(typcCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(PermissionRuleDO::getRelationTypeCode, typcCodeList)
                .eq(PermissionRuleDO::getIsDelete, BusinessConstant.N)
                .list();
    }

    @Override
    public void removeRuleByTypeCode(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return;
        }
        lambdaUpdate()
                .eq(PermissionRuleDO::getRelationTypeCode, typeCode)
                .eq(PermissionRuleDO::getIsDelete, BusinessConstant.N)
                .set(PermissionRuleDO::getIsDelete, BusinessConstant.Y)
                .update(new PermissionRuleDO());
    }

    @Override
    public List<PermissionRuleDO> getRuleByTypeCode(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(PermissionRuleDO::getRelationTypeCode, typeCode)
                .eq(PermissionRuleDO::getIsDelete, BusinessConstant.N)
                .list();
    }

}

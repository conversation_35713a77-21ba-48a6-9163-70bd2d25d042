package com.imile.permission.dao.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.RoleAuthorityApprovalCollectDAO;
import com.imile.permission.domain.applicationApprove.vo.UserRoleCollectVO;
import com.imile.permission.domain.entity.RoleAuthorityApprovalCollectDO;
import com.imile.permission.domain.permission.dto.ApprovalCollectDTO;
import com.imile.permission.domain.role.dto.UserRoleDTO;
import com.imile.permission.domain.user.query.UserPermissionQuery;
import com.imile.permission.entity.BaseDO;
import com.imile.permission.mapper.RoleAuthorityApprovalCollectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 角色授权汇总表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-06
 */
@Service
public class RoleAuthorityApprovalCollectDAOImpl extends ServiceImpl<RoleAuthorityApprovalCollectMapper, RoleAuthorityApprovalCollectDO> implements RoleAuthorityApprovalCollectDAO {

    @Override
    public boolean updateDO(RoleAuthorityApprovalCollectDO roleAuthorityApprovalCollectDO) {
        return updateDO(roleAuthorityApprovalCollectDO, true);
    }

    @Override
    public boolean updateDO(RoleAuthorityApprovalCollectDO roleAuthorityApprovalCollectDO, boolean isCleanLastUpd) {
        if (Objects.isNull(roleAuthorityApprovalCollectDO) || Objects.isNull(roleAuthorityApprovalCollectDO.getId())) {
            return Boolean.FALSE;
        }
        if (isCleanLastUpd) {
            roleAuthorityApprovalCollectDO.cleanLastUpd();
        }
        return lambdaUpdate()
                .eq(RoleAuthorityApprovalCollectDO::getId, roleAuthorityApprovalCollectDO.getId())
                .update(new RoleAuthorityApprovalCollectDO());
    }

    @Override
    public List<RoleAuthorityApprovalCollectDO> getUserRoleCollect(String userCode, Long roleId) {
        return lambdaQuery()
                .eq(RoleAuthorityApprovalCollectDO::getUserCode, userCode)
                .eq(RoleAuthorityApprovalCollectDO::getRoleId, roleId)
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .list();
    }

    @Override
    public void updateUserExpirationDate(String userCode, Collection<Long> removeIdList, LocalDateTime now) {
        lambdaUpdate()
                .eq(RoleAuthorityApprovalCollectDO::getUserCode, userCode)
                .in(RoleAuthorityApprovalCollectDO::getRoleId, removeIdList)
                .set(RoleAuthorityApprovalCollectDO::getExpirationDate, now)
                .update(new RoleAuthorityApprovalCollectDO());
    }

    @Override
    public List<RoleAuthorityApprovalCollectDO> getUserRoleList(String userCode, Collection<Long> idList) {
        return lambdaQuery()
                .eq(RoleAuthorityApprovalCollectDO::getUserCode, userCode)
                .in(RoleAuthorityApprovalCollectDO::getRoleId, idList)
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .list();
    }

    @Override
    public List<RoleAuthorityApprovalCollectDO> getUserAndRoleList(List<String> userCodeList, Collection<Long> idList) {
        return lambdaQuery()
                .in(RoleAuthorityApprovalCollectDO::getUserCode, userCodeList)
                .in(RoleAuthorityApprovalCollectDO::getRoleId, idList)
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .list();
    }

    @Override
    public Integer getApplicationRecordEffectiveCount(String userCode, LocalDateTime now) {
        if (StringUtils.isBlank(userCode)) {
            return 0;
        }
        return getBaseMapper().getApplicationRecordEffectiveCount(userCode, now);
    }

    @Override
    public Integer getApplicationRecordLossCount(String userCode, LocalDateTime now) {
        if (StringUtils.isBlank(userCode)) {
            return 0;
        }
        return getBaseMapper().getApplicationRecordLossCount(userCode, now);
    }

    @Override
    public void updateRoleAuthorityApprovalCollectExpirationDateApplicationDate(RoleAuthorityApprovalCollectDO collect) {
        lambdaUpdate()
                .eq(RoleAuthorityApprovalCollectDO::getUserCode, collect.getUserCode())
                .eq(RoleAuthorityApprovalCollectDO::getRoleId, collect.getRoleId())

                .set(RoleAuthorityApprovalCollectDO::getExpirationDate, collect.getExpirationDate())
                .set(RoleAuthorityApprovalCollectDO::getApplicationDate, collect.getApplicationDate())
                .update(new RoleAuthorityApprovalCollectDO());
    }

    @Override
    public void userRoleUpdateOrSave(RoleAuthorityApprovalCollectDO roleAuthorityApprovalCollectDO) {
        if (Objects.isNull(roleAuthorityApprovalCollectDO)) {
            return;
        }

        // 根据userCode和roleId查询用户角色授权汇总信息
        Wrapper<RoleAuthorityApprovalCollectDO> updateWrapper = new UpdateWrapper<RoleAuthorityApprovalCollectDO>()
                .eq("user_code", roleAuthorityApprovalCollectDO.getUserCode())
                .eq("role_id", roleAuthorityApprovalCollectDO.getRoleId());
        saveOrUpdate(roleAuthorityApprovalCollectDO, updateWrapper);
    }

    @Override
    public void userRoleBatchUpsert(List<RoleAuthorityApprovalCollectDO> collectDOList) {
        if (collectDOList.isEmpty()) {
            return;
        }
        getBaseMapper().batchUpsert(collectDOList);
    }

    @Override
    public List<RoleAuthorityApprovalCollectDO> getUserRolePermissionCollect(UserPermissionQuery query) {
        return lambdaQuery().eq(RoleAuthorityApprovalCollectDO::getIsDelete, BusinessConstant.N)
                .between(RoleAuthorityApprovalCollectDO::getExpirationDate,
                        query.getStartExpireTime(), query.getEndExpireTime())
                .list();
    }

    @Override
    public List<UserRoleCollectVO> getUserRoleCollect() {
        return getBaseMapper().getUserRoleCollect();
    }

    @Override
    public void batchDelete(List<Long> ids) {
        if (ids.isEmpty()) {
            return;
        }
        getBaseMapper().deleteBatchIds(ids);
    }

    @Override
    public List<RoleAuthorityApprovalCollectDO> getUserBindingRoleCollect(Collection<String> userCodeList, Long roleId) {
        return lambdaQuery()
                .in(RoleAuthorityApprovalCollectDO::getUserCode, userCodeList)
                .eq(RoleAuthorityApprovalCollectDO::getRoleId, roleId)
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .list();
    }

    @Override
    public void updateUserBindingRoleCollectExpirationDate(List<String> userCodeList, Long roleId, LocalDateTime time) {
        lambdaUpdate()
                .in(RoleAuthorityApprovalCollectDO::getUserCode, userCodeList)
                .eq(RoleAuthorityApprovalCollectDO::getRoleId, roleId)
                .set(RoleAuthorityApprovalCollectDO::getExpirationDate, time)
                .update();
    }

    @Override
    public void updateUserRoleCollectExpirationDate(List<String> userCodeList, List<Long> roleIdList, LocalDateTime time) {
        lambdaUpdate()
                .in(RoleAuthorityApprovalCollectDO::getUserCode, userCodeList)
                .in(RoleAuthorityApprovalCollectDO::getRoleId, roleIdList)
                .set(RoleAuthorityApprovalCollectDO::getExpirationDate, time)
                .update();
    }

    @Override
    public List<ApprovalCollectDTO> getValidatedExpirationCollectDTO(UserPermissionQuery query) {
        return getBaseMapper().getApprovalCollectDTOList(query);
    }

    @Override
    public List<UserRoleDTO> listSystemRoleByUserCode(List<String> system, List<String> userCodeList) {
        return getBaseMapper().listSystemRoleByUserCode(system, userCodeList);
    }
}

package com.imile.permission.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.DirectAuthorizationApprovalDetailDAO;
import com.imile.permission.domain.applicationApprove.query.DirectAuthorizationApplicationDetailsQuery;
import com.imile.permission.domain.entity.DirectAuthorizationApprovalDetailDO;
import com.imile.permission.mapper.DirectAuthorizationApprovalDetailMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 直接授权申请明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-19
 */
@Service
public class DirectAuthorizationApprovalDetailDAOImpl extends ServiceImpl<DirectAuthorizationApprovalDetailMapper, DirectAuthorizationApprovalDetailDO> implements DirectAuthorizationApprovalDetailDAO {

    @Override
    public boolean updateDO(DirectAuthorizationApprovalDetailDO directAuthorizationApprovalDetailDO) {
        return updateDO(directAuthorizationApprovalDetailDO, true);
    }

    @Override
    public boolean updateDO(DirectAuthorizationApprovalDetailDO directAuthorizationApprovalDetailDO, boolean isCleanLastUpd) {
        if (Objects.isNull(directAuthorizationApprovalDetailDO) || Objects.isNull(directAuthorizationApprovalDetailDO.getId())) {
            return Boolean.FALSE;
        }
        if (isCleanLastUpd) {
            directAuthorizationApprovalDetailDO.cleanLastUpd();
        }
        return lambdaUpdate()
                .eq(DirectAuthorizationApprovalDetailDO::getId, directAuthorizationApprovalDetailDO.getId())
                .update(new DirectAuthorizationApprovalDetailDO());
    }

    @Override
    public List<DirectAuthorizationApprovalDetailDO> select(DirectAuthorizationApplicationDetailsQuery directAuthorizationApplicationDetailsQuery) {
        if (Objects.isNull(directAuthorizationApplicationDetailsQuery)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(DirectAuthorizationApprovalDetailDO::getIsDelete, BusinessConstant.N)
                .eq(Objects.nonNull(directAuthorizationApplicationDetailsQuery.getUserCode()), DirectAuthorizationApprovalDetailDO::getUserCode, directAuthorizationApplicationDetailsQuery.getUserCode())
                .eq(Objects.nonNull(directAuthorizationApplicationDetailsQuery.getSourceId()), DirectAuthorizationApprovalDetailDO::getSourceId, directAuthorizationApplicationDetailsQuery.getSourceId())
                .eq(Objects.nonNull(directAuthorizationApplicationDetailsQuery.getSourceType()), DirectAuthorizationApprovalDetailDO::getSourceType, directAuthorizationApplicationDetailsQuery.getSourceType())
                // .orderByDesc(DirectAuthorizationApprovalDetailDO::getOperationTime)
                .last("order by operation_time desc, id desc")
                .list();
    }

    @Override
    public void patchSave(List<DirectAuthorizationApprovalDetailDO> directDetailList) {
        if (CollectionUtils.isEmpty(directDetailList)) {
            return;
        }
        for (List<DirectAuthorizationApprovalDetailDO> list : Lists.partition(directDetailList, 1000)) {
            getBaseMapper().patchSave(list);
        }
    }


}

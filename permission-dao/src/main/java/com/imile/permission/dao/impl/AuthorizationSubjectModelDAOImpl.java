package com.imile.permission.dao.impl;

import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.domain.entity.AuthorizationSubjectModelDO;
import com.imile.permission.mapper.AuthorizationSubjectModelMapper;
import com.imile.permission.dao.AuthorizationSubjectModelDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 授权主体模型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
public class AuthorizationSubjectModelDAOImpl extends ServiceImpl<AuthorizationSubjectModelMapper, AuthorizationSubjectModelDO> implements AuthorizationSubjectModelDAO {
    @Override
    public List<AuthorizationSubjectModelDO> modelList() {
        return lambdaQuery()
                .eq(AuthorizationSubjectModelDO::getIsDelete, BusinessConstant.N)
                .list();
    }

    @Override
    public AuthorizationSubjectModelDO getModelBySubjectModelCode(String subjectModelCode) {
        if (StringUtils.isBlank(subjectModelCode)) {
            return null;
        }
        List<AuthorizationSubjectModelDO> list = lambdaQuery()
                .eq(AuthorizationSubjectModelDO::getIsDelete, BusinessConstant.N)
                .eq(AuthorizationSubjectModelDO::getSubjectModelCode, subjectModelCode)
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }


    @Override
    public void updateModelBySubjectModelCode(AuthorizationSubjectModelDO authorizationSubjectModelDO) {
        if (Objects.isNull(authorizationSubjectModelDO)) {
            return;
        }

        lambdaUpdate()
                .eq(AuthorizationSubjectModelDO::getSubjectModelCode, authorizationSubjectModelDO.getSubjectModelCode())
                .set(AuthorizationSubjectModelDO::getSubjectModelName, authorizationSubjectModelDO.getSubjectModelName())
                .set(AuthorizationSubjectModelDO::getDefineJson, authorizationSubjectModelDO.getDefineJson())
                .update(new AuthorizationSubjectModelDO());

    }

    @Override
    public void modelBatchDelete(List<String> subjectModelCodeList) {
        if (CollectionUtils.isEmpty(subjectModelCodeList)) {
            return;
        }
        lambdaUpdate()
                .in(AuthorizationSubjectModelDO::getSubjectModelCode, subjectModelCodeList)
                .set(AuthorizationSubjectModelDO::getIsDelete, BusinessConstant.Y)
                .update(new AuthorizationSubjectModelDO());

    }
}

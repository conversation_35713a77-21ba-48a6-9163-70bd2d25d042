package com.imile.permission.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.permission.domain.applicationApprove.query.WorkCenterApplicationDetailsQuery;
import com.imile.permission.domain.entity.WorkCenterApprovalDetailDO;

import java.util.List;

/**
 * <p>
 * 流程授权详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface WorkCenterApprovalDetailDAO extends IService<WorkCenterApprovalDetailDO> {

    /**
     * 更新信息
     *
     * @param workCenterApprovalDetailDO workCenterApprovalDetailDO
     * @return boolean
     */
    boolean updateDO(WorkCenterApprovalDetailDO workCenterApprovalDetailDO);

    /**
     * 更新信息
     *
     * @param workCenterApprovalDetailDO workCenterApprovalDetailDO
     * @param isCleanLastUpd isCleanLastUpd
     * @return boolean
     */
    boolean updateDO(WorkCenterApprovalDetailDO workCenterApprovalDetailDO, boolean isCleanLastUpd);

    List<WorkCenterApprovalDetailDO> getWorkCenterApplicationDetails(WorkCenterApplicationDetailsQuery query);
}

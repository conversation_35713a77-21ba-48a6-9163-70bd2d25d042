package com.imile.permission.dao.impl;

import com.imile.permission.domain.entity.PermissionEventLogDO;
import com.imile.permission.mapper.PermissionEventLogMapper;
import com.imile.permission.dao.PermissionEventLogDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 权限变更事件日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class PermissionEventLogDAOImpl extends ServiceImpl<PermissionEventLogMapper, PermissionEventLogDO> implements PermissionEventLogDAO {

}

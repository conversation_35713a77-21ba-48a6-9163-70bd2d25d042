package com.imile.permission.dao.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.WorkCenterDAO;
import com.imile.permission.domain.entity.WorkCenterDO;
import com.imile.permission.domain.workCenter.dto.WorkCenterDTO;
import com.imile.permission.domain.workCenter.query.WorkCenterQuery;
import com.imile.permission.entity.BaseDO;
import com.imile.permission.helper.PermissionPageHelper;
import com.imile.permission.mapper.WorkCenterMapper;
import com.imile.permission.util.OrikaUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作中心表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Service
public class WorkCenterDAOImpl extends ServiceImpl<WorkCenterMapper, WorkCenterDO> implements WorkCenterDAO {

    @Override
    public boolean updateDO(WorkCenterDO workCenterDO) {
        return updateDO(workCenterDO, true);
    }

    @Override
    public boolean updateDO(WorkCenterDO workCenterDO, boolean isCleanLastUpd) {
        if (Objects.isNull(workCenterDO) || Objects.isNull(workCenterDO.getId())) {
            return Boolean.FALSE;
        }
        if (isCleanLastUpd) {
            workCenterDO.cleanLastUpd();
        }
        return lambdaUpdate()
                .eq(WorkCenterDO::getId, workCenterDO.getId())
                .set(WorkCenterDO::getWorkCenterName, workCenterDO.getWorkCenterName())
                .set(WorkCenterDO::getDescription, workCenterDO.getDescription())
                .set(WorkCenterDO::getAuthorizationType, workCenterDO.getAuthorizationType())
                .set(WorkCenterDO::getTemplateModelConfig, workCenterDO.getTemplateModelConfig())
                .set(WorkCenterDO::getTemplateText, workCenterDO.getTemplateText())
                .update(new WorkCenterDO());
    }

    @Override
    public List<WorkCenterDO> select(WorkCenterQuery workCenterQuery) {

        LambdaQueryChainWrapper<WorkCenterDO> eq = lambdaQuery()
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .like(StringUtils.isNotEmpty(workCenterQuery.getWorkCenterName()), WorkCenterDO::getWorkCenterName, workCenterQuery.getWorkCenterName())
                .in(CollectionUtils.isNotEmpty(workCenterQuery.getSystemCodeList()), WorkCenterDO::getSingleSystem, workCenterQuery.getSystemCodeList())
                .in(CollectionUtils.isNotEmpty(workCenterQuery.getIdList()), WorkCenterDO::getId, workCenterQuery.getIdList())
                .eq(Objects.nonNull(workCenterQuery.getAuthorizationType()), WorkCenterDO::getAuthorizationType, workCenterQuery.getAuthorizationType())
                .eq(Objects.nonNull(workCenterQuery.getIsDisable()), WorkCenterDO::getIsDisable, workCenterQuery.getIsDisable())
                .isNotNull(workCenterQuery.isFilterNullNode(), WorkCenterDO::getTemplateModelConfig);
        return eq.list();
    }

    @Override
    public PageInfo<WorkCenterDO> selectPage(WorkCenterQuery workCenterQuery) {
        return PermissionPageHelper.startPage(workCenterQuery)
                .doSelectPageInfo(() -> select(workCenterQuery));
    }

    @Override
    public WorkCenterDO saveDTO(WorkCenterDTO obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        WorkCenterDO workCenter = OrikaUtil.map(obj, WorkCenterDO.class);
        workCenter.setSingleSystem(obj.getSystemCode());
        save(workCenter);
        return workCenter;
    }

    @Override
    public boolean updateDTO(WorkCenterDTO obj) {
        if (Objects.isNull(obj) || Objects.isNull(obj.getId())) {
            return Boolean.FALSE;
        }
        WorkCenterDO workCenter = OrikaUtil.map(obj, WorkCenterDO.class);
        return updateById(workCenter);
    }

    @Override
    public List<WorkCenterDO> selectByWorkCenterName(String workCenterName) {
        if (StringUtils.isBlank(workCenterName)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .eq(WorkCenterDO::getWorkCenterName, workCenterName)
                .list();
    }

    @Override
    public List<WorkCenterDO> selectByWorkCenterName(String workCenterName, Long workCenterId) {
        if (StringUtils.isBlank(workCenterName) || Objects.isNull(workCenterId)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .eq(WorkCenterDO::getWorkCenterName, workCenterName)
                .ne(WorkCenterDO::getId, workCenterId)
                .list();
    }

    @Override
    public List<String> selectNotDisableWorkCenterId(List<Long> workCenterIdList, List<String> workCenterNodeIdList) {
        if (CollectionUtils.isEmpty(workCenterIdList) || CollectionUtils.isEmpty(workCenterNodeIdList)) {
            return Collections.emptyList();
        }
        return getBaseMapper().selectNotDisableWorkCenterId(workCenterIdList, workCenterNodeIdList);
    }

    @Override
    public void deleteById(Long id) {
        lambdaUpdate()
                .eq(WorkCenterDO::getId, id)
                .set(BaseDO::getIsDelete, BusinessConstant.Y)
                .update(new WorkCenterDO());
    }

    @Override
    public List<WorkCenterDO> getSystemFilterWorkCenter(List<String> systemList, List<Long> workCenterIdList) {
        if (CollectionUtils.isEmpty(systemList) || CollectionUtils.isEmpty(workCenterIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .in(WorkCenterDO::getSingleSystem, systemList)
                .in(WorkCenterDO::getId, workCenterIdList)
                .list();
    }

    @Override
    public Map<Long, WorkCenterDO> mapByWorkCenterId(List<Long> workCenterIdList) {
        if (CollectionUtils.isEmpty(workCenterIdList)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .eq(WorkCenterDO::getIsDelete, BusinessConstant.N)
                .in(WorkCenterDO::getId, workCenterIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(WorkCenterDO::getId, Function.identity()));
    }


}

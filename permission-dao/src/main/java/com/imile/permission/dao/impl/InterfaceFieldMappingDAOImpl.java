package com.imile.permission.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.InterfaceFieldMappingDAO;
import com.imile.permission.domain.entity.InterfaceFieldMappingDO;
import com.imile.permission.mapper.InterfaceFieldMappingMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 主数据接口字段映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Service
public class InterfaceFieldMappingDAOImpl extends ServiceImpl<InterfaceFieldMappingMapper, InterfaceFieldMappingDO> implements InterfaceFieldMappingDAO {

    @Override
    public List<InterfaceFieldMappingDO> selectMappingsBySourceTypeCode(String sourceTypeCode){
        if(sourceTypeCode == null) {
            return Lists.newArrayList();
        }
        return lambdaQuery()
                .eq(InterfaceFieldMappingDO::getSourceTypeCode, sourceTypeCode)
                .eq(InterfaceFieldMappingDO::getIsDelete, BusinessConstant.N)
                .orderByAsc(InterfaceFieldMappingDO::getSortNo)
                .list();
    }

    @Override
    public void removeBySourceTypeCode(String sourceTypeCode) {
        if(sourceTypeCode == null) {
            return;
        }
        lambdaUpdate().eq(InterfaceFieldMappingDO::getSourceTypeCode, sourceTypeCode)
                .set(InterfaceFieldMappingDO::getIsDelete, BusinessConstant.Y)
                .update(new InterfaceFieldMappingDO());
    }

    @Override
    public List<String> selectSupportSearchTypeCodes(Collection<String> sourceTypeCodes) {
        if(CollectionUtils.isEmpty(sourceTypeCodes)) {
            return Lists.newArrayList();
        }
        return getBaseMapper().selectSupportSearchTypeCodes(sourceTypeCodes);
    }
}

package com.imile.permission.dao;

import com.imile.permission.domain.entity.PermissionBusinessModuleDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 权限业务模块表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface PermissionBusinessModuleDAO extends IService<PermissionBusinessModuleDO> {

    /**
     * 更新信息
     *
     * @param permissionBusinessModuleDO permissionBusinessModuleDO
     * @return boolean
     */
    boolean updateDO(PermissionBusinessModuleDO permissionBusinessModuleDO);

    /**
     * 更新信息
     *
     * @param permissionBusinessModuleDO permissionBusinessModuleDO
     * @param isCleanLastUpd isCleanLastUpd
     * @return boolean
     */
    boolean updateDO(PermissionBusinessModuleDO permissionBusinessModuleDO, boolean isCleanLastUpd);

    /**
     * 查询系统列表
     * @param systemCode
     * @return
     */
    List<PermissionBusinessModuleDO> selectAll();

    List<String> getSystemBusiness(String systemCode);
}

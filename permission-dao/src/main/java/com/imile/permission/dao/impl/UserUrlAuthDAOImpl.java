package com.imile.permission.dao.impl;

import com.imile.permission.domain.entity.UserUrlAuthDO;
import com.imile.permission.mapper.UserUrlAuthMapper;
import com.imile.permission.dao.UserUrlAuthDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 用户 url 授权表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
public class UserUrlAuthDAOImpl extends ServiceImpl<UserUrlAuthMapper, UserUrlAuthDO> implements UserUrlAuthDAO {
    @Override
    public UserUrlAuthDO getByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }
        List<UserUrlAuthDO> list = lambdaQuery()
                .eq(UserUrlAuthDO::getUserCode, userCode)
                .last("limit 1")
                .list();
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }
}

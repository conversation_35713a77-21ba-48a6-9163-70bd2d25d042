package com.imile.permission.dao;

import com.imile.permission.domain.dataPermission.query.PublishMainDataQuery;
import com.imile.permission.domain.entity.PublishedBusinessBasicDataPermissionConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 业务基础数据权限配置发布表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface PublishedBusinessBasicDataPermissionConfigDAO extends IService<PublishedBusinessBasicDataPermissionConfigDO> {

    PublishedBusinessBasicDataPermissionConfigDO getByTypeCode(String typeCode);

    List<PublishedBusinessBasicDataPermissionConfigDO> selectQuery(PublishMainDataQuery publishMainDataQuery);


    void deleteByTypeCode(String typeCode);

    void updateUseCaseDescriptionBySourceTypeCode(String sourceTypeCode, String useCaseDescription);

    List<PublishedBusinessBasicDataPermissionConfigDO> listByTypeCode(Set<String> multiDimensionRefMainDataCodeSet);
}

package com.imile.permission.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.PermissionSystemDAO;
import com.imile.permission.domain.entity.PermissionSystemDO;
import com.imile.permission.domain.system.param.SystemParam;
import com.imile.permission.entity.BaseDO;
import com.imile.permission.mapper.PermissionSystemMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 权限系统管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Service
public class PermissionSystemDAOImpl extends ServiceImpl<PermissionSystemMapper, PermissionSystemDO> implements PermissionSystemDAO {

    @Override
    public boolean updateDO(PermissionSystemDO permissionSystemDO) {
        return updateDO(permissionSystemDO, true);
    }

    @Override
    public boolean updateDO(PermissionSystemDO permissionSystemDO, boolean isCleanLastUpd) {
        if (Objects.isNull(permissionSystemDO) || Objects.isNull(permissionSystemDO.getId())) {
            return Boolean.FALSE;
        }
        if (isCleanLastUpd) {
            permissionSystemDO.cleanLastUpd();
        }
        return lambdaUpdate()
                .eq(PermissionSystemDO::getId, permissionSystemDO.getId())
                .update(new PermissionSystemDO());
    }

    @Override
    public List<String> getSystemCodeList() {
        return lambdaQuery()
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .list()
                .stream()
                .map(PermissionSystemDO::getSystemCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionSystemDO> getSystemListByParam(SystemParam systemParam) {

        return lambdaQuery().eq(PermissionSystemDO::getIsDelete, BusinessConstant.N)
                .likeRight(Objects.nonNull(systemParam.getSystemCode()),
                        PermissionSystemDO::getSystemCode, systemParam.getSystemCode())
                .eq(systemParam.getAccessType() != null && systemParam.getAccessType() == 1,PermissionSystemDO::getIsAccessApprovalFlow,1)
                .eq(systemParam.getAccessType() != null && systemParam.getAccessType() == 2,PermissionSystemDO::getIsDataAccessFlow,1)
                .eq(systemParam.getAccessType() != null && systemParam.getAccessType() == 3,PermissionSystemDO::getIsWorkCenterAccessFlow,1)
                .list();
    }

    @Override
    public List<PermissionSystemDO> getCooperateSystemCodeList() {
        return lambdaQuery()
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .eq(PermissionSystemDO::getIsCooperate, BusinessConstant.Y)
                .list();
    }

    @Override
    public List<String> getAccessApprovalFlowSystemList(Integer accessType) {
        return lambdaQuery()
                .eq(BaseDO::getIsDelete, BusinessConstant.N)
                .eq(accessType != null && accessType == 1,PermissionSystemDO::getIsAccessApprovalFlow,BusinessConstant.Y)
                .eq(accessType != null && accessType == 2,PermissionSystemDO::getIsDataAccessFlow,BusinessConstant.Y)
                .eq(accessType != null && accessType == 3,PermissionSystemDO::getIsWorkCenterAccessFlow,BusinessConstant.Y)
                .list()
                .stream()
                .map(PermissionSystemDO::getSystemCode)
                .collect(Collectors.toList());
    }


}

package com.imile.permission.dao;

import com.imile.permission.domain.entity.PermissionLogDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.permission.domain.log.PermissionLogQuery;

import java.util.List;

/**
 * <p>
 * 日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
public interface PermissionLogDAO extends IService<PermissionLogDO> {

    /**
     * 更新信息
     *
     * @param permissionLogDO permissionLogDO
     * @return boolean
     */
    boolean updateDO(PermissionLogDO permissionLogDO);

    /**
     * 更新信息
     *
     * @param permissionLogDO permissionLogDO
     * @param isCleanLastUpd isCleanLastUpd
     * @return boolean
     */
    boolean updateDO(PermissionLogDO permissionLogDO, boolean isCleanLastUpd);

    List<PermissionLogDO> selectByQuery(PermissionLogQuery permissionLogQuery);
}

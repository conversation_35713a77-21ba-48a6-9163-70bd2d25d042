package com.imile.permission.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.permission.domain.entity.AuthorityApprovalDataDO;

import java.util.List;

/**
 * <p>
 * 主数据授权申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface AuthorityApprovalDataDAO extends IService<AuthorityApprovalDataDO> {

    List<AuthorityApprovalDataDO> getByAuthorityApprovalId(Long approvalId);

    void deleteByAuthorityApprovalId(Long approvalId);
}

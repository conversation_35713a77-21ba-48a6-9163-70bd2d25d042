package com.imile.permission.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.permission.domain.entity.PermissionSystemDO;
import com.imile.permission.domain.system.param.SystemParam;

import java.util.List;

/**
 * <p>
 * 权限系统管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface PermissionSystemDAO extends IService<PermissionSystemDO> {

    /**
     * 更新信息
     *
     * @param permissionSystemDO permissionSystemDO
     * @return boolean
     */
    boolean updateDO(PermissionSystemDO permissionSystemDO);

    /**
     * 更新信息
     *
     * @param permissionSystemDO permissionSystemDO
     * @param isCleanLastUpd isCleanLastUpd
     * @return boolean
     */
    boolean updateDO(PermissionSystemDO permissionSystemDO, boolean isCleanLastUpd);

    List<String> getSystemCodeList();

    List<PermissionSystemDO> getSystemListByParam(SystemParam systemParam);

    /**
     * 获取已经接入的系统code
     * @return
     */
    List<PermissionSystemDO> getCooperateSystemCodeList();

    List<String> getAccessApprovalFlowSystemList(Integer accessType);

}

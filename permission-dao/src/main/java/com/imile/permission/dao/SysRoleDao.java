package com.imile.permission.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.report.SystemCountDTO;
import com.imile.permission.domain.operatorSystem.OperatorSystemRoleQueryDTO;
import com.imile.permission.domain.role.dto.SysRoleDTO;
import com.imile.permission.domain.role.query.SysRoleQuery;
import com.imile.permission.domain.supplier.CspSupplierAdminRoleQuery;
import com.imile.permission.domain.supplier.DriverRoleQuery;
import com.imile.permission.domain.supplier.OutletRoleQuery;
import com.imile.permission.domain.supplier.SupplierRoleQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SysRoleDao extends IService<SysRoleDO> {


    /**
     * 删除角色
     *
     * @param id id
     * @return boolean
     */
    boolean delete(Long id);

    /**
     * 查询角色中文名个数
     *
     * @param roleNameCn roleNameCn
     * @return Integer
     */
    Integer selectRoleNameCnCount(String roleNameCn);


    /**
     * 角色分页查询
     *
     * @param query query
     * @return List<SysRoleDO>
     */
    List<SysRoleDO> select(SysRoleQuery query);

    /**
     * 角色名称统计
     *
     * @param roleNameCn roleNameCn
     * @return Integer
     */
    Integer selectRoleNameCount(String roleNameCn);

    /**
     * 角色名称统计
     *
     * @param roleNameCn roleNameCn
     * @param id         id
     * @return Integer
     */
    Integer selectRoleNameCount(String roleNameCn, Long id);

    Integer selectRoleNameEnCount(String roleNameEn, Long id);

    /**
     * 更新角色信息
     *
     * @param sysRoleDO sysRoleDO
     * @return boolean
     */
    boolean updateDO(SysRoleDO sysRoleDO);

    /**
     * 角色分页查询
     *
     * @param query query
     * @return PageInfo<SysRoleDO>
     */
    PageInfo<SysRoleDO> selectPage(SysRoleQuery query);


    /**
     * 新增系统角色
     *
     * @param sysRoleDTO
     * @return
     */
    boolean saveDTO(SysRoleDTO sysRoleDTO);

    /**
     * 修改系统角色
     *
     * @param sysRoleDTO
     * @return
     */
    boolean updateDTO(SysRoleDTO sysRoleDTO);

    /**
     * 获取 角色ID 映射
     *
     * @param roleIdList roleIdList
     * @return Map<Long, SysRoleDO>
     */
    Map<Long, SysRoleDO> mapByRoleId(List<Long> roleIdList);

    /**
     * 查询 ID 列表
     *
     * @param roleIdList roleIdList
     * @return List<SysRoleDO>
     */
    List<SysRoleDO> selectById(List<Long> roleIdList);

    /**
     * rpc 变更角色信息
     *
     * @param role role
     */
    void rpcUpdateDO(SysRoleDO role);

    /**
     * 查询未禁用的角色ID
     *
     * @param roleIdList roleIdList
     * @return List<Long>
     */
    List<Long> selectNotDisableRoleId(List<Long> roleIdList);

    /**
     * 更新角色是否禁用状态
     *
     * @param roleId    roleId
     * @param isDisable isDisable
     */
    void updateRoleDisable(Long roleId, Integer isDisable);

    SysRoleDO getClientDefaultRole();


    SysRoleDO getChildRole(Long parentRoleId);

    List<SysRoleDO> getParentRole();

    List<SysRoleDO> getChildRoleByParentIdList(List<Long> roleIdList);

    List<SysRoleDO> listChildRoleByParentId(List<Long> roleIdList);

    List<SysRoleDO> listBySystemRoleId(List<String> systemList, List<Long> roleIdList);

    List<Long> getExistRoleIds(Collection<Long> roleIdList);

    List<SystemCountDTO> notInRole(List<Long> roleIdList, List<String> targetSystems);

    List<SysRoleDO> getRolesPaginated(OperatorSystemRoleQueryDTO operatorSystemRoleQuery);

    List<SysRoleDO> listByRoleIdAndOrgId(List<Long> roleIdList, Long orgId);

    Boolean checkCSPSupplierAdminUnique(List<String> systemCodeList, List<String> roleCountryList, String adminOrg);

    List<SysRoleDO> getChildRoleList(Long roleId);

    PageInfo<SysRoleDO> listCspSupplierAdminRolePage(CspSupplierAdminRoleQuery cspSupplierAdminRoleQuery);

    boolean checkDriverUnique(List<String> systemCodeList, List<String> roleCountryList, String functional);

    PageInfo<SysRoleDO> listDriverRolePage(DriverRoleQuery driverRoleQuery);

    List<SysRoleDO> getCspAdminRoleRoleByRoleId(List<Long> roleIdByUserCode);

    PageInfo<SysRoleDO> listOutletRolePage(OutletRoleQuery outletRoleQuery);

    boolean checkSupplierUnique(List<String> systemCodeList, String supplierType);

    boolean checkCountrySupplierUnique(List<String> systemCodeList, List<String> roleCountryList, String supplierType);

    PageInfo<SysRoleDO> listSupplierRolePage(SupplierRoleQuery supplierRoleQuery);

    List<SysRoleDO> getCspAdminRoleRole(String country, String adminOrg);

    List<SysRoleDO> getDriverRoleRole(String country, List<String> functionalList);

    List<SysRoleDO> getDriverRoleByRoleId(List<Long> roleIdByUserCode);

    List<SysRoleDO> getSupplierRole(List<String> supplierTypeList);

    List<SysRoleDO> getCountrySupplierRole(List<String> countryList, List<String> supplierTypeList);

    List<SysRoleDO> getCountrySupplierRoleByRoleId(List<Long> roleIdByUserCode);

    List<SysRoleDO> getSupplierRoleByRoleId(List<Long> roleIdByUserCode);

    List<SysRoleDO> getCSPSupplierAdmin(List<String> systemCodeList, List<String> roleCountryList, String adminOrg);

    List<SysRoleDO> getDriverRole(List<String> systemCodeList, List<String> roleCountryList, String functional);

    List<SysRoleDO> getSupplierRole(List<String> systemCodeList, String supplierType);

    List<SysRoleDO> getCountrySupplierRole(List<String> systemCodeList, List<String> roleCountryList, String supplierType);
}

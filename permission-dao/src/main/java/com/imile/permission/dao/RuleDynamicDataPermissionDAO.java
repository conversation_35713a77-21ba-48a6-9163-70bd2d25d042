package com.imile.permission.dao;

import com.imile.permission.domain.entity.RuleDynamicDataPermissionDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规则动态数据权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
public interface RuleDynamicDataPermissionDAO extends IService<RuleDynamicDataPermissionDO> {

    /**
     * 更新信息
     *
     * @param ruleDynamicDataPermissionDO ruleDynamicDataPermissionDO
     * @return boolean
     */
    boolean updateDO(RuleDynamicDataPermissionDO ruleDynamicDataPermissionDO);

    /**
     * 更新信息
     *
     * @param ruleDynamicDataPermissionDO ruleDynamicDataPermissionDO
     * @param isCleanLastUpd isCleanLastUpd
     * @return boolean
     */
    boolean updateDO(RuleDynamicDataPermissionDO ruleDynamicDataPermissionDO, boolean isCleanLastUpd);

    List<RuleDynamicDataPermissionDO> listDynamicDataByUserAndTypeCode(String userCode, List<String> filterTypeCode);

    void deletePermissionAuthorizationRuleByUserCode(String userCode);

    List<RuleDynamicDataPermissionDO> listDynamicDataByUserCode(String userCode);
}

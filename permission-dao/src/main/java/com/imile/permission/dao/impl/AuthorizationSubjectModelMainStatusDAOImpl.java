package com.imile.permission.dao.impl;

import com.imile.permission.domain.entity.AuthorizationSubjectModelMainStatusDO;
import com.imile.permission.mapper.AuthorizationSubjectModelMainStatusMapper;
import com.imile.permission.dao.AuthorizationSubjectModelMainStatusDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 授权主体模型-主体状态表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
public class AuthorizationSubjectModelMainStatusDAOImpl extends ServiceImpl<AuthorizationSubjectModelMainStatusMapper, AuthorizationSubjectModelMainStatusDO> implements AuthorizationSubjectModelMainStatusDAO {

    @Override
    public boolean updateDO(AuthorizationSubjectModelMainStatusDO authorizationSubjectModelMainStatusDO) {
        return updateDO(authorizationSubjectModelMainStatusDO, true);
    }

    @Override
    public boolean updateDO(AuthorizationSubjectModelMainStatusDO authorizationSubjectModelMainStatusDO, boolean isCleanLastUpd) {
        if (Objects.isNull(authorizationSubjectModelMainStatusDO) || Objects.isNull(authorizationSubjectModelMainStatusDO.getId())) {
            return Boolean.FALSE;
        }
        if (isCleanLastUpd) {
            authorizationSubjectModelMainStatusDO.cleanLastUpd();
        }
        return lambdaUpdate()
                .eq(AuthorizationSubjectModelMainStatusDO::getId, authorizationSubjectModelMainStatusDO.getId())
                .update(new AuthorizationSubjectModelMainStatusDO());
    }

}

package com.imile.permission.dao;

import com.imile.permission.domain.dataPermission.dto.TypeCodeDataCodeDTO;
import com.imile.permission.domain.entity.UserDynamicDataPermissionDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 动态数据权限授权表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface UserDynamicDataPermissionDAO extends IService<UserDynamicDataPermissionDO> {

    /**
     * 更新信息
     *
     * @param userDynamicDataPermissionDO userDynamicDataPermissionDO
     * @return boolean
     */
    boolean updateDO(UserDynamicDataPermissionDO userDynamicDataPermissionDO);

    /**
     * 更新信息
     *
     * @param userDynamicDataPermissionDO userDynamicDataPermissionDO
     * @param isCleanLastUpd isCleanLastUpd
     * @return boolean
     */
    boolean updateDO(UserDynamicDataPermissionDO userDynamicDataPermissionDO, boolean isCleanLastUpd);

    void deleteByUserCode(String userCode);

    void deleteByUserCodeAndTypeCode(String userCode, String typeCode);

    List<UserDynamicDataPermissionDO> getByUserCode(String userCode);

    void deleteByUserCodeAndTypeCodeAndDataCode(String userCode, String typeCode, String dataCode);

    List<UserDynamicDataPermissionDO> selectDataContentByUserCodeAndTypeCodeAndDataCode(String userCode, String typeCode, String dataCode);

    void deleteByUserCodeAndTypeCodeDataCodeDTO(String userCode, List<TypeCodeDataCodeDTO> typeCodeDataCodeDTOS);

}

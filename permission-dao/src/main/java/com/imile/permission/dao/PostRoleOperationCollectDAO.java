package com.imile.permission.dao;

import com.imile.permission.domain.entity.PostRoleOperationCollectDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.permission.domain.relation.dto.PostRoleDTO;
import com.imile.permission.domain.relation.dto.PostRoleOperationCollectionDTO;
import com.imile.permission.domain.relation.query.PostRolePageQuery;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 岗位角色操作汇总表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PostRoleOperationCollectDAO extends IService<PostRoleOperationCollectDO> {

    /**
     * 更新信息
     *
     * @param postRoleOperationCollectDO postRoleOperationCollectDO
     * @return boolean
     */
    boolean updateDO(PostRoleOperationCollectDO postRoleOperationCollectDO);

    /**
     * 更新信息
     *
     * @param postRoleOperationCollectDO postRoleOperationCollectDO
     * @param isCleanLastUpd isCleanLastUpd
     * @return boolean
     */
    boolean updateDO(PostRoleOperationCollectDO postRoleOperationCollectDO, boolean isCleanLastUpd);

    List<PostRoleOperationCollectionDTO> getRoleCount(Long postId);

    List<PostRoleDTO> getRolePage(PostRolePageQuery pageQuery);

    List<PostRoleOperationCollectDO> getByPostAndRole(Long postId, List<Long> checkIdList);

    void updateOperationType(Long postId, Collection<Long> roleIdList, String operationType, LocalDateTime now);
}

package com.imile.permission.common;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Collection;

public interface PermissionBaseMapper<T> extends BaseMapper<T> {

    /**
     * 批量更新插入 仅适用于mysql
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    Integer replaceIntoBatchSomeColumn(Collection<T> entityList);

    /**
     * 批量插入 仅适用于mysql
     * 解决MybatisPlus无法使用批量新增问题，但仅支持MYSQL（MybatisPlus中虽然使用savaBatch可以实现批量插入，但是使用for循环，效率比较低）
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    Integer insertBatchSomeColumn(Collection<T> entityList);
}

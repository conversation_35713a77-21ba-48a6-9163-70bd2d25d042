<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.SysRoleMapper">

    <insert id="insertRoleList">
        insert into sys_role (id, role_name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.roleName})
        </foreach>


    </insert>

    <select id="getExistRoleIds" resultType="java.lang.Long">
        select id from sys_role where is_delete = 0 and id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="notInRole" resultType="com.imile.permission.domain.report.SystemCountDTO">
        select multiple_system as `system`, count(id) as count
        from sys_role
        where id not in
        <foreach collection="roleIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and is_delete = 0
        and multiple_system in
        <foreach collection="targetSystems" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by multiple_system
    </select>
    <select id="listCspSupplierAdminRole" resultType="com.imile.permission.domain.entity.SysRoleDO">
        SELECT * FROM sys_role
        WHERE 1=1
        and is_delete = 0
        and role_type = 3
        <if test="query.roleName != null and query.roleName != ''">
            AND (role_name LIKE CONCAT('%', #{query.roleName}, '%')
            OR role_name_en LIKE CONCAT('%', #{query.roleName}, '%'))
        </if>
        <if test="query.country != null and query.country != ''">
            AND role_country = CONCAT('["', #{query.country}, '"]')
        </if>
        <if test="query.adminOrg != null and query.adminOrg != ''">
            AND admin_org = #{query.adminOrg}
        </if>

    </select>
    <select id="listDriverRole" resultType="com.imile.permission.domain.entity.SysRoleDO">
        SELECT * FROM sys_role
        WHERE 1=1
        and is_delete = 0
        and role_type = 4
        <if test="query.roleName != null and query.roleName != ''">
            AND (role_name LIKE CONCAT('%', #{query.roleName}, '%')
            OR role_name_en LIKE CONCAT('%', #{query.roleName}, '%'))
        </if>
        <if test="query.country != null and query.country != ''">
            AND role_country = CONCAT('["', #{query.country}, '"]')
        </if>
        <if test="query.functional != null and query.functional != ''">
            AND functional = #{query.functional}
        </if>
    </select>
    <select id="listOutletRole" resultType="com.imile.permission.domain.entity.SysRoleDO">
        SELECT * FROM sys_role
        WHERE 1=1
        and is_delete = 0
        and role_type = 5
        <if test="query.roleName != null and query.roleName != ''">
            AND (role_name LIKE CONCAT('%', #{query.roleName}, '%')
            OR role_name_en LIKE CONCAT('%', #{query.roleName}, '%'))
        </if>
        <if test="query.country != null and query.country != ''">
            AND role_country = CONCAT('["', #{query.country}, '"]')
        </if>
        <if test="query.ownStationCode != null and query.ownStationCode != ''">
            AND own_station_code = #{query.ownStationCode}
        </if>
    </select>
    <select id="listSupplierRole" resultType="com.imile.permission.domain.entity.SysRoleDO">
        SELECT * FROM sys_role
        WHERE 1=1
        and is_delete = 0
        and role_type in (6,7)
        <if test="query.roleName != null and query.roleName != ''">
            AND (role_name LIKE CONCAT('%', #{query.roleName}, '%')
            OR role_name_en LIKE CONCAT('%', #{query.roleName}, '%'))
        </if>
        <if test="query.country != null and query.country != ''">
            AND role_country = CONCAT('["', #{query.country}, '"]')
        </if>
        <if test="query.supplierType != null and query.supplierType != ''">
            AND supplier_type = #{query.supplierType}
        </if>
        order by last_upd_date desc

    </select>


</mapper>

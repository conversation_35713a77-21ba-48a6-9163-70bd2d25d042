<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.XxlJobUserRefreshMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.XxlJobUserRefreshDO">
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_code" property="userCode" />
        <result column="refresh_date" property="refreshDate" />
        <result column="last_call_data" property="lastCallData" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_code, refresh_date, last_call_data
    </sql>
    <insert id="customSaveOrUpdate">
        INSERT INTO xxl_job_user_refresh (user_code,
                               is_delete,
                               record_version,
                               create_date,
                               create_user_code,
                               create_user_name,
                               last_upd_date,
                               last_upd_user_code,
                               last_upd_user_name,
                               refresh_date,
                               last_call_data)
        VALUES (#{xxlJobUserRefreshDO.userCode, jdbcType=VARCHAR},
                #{xxlJobUserRefreshDO.isDelete, jdbcType=TINYINT},
                #{xxlJobUserRefreshDO.recordVersion, jdbcType=BIGINT},
                #{xxlJobUserRefreshDO.createDate, jdbcType=TIMESTAMP},
                #{xxlJobUserRefreshDO.createUserCode, jdbcType=VARCHAR},
                #{xxlJobUserRefreshDO.createUserName, jdbcType=VARCHAR},
                #{xxlJobUserRefreshDO.lastUpdDate, jdbcType=TIMESTAMP},
                #{xxlJobUserRefreshDO.lastUpdUserCode, jdbcType=VARCHAR},
                #{xxlJobUserRefreshDO.lastUpdUserName, jdbcType=VARCHAR},
                #{xxlJobUserRefreshDO.refreshDate, jdbcType=TIMESTAMP},
                #{xxlJobUserRefreshDO.lastCallData, jdbcType=TIMESTAMP}) ON DUPLICATE KEY
        UPDATE
            last_upd_date = VALUES(last_upd_date),
            last_upd_user_code = VALUES(last_upd_user_code),
            last_upd_user_name = VALUES(last_upd_user_name),
            last_call_data = VALUES (last_call_data);
    </insert>

</mapper>

package com.imile.permission.domain.relation.query;

import com.imile.permission.domain.common.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SysPostRoleRelationQuery extends ResourceQuery {

    /**
     * id
     */
    private Long id;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 角色 ID
     */
    private Long roleId;

    /**
     * postIdList
     */
    private List<Long> postIdList;

    /**
     * 配置状态
     */
    private Boolean configurationStatus;

}

package com.imile.permission.domain.dataPermission.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/29
 */
@Data
public class MenuPermissionDTO implements Serializable {
    /**
     * 系统平台名称
     */
    private String systemPlatform;

    /**
     * 菜单 ID
     */
    private Long menuId;

    /**
     * 全选菜单集合
     */
    private List<Long> menuIdList;

    /**
     * 半选菜单集合
     */
    private List<Long> partiallyMenuIdList;
}

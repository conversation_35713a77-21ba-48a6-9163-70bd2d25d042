package com.imile.permission.domain.mq;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/8
 */
@Data
public class BusinessRoleUpdateEvent implements Serializable {
    private String eventType;
    private CspSupplierAdminRoleUpdateEvent cspSupplierAdminRoleUpdateEvent;
    private DriverRoleUpdateEvent driverRoleUpdateEvent;
    private SupplierAccountUpdateEvent supplierAccountUpdateEvent;

    @Data
    public static class CspSupplierAdminRoleUpdateEvent implements Serializable {
        private String userCode;
        private String country;
        private String adminOrg;
    }


    @Data
    public static class DriverRoleUpdateEvent implements Serializable {
        private String userCode;
        private String country;
        private List<String> functionalList;


    }

    @Data
    public static class SupplierAccountUpdateEvent implements Serializable {

        private String supplierAccount;
        private List<String> countryList;
        private List<String> supplierTypeList;

    }
}

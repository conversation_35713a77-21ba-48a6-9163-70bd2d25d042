package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/24
 */
@Data
public class TypeCodesDataPermissionParam {

    /**
     * 系统编码（目前传系统英文名）
     */
    @NotBlank(message = "systemCode can not be null")
    private String systemCode;

    /**
     * 数据类型编码
     */
    private List<String> typeCodeList;

    /**
     * 查询场景的类型
     * 1-角色；2-员工；
     */
    private Integer sceneType = 1;
}

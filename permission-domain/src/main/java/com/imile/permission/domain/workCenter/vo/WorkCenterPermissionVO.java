package com.imile.permission.domain.workCenter.vo;

import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/17
 */
@Data
public class WorkCenterPermissionVO {

    /**
     * 工作中心ID
     */
    private Long workCenterId;

    /**
     * 节点资源ID
     */
    private String sourceNodeId;

    /**
     * 中文节点名称
     */
    private String nodeNameCn;


    /**
     * 英文节点名称
     */
    private String nodeNameEn;

    /**
     * 英文节点名称
     */
    private String nodeName;

    /**
     * 菜单权限
     */
    private List<MenuPermissionDTO> menuPermissionDTOList;

    /**
     * 基础数据权限
     */
    private List<SystemDataPermissionVO> baseDataPermissionDTO;

    /**
     * 主数据权限
     */
    private List<SystemDataPermissionVO> mainDataPermissionDTO;
}

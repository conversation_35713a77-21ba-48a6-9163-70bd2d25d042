package com.imile.permission.domain.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 角色申请审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
public class ClientRoleRecordDTO {

    /**
     * 用户编码
     */
    private String clientCode;

    /**
     * 角色编号-父
     */
    private Long roleId;

    /**
     * 关联系统
     */
    private String multipleSystem;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色英文名称
     */
    private String roleNameEn;

    /**
     * 英文描述
     */
    private String descriptionEn;

    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

}

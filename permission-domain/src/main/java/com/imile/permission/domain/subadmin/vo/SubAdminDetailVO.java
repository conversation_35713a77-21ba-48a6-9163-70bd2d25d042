package com.imile.permission.domain.subadmin.vo;

import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.subadmin.param.SubAdminSystemParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/12
 */
@Data
public class SubAdminDetailVO {

    /**
     * 账号（员工编码）
     */
    private String userCode;

    /**
     * 系统列表
     */
    private List<String> systemCodeList;

    /**
     * 系统授权
     */
    private List<SubAdminSystemParam> subAdminSystemParamList;

    /**
     * 菜单权限
     */
    private MenuPermissionDTO menuPermissionDTO;
}

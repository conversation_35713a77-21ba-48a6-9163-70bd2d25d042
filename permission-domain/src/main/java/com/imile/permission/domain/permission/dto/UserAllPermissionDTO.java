package com.imile.permission.domain.permission.dto;

import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 基于casbin表结构
 * 用户权限模型
 * <AUTHOR>
 * @since 2024/7/31
 */
@Data
public class UserAllPermissionDTO implements Serializable {

    /**
     * 用户code
     */
    private String userCode;

    /**
     * 员工角色
     */
    private List<Long> roleList;

    /**
     * 员工岗位
     */
    private List<Long> postIdList;

    /**
     * 菜单权限
     * 包括流程和直接授权的菜单
     */
    private List<MenuPermissionDTO> menuPermissionDTOList;

    /**
     * 数据权限
     * 包括流程和直接授权的数据权限
     */
    private List<DataPermissionDTO> dataPermissionDTOList;
}

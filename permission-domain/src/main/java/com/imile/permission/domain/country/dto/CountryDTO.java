package com.imile.permission.domain.country.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version Id: CountryConfigDTO.java, v 0.1 2020/11/26 21:15 tony Exp $
 * @Description 国家配置信息
 */
@Data
public class CountryDTO implements Serializable {
    private static final long serialVersionUID = 4202000642212282937L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 企业id(前端不需要传)
     */
    private Long orgId;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国旗图片路径
     */
    private String picPath;

    /**
     * 国家简码
     */
    private String countryShort;

    /**
     * 是否启用系统时区 YES-是 NO-否
     */
    private String enableStatus;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 国际区号
     */
    private String countryCallingCode;

    /**
     * 预约当日截止时间
     */
    @Deprecated
    private String scheduleEndTime;

    /**
     * 每周休息日
     */
    private String restWeekDay;

    /**
     * 币种
     */
    private String currency;

    /**
     * 关税起征点
     */
    private BigDecimal dutyTaxation;

    /**
     * 汇率类型 详见数据字典rateType
     */
    private String rateType;

    /**
     * 固定费率
     */
    private BigDecimal flatRate;

    /**
     * 内部申报方式 详见数据字典declareType
     */
    private String declareType;


    /**
     * 申报范围起始值
     */
    private BigDecimal startDeclareValue;

    /**
     * 申报范围终止值
     */
    private BigDecimal endDeclareValue;

    /**
     * 客服电话
     */
    private String csTel;

    /**
     * what app
     */
    private String whatApp;

    /**
     * 开通时间
     */
    private Date createDate;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 快递标准产品
     */
    private String standardProduct;
    /**
     * 快递订单首重KG
     */
    private Integer firstWeight;
    /**
     * 快递最大重量限制KG
     */
    private Integer maxWeight;
    /**
     * 快递标准面单样式
     */
    private String standardExpressBill;
    /**
     * 快递退回面单样式
     */
    private String returnExpressBill;

    /**
     * 寄件App是否需要自动审核
     */
    private Integer expressAudit;

    /**
     * 销售电话
     */
    private String salesPhone;
    /**
     * 销售负责人邮箱
     */
    private String salesEmail;

    /**
     * 支持银行
     */
    private String supportBank;

    /**
     * 手机号长度
     */
    private Integer phoneLength;

    /**
     * 寄件APP语言
     */
    private String expressAppLanguage;
    /**
     * 国家名称
     */
    private String countryTitle;

    /**
     * 国家英文名称
     */
    private String countryTitleEn;

}

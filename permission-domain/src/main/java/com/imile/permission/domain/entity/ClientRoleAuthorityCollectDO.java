package com.imile.permission.domain.entity;

import com.imile.permission.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商家角色授权汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("client_role_authority_collect")
public class ClientRoleAuthorityCollectDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商家编码
     */
    @TableField("client_code")
    private String clientCode;

    /**
     * 角色编号
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 申请日期
     */
    @TableField("application_date")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @TableField("expiration_date")
    private LocalDateTime expirationDate;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

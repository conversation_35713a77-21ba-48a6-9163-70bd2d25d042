package com.imile.permission.domain.dataPermission.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
public class DimensionDataPermissionRuleDTO {
    /**
     * 数据字典类型
     */
    private String typeCode;

    /**
     * 数据字典名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String typeName;

    /**
     * 是否全选
     */
    private Integer selectAll = 0;

    /**
     * 字典数据
     */
    private List<String> dataCodeList;

    /**
     * 字典数据-扩展数据
     */
    private List<DataCodeExtensionTagDTO> dataExtensionTagList;

}

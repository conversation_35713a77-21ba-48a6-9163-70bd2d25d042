package com.imile.permission.domain.user.dto;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/4
 */
@Data
public class UserInfoDetailQuery extends BaseQuery {

    private String userCodeOrName;
    private Long postId;
    private String workStatus;
    private List<String> workStatusList;
    private List<String> employeeTypes;

    /**
     * 人员编码列表（一般回显时使用，回显时仅传该字段即可）
     */
    private List<String> userCodeList;


    /**
     * 账号状态
     */
    private String status;

}

package com.imile.permission.domain.workCenter.query;

import com.imile.permission.domain.common.ResourceQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
public class WorkCenterNodeQuery extends ResourceQuery {

    /**
     * 主键ID
     */
    private Long id;


    /**
     * 主键ID 集合
     */
    private List<Long> idList;

    /**
     * sourceNodeId 集合
     */
    private String sourceNodeId;

    /**
     * sourceNodeId 集合
     */
    private List<String> sourceNodeIdList;

    /**
     * 工作中心ID
     */
    private Long workCenterId;


}

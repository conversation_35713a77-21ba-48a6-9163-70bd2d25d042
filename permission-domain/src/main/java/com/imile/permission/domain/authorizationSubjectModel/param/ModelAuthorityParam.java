package com.imile.permission.domain.authorizationSubjectModel.param;

import com.imile.permission.domain.authorizationSubjectModel.dto.ModelAuthorityDataDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/25
 */
@Data
public class ModelAuthorityParam {

    /**
     * 授权主体模型类型编码
     */
    private String subjectModelCode;
    /**
     * 主体编码
     */
    private String mainId;

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据值
     */
    private List<ModelAuthorityDataDTO> dataList;

}

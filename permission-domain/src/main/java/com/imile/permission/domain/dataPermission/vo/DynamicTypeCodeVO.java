package com.imile.permission.domain.dataPermission.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Data
public class DynamicTypeCodeVO {


    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;

    @JsonIgnore
    private Date createDate;


    /**
     * 是否关联主数据（0:不是 1:是）
     */
    private Integer isAssociatedMainData;

    /**
     * 主数据类型 type_code
     */
    private String associatedMainDataTypeCode;

    /**
     * 归属系统
     */
    private String singleSystem;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 数据内容
     */
    private List<DynamicDataCodeVO> dataList;

    /**
     * 数据维度
     */
    private List<RuleDataVO> ruleDataList;

}

package com.imile.permission.domain.relation.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 角色申请审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
public class PostRoleVO {

    /**
     * 角色编号
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 操作时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operationTime;

    /**
     * 适用国家
     */
    private List<String> countryList;

    /**
     * 关联系统
     */
    private List<String> systemCodeList;

    /**
     * 敏感等级： 1.敏感，0.不敏感
     */
    private Integer sensitiveLevel;


}

package com.imile.permission.domain.dataPermission.vo;

import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.util.RegexUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@Data
public class SpecificRuleConfigVO {
    /**
     * 关联类型（主数据、函数数据）
     */
    private String relationType;
    /**
     * 关联数据类型
     */
    private String relationTypeCode;
    /**
     * 规则编码
     */
    private String ruleCode;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则数据类型
     */
    private String ruleType;

    /**
     * 值类型
     */
    private String valueType;

    /**
     * 值列表
     */
    private String valueContent;

    /**
     * 操作符
     */
    private String op;

    /**
     * 操作符描述
     */
    private String opDesc;


    public void check() {
        BusinessLogicException.checkTrue(StringUtils.isBlank(ruleCode), PermissionErrorCodeEnums.RULE_CODE_IS_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(ruleName), PermissionErrorCodeEnums.RULE_NAME_IS_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(ruleType), PermissionErrorCodeEnums.RULE_TYPE_IS_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(valueType), PermissionErrorCodeEnums.VALUE_TYPE_IS_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(valueContent), PermissionErrorCodeEnums.VALUE_CONTENT_IS_NULL);

        if (ruleType.equals("number")) {
            BusinessLogicException.checkTrue(!Arrays.asList("eq", "gt", "ge", "lt", "le", "in").contains(op), PermissionErrorCodeEnums.OP_NOT_SUPPORT);
            String[] split = valueContent.split(",");
            for (String value : split) {
                BusinessLogicException.checkTrue(!RegexUtil.isNumber(value), PermissionErrorCodeEnums.NUMBER_DATA_ERROR);

            }


        } else if (ruleType.equals("string")) {
            BusinessLogicException.checkTrue(!Arrays.asList("eq", "in").contains(op), PermissionErrorCodeEnums.OP_NOT_SUPPORT);
        } else {
            BusinessLogicException.checkTrue(true, PermissionErrorCodeEnums.RULE_TYPE_NOT_EXIST);
        }

    }


}

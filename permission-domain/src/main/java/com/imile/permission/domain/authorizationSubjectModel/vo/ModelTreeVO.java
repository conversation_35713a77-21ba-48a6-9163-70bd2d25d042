package com.imile.permission.domain.authorizationSubjectModel.vo;

import com.imile.permission.domain.dataPermission.vo.KeyLabelValueVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/25
 */
@Data
public class ModelTreeVO {
    /**
     * 主体编码
     */
    private String mainId;

    /**
     * 节点ID
     */
    private String nodeId;

    // /**
    //  * 父ID
    //  */
    // private String parentNodeId;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     */
    private String status;
    /**
     * 数据与标签
     */
    private List<KeyLabelValueVO> keyLabelValueList;

    private List<ModelTreeVO> children;
}

package com.imile.permission.domain.common;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/5 17:14
 * @info
 */
@Data
@Accessors(chain = true)
public class OcInfoPayloadDTO implements Serializable {
    private static final long serialVersionUID = -2590383004141703693L;

    private Long id;

    /**
     * 加盟商企业id
     */
    private Long vendorOrgId;
    /**
     * 网点编号
     */
    private String ocCode;
    /**
     * 网点名称
     */
    private String ocName;
    /**
     * 网点简称
     */
    private String ocShortName;
    /**
     * 父网点Id
     */
    private Long parentOcId;
    /**
     * 父网点编码
     */
    private String parentOcCode;
    /**
     * 网点类型
     */
    private String ocType;
    /**
     * 网点级数
     */
    private Integer ocLevel;
    /**
     * 父网点Id
     */
    private Integer parentLevelId;
    /**
     * 区域中心编码
     */
    private String ocCenterCode;
    /**
     * 地址
     */
    private String address;
    /**
     * 区域
     */
    private String region;
    /**
     * 城市
     */
    private String city;
    /**
     * 省份
     */
    private String province;
    /**
     * 国家
     */
    private String country;
    /**
     * 邮编
     */
    @Deprecated
    private String postCode;
    /**
     * 电话
     */
    private String tel;
    /**
     * 手机
     */
    private String cell;
    /**
     * 传真
     */
    private String fax;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 联系人
     */
    private String attentionTo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private String status;
    /**
     * 负责人编码(直营网点必填)
     */
    private String ocPrincipalCode;
    /**
     * 负责人(加盟网点必填)
     */
    private String ocPrincipal;
    /**
     * 负责人电话
     */
    private String ocPrincipalCall;
    /**
     * 负责人邮箱
     */
    private String ocPrincipalMail;
    /**
     * 经理
     */
    private String ocManager;
    /**
     * 经理电话
     */
    private String ocManagerCall;
    /**
     * 运营负责人
     */
    private String ocOperationManager;
    /**
     * 运营负责人编码
     */
    private String ocOperationManagerCode;
    /**
     * 运营负责人电话
     */
    private String ocOperationManagerCall;
    /**
     * 运营负责人邮箱
     */
    private String ocOperationManagerEmail;
    /**
     * 电话区号
     */
    private String phoneAreaCode;
    /**
     * 客服人员
     */
    private String customerService;
    /**
     * 客服电话
     */
    private String consumerHotline;
    /**
     * 是否删除 0为不删除,1为已删除
     */
    private Integer isDelete;
    /**
     * 版本编码
     */
    private Long recordVersion;
    /**
     * 新增时间
     */
    private Date createDate;
    /**
     * 新增用户编码
     */
    private String createUserCode;
    /**
     * 新增用户名
     */
    private String createUserName;
    /**
     * 最后修改时间
     */
    private Date lastUpdDate;
    /**
     * 最后修改用户编码
     */
    private String lastUpdUserCode;
    /**
     * 最后修改用户名
     */
    private String lastUpdUserName;
    /**
     * 经营类别
     */
    private String businessType;
    /**
     * 所属区域
     */
    private String businessRegion;
    /**
     * 所属区域编号
     */
    private String businessRegionCode;
    /**
     * 默认配载网点Id
     */
    private Long defaultOcId;
    /**
     * 经度
     */
    private BigDecimal longitude;
    /**
     * 纬度
     */
    private BigDecimal latitude;
    /**
     * 投诉电话
     */
    private String complaintCall;
    /**
     * 内部电话
     */
    private String internalTelephone;
    /**
     * 默认配载网点编码
     */
    private String defaultOcCode;
    /**
     * 网点经理邮箱
     */
    private String ocManagerEmail;
    /**
     * 客服人员编码
     */
    private String customerServiceCode;
    /**
     * 经营类别
     */
    private String businessCategory;

    /**
     * 提示音名称
     */
    private String voiceName;

    /**
     * 提示音链接
     */
    private String voiceUrl;

    /**
     * 社区
     */
    private String suburb;

    /**
     * 街道
     */
    private String street;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 外部号码（街道号）
     */
    private String externalNo;

    /**
     * 内部号码（门牌号码）
     */
    private String internalNo;

    /**
     * ppd订单签收验证方式
     * ID&签名 网点选中的网点司机APP签收PPD订单页面为现有ID与签名都必填
     * ID/签名 网点选中的网点司机APP签收PPD订单页面必须上传ID照和签名中的一个
     * OTP验证 选中的网点，司机APP签收PPD订单页面不再显示上传id照和签名选项
     */
    private String ppdOrderReceiptMethod;

    /**
     * 是否仅支持扫描面单后签收，0:支持手动输入单号和扫描单号 ；1:只支持扫描单号签收 默认0
     */
    private Integer onlySupportScan2FinishDelivery;

    /**
     * 是否开启该网点POS服务
     */
    private Integer posSwitch;
    /**
     * 特殊区域类型
     */
    private String specialAreaType;
    /**
     * 特殊区域编码
     */
    private String specialAreaCode;
    /**
     * 网点分拣方式 0-zipcode/area,1-routecode,2-zonecode
     */
    private Integer sortingMethod;

    /**
     * 供应商编码(把供应商当成一个网点)
     */
    private String vendorCode;

    /**
     * 所属分拨中心网点编码
     */
    private String belongDcCode;

    /**
     * 网点包裹操作类型
     * Pickup - 揽件网点
     * Delivery - 派送网点
     */
    private String packageOpsType;
}

package com.imile.permission.domain.user.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/12
 */
@Data
public class BindingUserSystemRoleParam {

    /**
     * 版本号
     */
    private Long recordVersion;
    /**
     * 用户编号
     */
    private String userCode;
    /**
     * 角色ID
     */
    private List<Long> roleIdList;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 有效天数
     */
    private Integer day;
}

package com.imile.permission.domain.user.vo;

import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 
 * @date: 2023/10/15
 */
@Data
public class UserAuthorizationSystemIsolationVO implements Serializable {

    /**
     * userCode
     */
    private String userCode;

    /**
     * 角色列表
     */
    private List<Long> roleList;

    /**
     * 基础数据权限
     */
    private List<SystemDataPermissionVO> baseDataPermissionDTO;

    /**
     * 主数据权限
     */
    private List<SystemDataPermissionVO> mainDataPermissionDTO;

    /**
     * 动态数据权限
     */
    private List<SystemDataPermissionVO> dynamicDataPermissionDTO;


    /**
     * 菜单权限
     */
    private List<MenuPermissionDTO> menuPermissionDTOList;


}

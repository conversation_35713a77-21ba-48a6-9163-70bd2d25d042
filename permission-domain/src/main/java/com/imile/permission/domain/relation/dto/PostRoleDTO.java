package com.imile.permission.domain.relation.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <p>
 * 角色申请审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
public class PostRoleDTO {

    /**
     * 角色编号
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色名称
     */
    private String roleNameEn;
    /**
     * 操作时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operationTime;

    /**
     * 适用国家
     */
    private String roleCountry;

    /**
     * 关联系统
     */
    private String multipleSystem;


    /**
     * 敏感等级： 1.敏感，0.不敏感
     */
    private Integer sensitiveLevel;
}

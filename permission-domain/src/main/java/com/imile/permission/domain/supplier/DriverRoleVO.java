package com.imile.permission.domain.supplier;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Data
public class DriverRoleVO {

    private Long id;
    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 绑定人数
     */
    private Integer bindingCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;
    /**
     * 关联系统（用于筛选角色）
     */
    private List<String> systemCodeList;

    /**
     * 被授权主体，1、员工，2、商家
     */
    private Integer authScene;

    /**
     * 角色类型 1 业务角色，2 默认角色，3 CSP供应商管理员角色, 4 司机角色，5 CSP网点角色, 6 供应商类型角色，7 国家供应商类型角色
     */
    private Integer roleType;

    /**
     * 角色名称(中文)
     */
    private String roleName;

    /**
     * 角色英文名称
     */
    private String roleNameEn;

    /**
     * 角色国家
     */
    private List<String> roleCountryList;

    /**
     * 司机职能
     */
    private String functional;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 英文描述
     */
    private String descriptionEn;


    /**
     * 是否接入审批流
     */
    private Integer isAccessApprovalFlow;


}

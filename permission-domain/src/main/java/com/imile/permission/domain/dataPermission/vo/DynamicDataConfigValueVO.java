package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;

import java.util.List;

@Data
public class DynamicDataConfigValueVO {

        /**
         * 是否关联主数据（0:不是 1:是）
         */
        private Integer isAssociatedMainData;

        /**
         * 主数据类型 type_code
         */
        private String associatedMainDataTypeCode;

        /**
         * 归属系统
         */
        private String singleSystem;

        /**
         * 数据内容
         */
        private List<DynamicDataCodeVO> dataList;

        /**
         * 数据维度
         */
        private List<RuleDataVO> ruleDataList;

    }
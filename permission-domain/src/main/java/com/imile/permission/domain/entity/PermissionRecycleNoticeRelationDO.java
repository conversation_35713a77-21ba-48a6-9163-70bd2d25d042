package com.imile.permission.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.permission.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 过期/到期提醒权限关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("permission_recycle_notice_relation")
public class PermissionRecycleNoticeRelationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户编码
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 系统编码
     */
    @TableField("system_code")
    private String systemCode;

    /**
     * 权限类型, 1-角色，2-菜单，3-流程
     */
    @TableField("permission_type")
    private Integer permissionType;

    /**
     * 权限id
     */
    @TableField("permission_ids")
    private String permissionIds;

    /**
     * 到期时间
     */
    @TableField("expirationDate")
    private LocalDateTime expirationDate;

    /**
     * 消息发送时间
     */
    @TableField("sendDate")
    private LocalDateTime sendDate;


    @TableField("is_apply")
    private Integer isApply;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

package com.imile.permission.domain.entity;

import com.imile.permission.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 业务基础数据权限配置发布表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("published_business_basic_data_permission_config")
public class PublishedBusinessBasicDataPermissionConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    @TableField("data_structures")
    private Integer dataStructures;

    /**
     * 数据接口 url
     */
    @TableField("data_url")
    private String dataUrl;

    /**
     * 请求类型（默认GET）
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 源数据类型
     */
    @TableField("source_type_code")
    private String sourceTypeCode;

    /**
     * 数据类型名称
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 归属系统
     */
    @TableField("single_system")
    private String singleSystem;

    /**
     * 最后引用编号
     */
    @TableField("last_reference_version")
    private Integer lastReferenceVersion;

    /**
     * 使用场景解释
     */
    @TableField("use_case_description")
    private String useCaseDescription;

    /**
     * 数据类型英文名称
     */
    @TableField("type_name_en")
    private String typeNameEn;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

package com.imile.permission.domain.applicationApprove.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-28
 * @version: 1.0
 */
@Data
public class ApprovalCreateUserDTO {
    /**
     * 角色对应人员
     */
    private List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS;

    /**
     * 指定人员
     */
    private List<ApprovalUserInfoDTO> approvalUserInfos = Lists.newArrayList();


    /**
     * 该节点所有找不到的人员信息
     */
    private List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList;


    /**
     * 该节点所有找到人员信息
     */
    private List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList;
}

package com.imile.permission.domain.role.param;

import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/9/5
 */
@Data
public class RoleNameParam implements Serializable {

    private Long roleId;

    @Size(max = 50, message = "不能超过 50 字符")
    private String roleName;

    @Size(max = 50, message = "不能超过 50 字符")
    private String roleNameEn;
}

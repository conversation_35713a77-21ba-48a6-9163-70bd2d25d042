package com.imile.permission.domain.relation.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
public class PostRolePageQuery extends BaseQuery {

    /**
     * 是否过期
     */
    private Boolean isExpired;

    /**
     * 是否进行时区转换，默认为 true
     */
    private Boolean useTimezone = true;

    /**
     * 岗位 ID
     */
    private Long postId;

}

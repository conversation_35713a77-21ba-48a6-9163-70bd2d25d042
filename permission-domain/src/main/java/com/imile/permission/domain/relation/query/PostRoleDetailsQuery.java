package com.imile.permission.domain.relation.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/4/7
 */
@Data
public class PostRoleDetailsQuery {
    /**
     * 岗位ID
     */
    @NotNull(message = "postId 不能为空")
    private Long postId;

    /**
     * 用户编码
     */
    @NotNull(message = "roleId 不能为空")
    private Long roleId;


    /**
     * 是否进行时区转换，默认为 true
     */
    private Boolean useTimezone = true;
}
package com.imile.permission.domain.permission.dto;

import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.LabelValueVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/11
 */
@Data
public class PermissionWithNameDTO {

    /**
     * 角色列表
     */
    private List<Long> roleList;

    /**
     * 数据权限
     */
    private List<DataPermissionWithNameDTO> dataPermissionDTOList;

    /**
     * 菜单权限
     */
    private List<DataPermissionDTO> menuPermissionDTOList;


    @Data
    public static class DataPermissionWithNameDTO {

        /**
         * 数据字典类型
         */
        private String typeCode;

        /**
         * 数据字典名称
         */
        private String typeName;

        /**
         * 字典数据
         */
        private List<LabelValueVO> dataList;
    }
}

package com.imile.permission.domain.entity;

import com.imile.permission.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 条件规则关联详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("permission_authorization_rule")
public class PermissionAuthorizationRuleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 授权类型（人、角色）
     */
    @TableField("authorization_type")
    private String authorizationType;

    /**
     * 授权用户编码
     */
    @TableField("authorization_user_code")
    private String authorizationUserCode;

    /**
     * 角色编号
     */
    @TableField("authorization_role_id")
    private Long authorizationRoleId;

    /**
     * 关联类型（主数据、函数数据）
     */
    @TableField("relation_type")
    private String relationType;

    /**
     * 关联数据类型
     */
    @TableField("relation_type_code")
    private String relationTypeCode;

    /**
     * 授权规则JSON
     */
    @TableField("authorization_rule_json")
    private String authorizationRuleJson;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

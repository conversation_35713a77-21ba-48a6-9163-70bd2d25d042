package com.imile.permission.domain.relation.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SysPostRoleRelationDTO implements Serializable {
    private static final long serialVersionUID = 2659686038342136160L;

    private Long id;

    /**
     * 岗位id
     */
    private Long postId;


    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;


    /**
     * 角色ID
     */
    private JSON roleIdJson;

    /**
     * 是否配置角色（0:未配置，1:已配置）
     */
    private Integer isConfigRole;

}

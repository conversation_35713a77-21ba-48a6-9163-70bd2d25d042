package com.imile.permission.domain.authorizationSubjectModel.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/24
 */
@Data
public class ModelBatchAddParam {
    private List<Model> modelList;

    @Data
    public static class Model {

        /**
         * 授权主体模型类型编码
         */
        private String subjectModelCode;

        /**
         * 授权主体模型名称
         */
        private String subjectModelName;

        /**
         * 授权主体模型定义
         */
        private String defineJson;


    }

}

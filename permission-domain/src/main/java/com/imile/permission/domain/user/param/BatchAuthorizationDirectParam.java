package com.imile.permission.domain.user.param;

import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.MultiDynamicDataConfigValueDTO;
import lombok.Data;

import java.util.List;


/**
 * @description: This is a sample class.
 * @author:
 * @date: 2023/10/24
 */
@Data
public class BatchAuthorizationDirectParam {

    /**
     * 用户列表
     */
    private List<String> userCodeList;

    /**
     * 基础数据权限
     */
    private List<DataPermissionRuleDTO> baseDataPermissionDTO;

    /**
     * 主数据权限
     */
    private List<DataPermissionRuleDTO> mainDataPermissionDTO;

    /**
     * 动态数据权限
     */
    private List<DataPermissionRuleDTO> dynamicDataPermissionDTO;

    /**
     * 单数据维度
     */
    private List<DataPermissionRuleDTO> singleDynamicDataConfigValueDTO;

    /**
     * 多数据维度
     */
    private List<MultiDynamicDataConfigValueDTO> multiDynamicDataConfigValueDTO;

    /**
     * 菜单权限
     */
    private List<MenuPermissionDTO> menuPermissionDTOList;


    /**
     * 授权天数
     */
    private Integer day;


}

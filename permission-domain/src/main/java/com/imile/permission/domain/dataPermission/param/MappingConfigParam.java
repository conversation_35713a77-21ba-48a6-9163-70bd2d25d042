package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Data
public class MappingConfigParam {

    /**
     * 主数据id
     */
    @NotNull(message = "sourceTypeCode不可为空")
    private String sourceTypeCode;

    @Valid @NotEmpty(message = "缺少字段映射")
    List<MappingFieldInfoParam> fieldInfos;
}

package com.imile.permission.domain.report;

import com.imile.permission.annotation.WithDict;
import com.imile.permission.constants.BusinessConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthSourceUserPageDTO implements Serializable {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型具体值
     */
    private String employeeTypeDesc;

    /**
     * 所属国家
     */
    private String locationCountry;

    /**
     * 所属部门
     */
    private String deptName;

    /**
     * 所属部门code
     */
    private Long deptId;

    /**
     * 所属岗位
     */
    private String postName;

    /**
     * 所属岗位code
     */
    private Long postId;

    /**
     * 账号状态(ACTIVE-启用 DISABLED-停用)
     */
    private String status;

    /**
     * 授权来源
     */
    private String authSource;

    /**
     * 授权来源code
     */
    private String authSourceDesc;

    private String roleName;
    private String roleNameEn;

    /**
     * 账号状态(ACTIVE-启用 DISABLED-停用)
     */
    private String statusDesc;

}

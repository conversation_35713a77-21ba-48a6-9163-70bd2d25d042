package com.imile.permission.domain.dataPermission.param;

import com.imile.permission.domain.dataPermission.dto.FlowBaseDataAddDTO;
import com.imile.permission.domain.dataPermission.dto.FlowFunctionDataAddDTO;
import com.imile.permission.domain.dataPermission.dto.FlowMainDataAddDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Data
public class FlowAddDataParam {
    /**
     * 流程类型（主数据-MAINDATA、函数数据-FUNCTIONDATA、基础数据-BASEDATA）
     */
    private String flowType;

    /**
     * 主数据流程
     */
    private FlowMainDataAddDTO flowMainDataAddDTO;
    /**
     * 函数数据流程
     */
    private FlowFunctionDataAddDTO flowFunctionDataAddDTO;
    /**
     * 基础数据流程
     */
    private FlowBaseDataAddDTO flowBaseDataAddDTO;


}

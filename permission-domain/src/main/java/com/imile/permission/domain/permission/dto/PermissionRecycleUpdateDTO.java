package com.imile.permission.domain.permission.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/9
 */
@Data
public class PermissionRecycleUpdateDTO {

    private List<Long> deleteIds = Lists.newArrayList();

    private List<Long> execute1Ids = Lists.newArrayList();

    private List<Long> execute2Ids = Lists.newArrayList();

    private List<Long> execute3Ids = Lists.newArrayList();
}

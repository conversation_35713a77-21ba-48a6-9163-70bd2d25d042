package com.imile.permission.domain.operatorSystem;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/18
 */
@Data
public class OperatorSystemRoleQueryDTO extends BaseQuery {
    private Long orgId;
    /**
     * countryCode 国家编码
     */
    private List<String> countryCodeList;

    /**
     * ownStationCode 角色所属站点编码
     */
    private List<String> ownStationCodeList;

    /**
     * roleNameOrCode 角色名称/编码 组合查询
     */
    private String roleNameOrCode;

    /**
     * isDisable 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * adminOrg 角色所属机构级别
     */
    private List<String> adminOrgList;

    /**
     * xmile 系统角色类型字段
     */
    private List<Integer> xmileRoleTypeList;


}

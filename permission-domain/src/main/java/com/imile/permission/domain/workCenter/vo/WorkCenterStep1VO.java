package com.imile.permission.domain.workCenter.vo;

import com.imile.permission.domain.role.vo.RoleListVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
public class WorkCenterStep1VO {
    /**
     * 主键ID
     */
    private Long workCenterId;

    /**
     * 工作中心名称
     */
    private String workCenterName;

    /**
     * 描述
     */
    private String description;


    /**
     * 授权类型（1:公共访问 2:授权角色）
     */
    private Integer authorizationType;

    /**
     *  
     */
    List<RoleListVO> roleList;
}

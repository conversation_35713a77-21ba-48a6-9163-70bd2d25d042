package com.imile.permission.domain.applicationApprove.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.vo.MultiDynamicDataConfigValueDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
public class AuthorityApprovalDataVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

    /**
     * 有效时间（单位，天）
     */
    private Integer effectiveTimeDay;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 审批状态
     */
    private Integer approveStatus;

    /**
     * 审批创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approveCreateTime;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approvedTime;


    /**
     * 权限类型
     */
    private Integer permissionType;

    /**
     * 审批流 ID
     */
    private Long approvalId;

    /**
     * 单据编号
     */
    private String applicationCode;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 数据权限编码集合
     */
    private Set<String> typeCodes;

    /**
     * 基础数据权限
     */
    private List<DataPermissionRuleDTO> baseDataPermissionDTO;

    /**
     * 主数据权限
     */
    private List<DataPermissionRuleDTO> mainDataPermissionDTO;

    /**
     * 动态数据权限
     */
    private List<DataPermissionRuleDTO> dynamicDataPermissionDTO;

    /**
     * 单数据维度
     */
    private List<DataPermissionRuleDTO> singleDynamicDataConfigValueDTO;

    /**
     * 多维度数据
     */
    private List<MultiDynamicDataConfigValueDTO> multiDynamicDataConfigValueDTO;

}

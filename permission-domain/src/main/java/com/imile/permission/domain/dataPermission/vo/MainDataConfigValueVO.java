package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;

import java.util.List;

@Data
public class MainDataConfigValueVO {

        private Long id;
        /**
         * 数据结构类型（0:列表 1:树）
         */
        private Integer dataStructures;

        /**
         * 来源主数据typeCode
         */
        private String sourceTypeCode;
        /**
         * 数据接口 url
         */
        private String dataUrl;

        /**
         * 是否支持搜索
         */
        private Integer isSupportSearch;


        /**
         * 函数数据
         */
        private List<DynamicDataCodeVO> dynamicDataCodeList;

        /**
         * 数据维度
         */
        private List<RuleDataVO> ruleDataList;

        /**
         * 数据扩展标签
         */
        private List<ExtensionTagVO> extensionTagList;
    }
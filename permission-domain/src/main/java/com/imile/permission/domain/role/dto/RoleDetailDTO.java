package com.imile.permission.domain.role.dto;

import com.imile.permission.domain.dataPermission.vo.LabelValueVO;
import com.imile.permission.enums.RoleApplyTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/8
 */
@Data
public class RoleDetailDTO {

    /**
     * 角色名称(中文)
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色名称(英文)
     */
    private String roleNameEn;

    /**
     * 角色描述
     */
    private String descriptionEn;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 是否接入审批流
     */
    private Integer isAccessApprovalFlow;

    /**
     * 关联系统（用于筛选角色）
     */
    private List<String> systemCodeList;


    /**
     * label标签渲染
     */
    private List<LabelValueVO> systemLabelList;

    /**
     * 角色国家
     */
    private List<String> roleCountryList;

    /**
     * 角色类型
     *
     * @see com.imile.permission.enums.RoleTypeEnum
     */
    private Integer roleType;

    /**
     * 被授权主体
     *
     * @see com.imile.permission.enums.RoleAuthSceneEnum
     */
    private Integer authScene;

    /**
     * 用工类型
     *
     * @see com.imile.genesis.api.enums.EmploymentTypeEnum
     */
    private List<String> employmentTypeList;

    /**
     * 1-员工
     * 2-岗位
     *
     * @see RoleApplyTypeEnum
     */
    private Integer applyType;

    /**
     * 父角色 ID
     */
    private Long parentId;
}

package com.imile.permission.domain.role.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * RoleEditableVO
 */
@Data
public class RoleEditableVO implements Serializable {

    private Long id;

    /**
     * 角色名称(中文)
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 关联系统
     */
    private String multipleSystem;

    /**
     * 关联系统列表
     */
    private List<String> systemCodeList;

    /**
     * true：可编辑
     * false: 不可编辑
     */
    private Boolean editable;

    /**
     * 角色国家
     */
    private List<String> roleCountryList;

    /**
     * 0: 业务角色
     * 1: 默认角色
     *
     * @see com.imile.permission.enums.RoleTypeEnum
     */
    private Integer roleType;

    private String roleTypeDesc;

    /**
     * 用工类型
     *
     * @see com.imile.genesis.api.enums.EmploymentTypeEnum
     */
    private List<String> employmentTypeList;

    private List<String> employmentTypeDescList;


    /**
     * 敏感等级： 1.敏感，0.不敏感
     */
    private Integer sensitiveLevel;
}

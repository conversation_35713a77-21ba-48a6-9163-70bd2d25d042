package com.imile.permission.domain.workCenter.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
public class WorkCenterDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 工作中心名称
     */
    private String workCenterName;

    /**
     * 描述
     */
    private String description;

    /**
     * 流程步骤（1:第一步，2:第二步，3:第三步）
     */
    private Integer step;


    /**
     * 授权类型（1:公共访问 2:授权角色）
     */
    private Integer authorizationType;

    /**
     * 系统code
     */
    private String systemCode;
}

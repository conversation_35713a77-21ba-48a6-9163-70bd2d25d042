package com.imile.permission.domain.applicationApprove.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
public class AuthorityApprovalPageQuery extends BaseQuery {
    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 是否过期
     */
    private Boolean isExpired;


    /**
     * 过期时间，后端根据服务器自动时间生成，前端不用传输
     */
    private LocalDateTime expirationDate;

    /**
     * 数据检索类型，前端不用传输
     */
    private List<String> sourceTypeList;

    private Boolean searchDataFlag = false;
    /**
     * 是否进行时区转换，默认为 true
     */
    private Boolean useTimezone = true;

}

package com.imile.permission.domain.workCenter.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
public class WorkCenterEditableVO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 工作中心名称
     */
    private String workCenterName;

    /**
     * 流程属于的系统
     */
    private String singleSystem;

    /**
     * true：可编辑
     * false: 不可编辑
     */
    private Boolean editable;
}

package com.imile.permission.domain.supplier;

import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Data
public class AddOutletRoleParam {
    /**
     * 基础信息
     */
    private OutletRoleBasicParam roleBasicParam;
    /**
     * 菜单权限
     */
    private List<MenuPermissionDTO> menuPermissionDTOList;
}

package com.imile.permission.domain.dataPermission.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class BusinessBasicDataPermissionConfigVO {

    private Long id;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;

    /**
     * 数据接口 url
     */
    private String dataUrl;

    /**
     * 请求类型
     */
    private String requestType;

    /**
     * 是否支持搜索
     */
    private Integer isSupportSearch = 0;

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型显示名称
     */
    private String showTypeName;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 数据类型En名称
     */
    private String typeNameEn;
    /**
     * 系统code
     */
    private String systemCode;

    @JsonIgnore
    private Date createDate;

    /**
     * 同权的系统来源
     */
    private String sourceSystem;

    /**
     * 关联的引用系统
     */
    private List<String> relatedSystemList;

    /**
     * 创建类型
     * owner 创建
     * ref 引用
     */
    private String useCaseType;

    /**
     * 来源系统
     */
    private String refSourceSystem;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 来源数据类型
     */
    private String sourceTypeCode;

    /**
     * 函数数据
     */
    private List<DynamicDataCodeVO> dynamicDataCodeList;

    /**
     * 数据维度
     */
    private List<RuleDataVO> ruleDataList;

    /**
     * 数据扩展标签
     */
    private List<ExtensionTagVO> extensionTagList;
}

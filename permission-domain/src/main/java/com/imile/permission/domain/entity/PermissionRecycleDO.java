package com.imile.permission.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.permission.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 权限回收临时表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("permission_recycle")
public class PermissionRecycleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户编码
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 用户编号
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 流程id编号
     */
    @TableField("recycle_id")
    private String recycleId;

    /**
     * 申请日期
     */
    @TableField("application_date")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @TableField("expiration_date")
    private LocalDateTime expirationDate;

    /**
     * 是否执行角色失效处理（0:未执行，1:已执行）
     */
    @TableField("is_execute")
    private Integer isExecute;

    /**
     * 1- 直接授权
2-角色授权
3-流程授权
     */
    @TableField("recycle_type")
    private Integer recycleType;

    /**
     * 直接授权需要的类型
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 关联的外部id
     */
    @TableField("related_id")
    private Long relatedId;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

package com.imile.permission.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.permission.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("permission_log")
public class PermissionLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 平台
     */
    @TableField("platform")
    private String platform;

    /**
     * 操作者编码
     */
    @TableField("operator")
    private String operator;

    /**
     * 操作时间
     */
    @TableField("operate_time")
    private LocalDateTime operateTime;

    /**
     * 业务单号
     */
    @TableField("biz_no")
    private String bizNo;

    /**
     * 模块
     */
    @TableField("module")
    private String module;

    /**
     * 操作类型
     */
    @TableField("type")
    private String type;

    /**
     * 操作内容
     */
    @TableField("content")
    private String content;

    /**
     * 操作参数
     */
    @TableField("content_param")
    private String contentParam;

    /**
     * 操作花费的时间
     */
    @TableField("execute_time")
    private Long executeTime;

    /**
     * 操作状态（1:成功 2:失败）
     */
    @TableField("status")
    private Integer status;

    /**
     * 执行后返回的json字符串
     */
    @TableField("result")
    private String result;

    /**
     * 异常信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 变更详情
     */
    @TableField("detail")
    private String detail;

    /**
     * ip
     */
    @TableField("ip")
    private String ip;

    /**
     * url
     */
    @TableField("url")
    private String url;

    /**
     * HTTP请求方式
     */
    @TableField("http_method")
    private String httpMethod;

    /**
     * 类方法路径
     */
    @TableField("class_method")
    private String classMethod;

    /**
     * 接口参数 
     */
    @TableField("param")
    private String param;

    /**
     * 异常堆栈信息
     */
    @TableField("stack_trace")
    private String stackTrace;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

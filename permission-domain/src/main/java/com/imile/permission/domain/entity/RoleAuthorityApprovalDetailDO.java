package com.imile.permission.domain.entity;

import com.imile.permission.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 角色授权详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("role_authority_approval_detail")
public class RoleAuthorityApprovalDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户编码
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 用户编号
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 角色编号
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 申请日期
     */
    @TableField("application_date")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @TableField("expiration_date")
    private LocalDateTime expirationDate;

    /**
     * 是否系统操作类型（0:不是，1是）
     */
    @TableField("is_system_operation_type")
    private Integer isSystemOperationType;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 操作时间
     */
    @TableField("operation_time")
    private LocalDateTime operationTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 单据编号
     */
    @TableField("application_code")
    private String applicationCode;

    /**
     * 审批流 ID
     */
    @TableField("approval_id")
    private Long approvalId;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

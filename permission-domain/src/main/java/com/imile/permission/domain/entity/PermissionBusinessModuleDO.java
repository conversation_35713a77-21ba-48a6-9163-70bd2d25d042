package com.imile.permission.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.permission.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 权限业务模块表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("permission_business_module")
public class PermissionBusinessModuleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 关联的systemcode
     */
    @TableField("system_code")
    private String systemCode;

    /**
     * 业务模块code
     */
    @TableField("business_code")
    private String businessCode;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

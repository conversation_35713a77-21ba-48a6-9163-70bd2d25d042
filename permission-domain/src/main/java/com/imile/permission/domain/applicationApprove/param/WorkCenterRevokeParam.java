package com.imile.permission.domain.applicationApprove.param;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@Data
@Accessors(chain = true)
public class WorkCenterRevokeParam {

    /**
     * 用户编号
     */
    @NotBlank(message = "userCode不能为空")
    private String userCode;
    /**
     * 工作中心id
     */
    @NotNull(message = "workCenterId不能为空")
    private Long workCenterId;
}

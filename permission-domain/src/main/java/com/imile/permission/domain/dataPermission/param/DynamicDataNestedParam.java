package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/30
 */
@Data
public class DynamicDataNestedParam {

    /**
     * 数据code
     */
    private String dataCode;

    /**
     * 数据接口 url
     */
    private String dataUrl;

    /**
     * 可选参数
     */
    private String optionalParameter;

    /**
     * 描述
     */
    private String description;
}

package com.imile.permission.domain.role.vo;

import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.DimensionSystemDataPermissionRuleVO;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionRuleVO;
import com.imile.permission.domain.role.dto.RoleDetailDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author:
 * @date: 2023/10/15
 */
@Data
public class RolePermissionRuleVO implements Serializable {

    private Long id;
    /**
     * 角色基本信息
     */
    private RoleDetailDTO roleDetailDTO;


    /**
     * 基础数据权限
     */
    private List<SystemDataPermissionRuleVO> baseDataPermissionDTO;

    /**
     * 主数据权限
     */
    private List<SystemDataPermissionRuleVO> mainDataPermissionDTO;

    /**
     * 动态数据权限
     */
    private List<SystemDataPermissionRuleVO> dynamicDataPermissionDTO;

    /**
     * 单数据维度
     */
    private List<SystemDataPermissionRuleVO> singleDynamicDataConfigValueDTO;

    /**
     * 多维度数据
     */
    private List<DimensionSystemDataPermissionRuleVO> multiDynamicDataConfigValueDTO;

    /**
     * 菜单权限
     */
    private List<MenuPermissionDTO> menuPermissionDTOList;
}

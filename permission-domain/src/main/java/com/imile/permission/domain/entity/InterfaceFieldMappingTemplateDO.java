package com.imile.permission.domain.entity;

import com.imile.permission.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 主数据接口字段映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("interface_field_mapping_template")
public class InterfaceFieldMappingTemplateDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 目标字段
     */
    @TableField("target_field")
    private String targetField;

    /**
     * 来源字段类型
     */
    @TableField("target_field_type")
    private String targetFieldType;

    /**
     * 是否展示(0-否,1-是)
     */
    @TableField("is_display")
    private Integer isDisplay;

    /**
     * 序号
     */
    @TableField("sort_no")
    private Integer sortNo;

    /**
     * 是否必填(0-否,1-是)
     */
    @TableField("is_required")
    private Integer isRequired;


    /**
     * 数据结构（0-列表，1-树）
     */
    @TableField("mapping_type")
    private Integer mappingType;

    /**
     * 解释
     */
    @TableField("description")
    private String description;

    /**
     * 是否支持搜索
     */
    @TableField("is_support_search")
    private Integer isSupportSearch;

    @TableField("is_edit_target_field")
    private Integer isEditTargetField;

    @TableField("is_edit_display")
    private Integer isEditDisplay;

    @TableField("is_edit_search")
    private Integer isEditSearch;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

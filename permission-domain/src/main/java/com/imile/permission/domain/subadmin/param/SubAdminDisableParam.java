package com.imile.permission.domain.subadmin.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Data
public class SubAdminDisableParam {

    @NotNull(message = "userCode 不能为空")
    private String userCode;

    /**
     * @see com.imile.permission.enums.SubAdminEnableEnum
     *
     * DISABLE(0, "禁用"),
     * ENABLE(1, "启用"),
     */
    @NotNull(message = "enable 不能为空")
    private Integer enable;
}

package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CalculateWorkCenterExpirationTimeParam {

    @NotBlank(message = "用户编码不能为空")
    private String userCode;

    @NotNull(message = "流程ID不能为空")
    private Long workCenterId;

    @NotNull(message = "effectiveTimeDay 不能为空")
    private Integer effectiveTimeDay;
}

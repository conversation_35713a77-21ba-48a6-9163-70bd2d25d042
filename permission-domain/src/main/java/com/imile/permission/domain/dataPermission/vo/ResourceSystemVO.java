package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/11
 */
@Data
public class ResourceSystemVO {

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 系统描述
     */
    private String systemDesc;

    /**
     * 业务负责人userCode
     */
    private String bizUserCode;

    /**
     * 业务负责人名称
     */
    private String bizUserName;

    /**
     * 业务负责人电话
     */
    private String bizUserPhone;

    /**
     * 业务负责人企业微信id
     */
    private String bizUserWxid;

    /**
     * 产品负责人userCode
     */
    private String prodUserCode;

    /**
     * 产品负责人名称
     */
    private String prodUserName;

    /**
     * 产品负责人电话
     */
    private String prodUserPhone;

    /**
     * 产品负责人企业微信id
     */
    private String prodUserWxid;

}

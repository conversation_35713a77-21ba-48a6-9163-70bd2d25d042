package com.imile.permission.domain.role.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.enums.RoleApplyTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 系统角色明细返回DTO类.
 * @author:
 * @date: 2023/10/15
 */
@Data
public class RolePageVO implements Serializable {

    private Long id;

    private Long parentId;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 角色名称(中文)
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 关联岗位
     */
    private Integer postIdCount;

    /**
     * 修改人名称
     */
    private String lastUpdUserName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;


    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 关联系统JSON字符串
     */
    private String multipleSystem;

    /**
     * 关联系统数组
     */
    private List<String> systemCodeList;

    /**
     * 用户数量
     */
    private Integer userCount;

    /**
     * 是否接入审批流
     */
    private Integer isAccessApprovalFlow;

    /**
     * 角色国家
     */
    private List<String> roleCountryList;

    /**
     * 0: 业务角色
     * 1: 默认角色
     * 角色类型
     *
     * @see com.imile.permission.enums.RoleTypeEnum
     */
    private Integer roleType;

    private String roleTypeDesc;

    /**
     * 被授权主体
     *
     * @see com.imile.permission.enums.RoleAuthSceneEnum
     */
    private Integer authScene;

    private String authSceneDesc;


    /**
     * 用工类型
     *
     * @see com.imile.genesis.api.enums.EmploymentTypeEnum
     */
    private List<String> employmentTypeList;

    private List<String> employmentTypeDescList;

    /**
     * 1-员工
     * 2-岗位
     *
     * @see RoleApplyTypeEnum
     */
    private Integer applyType;

    private String applyTypeDesc;


    /**
     * 角色绑定岗位数量
     * 如果是默认角色，展示"-"
     */
    private Integer roleBindPostCount;

    /**
     * 绑定商家数量
     */
    private Integer boundClientCount;


    /**
     * 子角色信息
     */
    private RolePageVO childRole;
    /**
     * 敏感等级： 1.敏感，0.不敏感
     */
    private Integer sensitiveLevel;

}

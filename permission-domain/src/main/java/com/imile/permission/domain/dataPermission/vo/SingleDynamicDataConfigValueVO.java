package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/9
 */
@Data
public class SingleDynamicDataConfigValueVO {
    private Long id;
    private Integer dimension;
    private Integer dataStructures;
    private String dataUrl;
    private List<DynamicDataCodeVO> dynamicDataCodeList;
    private List<RuleDataVO> ruleDataList;
    /**
     * 是否支持搜索 默认否
     */
    private Integer isSupportSearch = 0;

}
package com.imile.permission.domain.obtain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class KAClientVO {
    /**
     * 合作状态 HOLD：已暂停 ING：合作中 END：已终止
     */
    private String cooperationStatus;

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 公司全称
     */
    private String fullName;

    /**
     * 客户简码
     */
    private String clientShortName;

    /**
     * 业务区域
     */
    private String businessArea;

    /**
     * 业务区域中心 对应国家 多个逗号隔开
     */
    private String ocCenterCodeDesc;

    /**
     * 商家账户类型 正式商家-FORMAL;测试商家-TEST
     */
    private String clientAccountType;

    /**
     * 公司全称英文
     */
    private String fullNameEn;

}

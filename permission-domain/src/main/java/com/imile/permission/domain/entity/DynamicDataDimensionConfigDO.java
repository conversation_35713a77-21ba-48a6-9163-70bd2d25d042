package com.imile.permission.domain.entity;

import com.imile.permission.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 动态数据权限维度配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dynamic_data_dimension_config")
public class DynamicDataDimensionConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 归属系统
     */
    @TableField("single_system")
    private String singleSystem;

    /**
     * 0：单维度，1：多维度
     */
    @TableField("dimension")
    private Integer dimension;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    @TableField("data_structures")
    private Integer dataStructures;

    /**
     * 数据接口 url
     */
    @TableField("data_url")
    private String dataUrl;

    /**
     * 数据接口 url
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 数据类型
     */
    @TableField("type_code")
    private String typeCode;

    /**
     * 数据类型名称
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 数据类型名称
     */
    @TableField("type_name_en")
    private String typeNameEn;

    /**
     * 使用场景解释
     */
    @TableField("use_case_description")
    private String useCaseDescription;

    /**
     * 多维度数据维度配置
     */
    @TableField("multi_dimension_config")
    private String multiDimensionConfig;

    /**
     * 多维度数据维度的主数据配置，冗余字段方便处理数据
     */
    @TableField("multi_dimension_ref_main_data_code")
    private String multiDimensionRefMainDataCode;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}

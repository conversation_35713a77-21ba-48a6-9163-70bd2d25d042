package com.imile.permission.domain.relation.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 岗位角色关联关系查询结果类
 * @author: 
 * @date: 2023/10/15
 */
@Data
public class SysPostRoleRelationResultVO implements Serializable {
    private static final long serialVersionUID = 8253618308780553648L;

    private Long id;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 关联角色数量
     */
    private Integer roleCount;

    /**
     * 最后更新时间
     */
    private Date lastUpdDate;

    /**
     * 最后更新人编码
     */
    private String lastUpdUserCode;

    /**
     * 最后更新人姓名
     */
    private String lastUpdUserName;
}

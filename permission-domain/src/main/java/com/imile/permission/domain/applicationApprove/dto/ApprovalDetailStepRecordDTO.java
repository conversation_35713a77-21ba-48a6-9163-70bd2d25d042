package com.imile.permission.domain.applicationApprove.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-28
 * @version: 1.0
 */
@Data
public class ApprovalDetailStepRecordDTO {
    /**
     * 审批流步骤编号
     */
    private String stepId;

    /**
     * 审批流步骤名称
     */
    private String stepName;

    /**
     * 审核记录状态
     */
    private Integer recordStatus;

    /**
     * 审核状态名称
     */
    private String recordStatusName;

    /**
     * 审核记录状态变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @OutWithTimeZone
    private Date recordStatusUpdateDate;

    /**
     * 审批步骤类型：approval(审批),cc(抄送)
     */
    private String approvalRecordType;

    /**
     * businessLetter专属类型
     */
    private String approvalType;

    /**
     * 审批人员
     */
    private List<ApprovalUserInfoDTO> approvalUserInfoDTOS;

    /**
     * 角色人员信息
     */
    private ApprovalCreateUserDTO approvalCreateUserDTO;
}

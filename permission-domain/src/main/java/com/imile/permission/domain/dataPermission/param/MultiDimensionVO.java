package com.imile.permission.domain.dataPermission.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
public class MultiDimensionVO {

    /**
     * 数据类型
     */
    private String typeCode;


    /**
     * 数据类型显示名称
     */
    private String showTypeName;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 数据类型英文名称
     */
    private String typeNameEn;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 维度数据
     */
    private List<DimensionDataExt> dimensionDataList;
    @JsonIgnore
    private Date createDate;

}

<?xml version="1.0" encoding="UTF-8"?>
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<configuration monitorInterval="5">
    <!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->

    <!--变量配置-->
    <Properties>
        <!-- 格式化输出：%date表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符-->
        <!-- %logger{36} 表示 Logger 名字最长36个字符 -->
        <property name="Console_PATTERN" value="%highlight{%d{DEFAULT} traceId:arms-%X{EagleEye-TraceID}/skywalking-{%traceId} %T %-5level  %c %m %l %msg%n}{FATAL=Bright Red, ERROR=Bright Magenta, WARN=Bright Yellow, INFO=Bright Green, DEBUG=Bright Cyan, TRACE=Bright White}" />
<!--        <property name="File_PATTERN" value="{time: %d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss}}; appName: ${project}; ip: ${ip}; level: %level; traceId: %replace{%traceId}{TID:}{}; thread: %thread; class: %c; line: %L; message: %enc{%maxLen{%msg}{30720}}{CRLF} Ω stack_trace: %throwable{100};}\r\n" />-->
        <property name="File_PATTERN" value="{time: %d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss}}; appName: ${project}; ip: ${ip}; level: %level; traceId: %X{EagleEye-TraceID}; thread: %thread; class: %c; line: %L; message: %enc{%maxLen{%msg}{30720}}{CRLF} arms-%X{EagleEye-TraceID}/skywalking-{%traceId}Ω stack_trace: %throwable{100};}\r\n" />

        <!-- 定义日志存储的路径，不要配置相对路径 -->
        <property name="FILE_PATH" value="/opt/logs/permission" />
        <property name="FILE_NAME" value="PERMISSION" />
        <property name="env">${sys:env}</property>
        <property name="project">${sys:project.name}</property>
        <property name="ip">${sys:local.ip}</property>
    </Properties>

    <appenders>

        <console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${Console_PATTERN}"/>
            <!--控制台只输出level及其以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </console>


        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingRandomAccessFile name="RollingFileInfo" fileName="${FILE_PATH}/info.log" filePattern="${FILE_PATH}/${FILE_NAME}-INFO-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${File_PATTERN}"/>
            <Policies>
                <!--10m滚动一次-->
                <CronTriggeringPolicy schedule="0 0/10 * * * ?" />
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy max="48"/>
        </RollingRandomAccessFile>

        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingRandomAccessFile name="RollingFileDruid" fileName="${FILE_PATH}/druid.log" filePattern="${FILE_PATH}/${FILE_NAME}-DRUID-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${File_PATTERN}"/>
            <Policies>
                <!--10m滚动一次-->
                <CronTriggeringPolicy schedule="0 0/10 * * * ?" />
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy max="48"/>
        </RollingRandomAccessFile>

        <!-- 这个会打印出所有的warn及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingRandomAccessFile name="RollingFileWarn" fileName="${FILE_PATH}/warn.log" filePattern="${FILE_PATH}/${FILE_NAME}-WARN-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${File_PATTERN}"/>
            <Policies>
                <!--10m滚动一次-->
                <CronTriggeringPolicy schedule="0 0/10 * * * ?" />
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy max="48"/>
        </RollingRandomAccessFile>

        <!-- 这个会打印出所有的error及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingRandomAccessFile name="RollingFileError" fileName="${FILE_PATH}/error.log" filePattern="${FILE_PATH}/${FILE_NAME}-ERROR-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${File_PATTERN}"/>
            <Policies>
                <!--10m滚动一次-->
                <CronTriggeringPolicy schedule="0 0/10 * * * ?" />
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy max="48"/>
        </RollingRandomAccessFile>

    </appenders>

    <!--Logger节点用来单独指定日志的形式，比如要为指定包下的class指定不同的日志级别等。-->
    <!--然后定义loggers，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <!--记录druid-sql的记录-->
        <AsyncLogger name="druid.sql.Statement" level="error" additivity="false">
            <appender-ref ref="RollingFileDruid"/>
        </AsyncLogger>

        <AsyncLogger name="druid.sql.Statement" level="error" additivity="false">
            <appender-ref ref="RollingFileDruid"/>
        </AsyncLogger>
        <AsyncLogger name="com.alibaba.druid.filter.stat.StatFilter" level="error" additivity="false">
            <appender-ref ref="RollingFileDruid"/>
        </AsyncLogger>
        <AsyncLogger name="com.alibaba.druid.filter.logging.Log4j2Filter" level="error" additivity="false">
            <appender-ref ref="RollingFileDruid"/>
        </AsyncLogger>

        <!--过滤掉spring和mybatis的一些无用的DEBUG信息-->
        <AsyncLogger name="org.mybatis" level="error" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </AsyncLogger>

        <!--过滤掉spring和mybatis的一些无用的DEBUG信息-->
        <AsyncLogger name="org.apache.ibatis" level="error" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </AsyncLogger>

        <!--监控系统信息-->
        <!--若是additivity设为false，则 子Logger 只会在自己的appender里输出，而不会在 父Logger 的appender里输出。-->
        <AsyncLogger name="org.springframework" level="info" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </AsyncLogger>

        <AsyncLogger name="com.log4j2.Log4j2" level="info" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </AsyncLogger>

        <AsyncLogger name="org.apache.zookeeper" level="info" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </AsyncLogger>

        <AsyncLogger name="io.shardingsphere.core" level="error" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </AsyncLogger>


        <Asyncroot level="info"  includeLocation="true">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </Asyncroot>

    </loggers>

</configuration>

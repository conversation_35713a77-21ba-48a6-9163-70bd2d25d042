package com.imile.permission.web;

import com.imile.common.result.Result;
import com.imile.permission.domain.dataPermission.vo.ResourceSystemVO;
import com.imile.permission.domain.permission.vo.ResourceTreeVO;
import com.imile.permission.domain.system.param.MenuTreeWithParentParam;
import com.imile.permission.domain.system.param.ResourceMenuParam;
import com.imile.permission.domain.system.vo.SystemNavigateVO;
import com.imile.permission.integration.resource.dto.RenewalTreeApiDTO;
import com.imile.permission.service.ResourceService;
import com.imile.resource.api.dto.ResResourceTreeForAuthApiDTO;
import com.imile.resource.api.dto.ResSystemResourceTreeApiDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@RestController
@RequestMapping("/resource")
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    /**
     * 获取授权用户列表
     */
    @GetMapping("/get/full/permission")
    public Result<?> getFullPermission() {
        return Result.ok(resourceService.getFullPermission());
    }

    /**
     * 设置用户全部权限
     */
    @GetMapping("/add/full/permission")
    public Result<Void> addFullPermission(
            String userCode
    ) {
        resourceService.addFullPermission(userCode);
        return Result.ok();
    }

    /**
     * 删除用户全部权限
     */
    @GetMapping("/delete/full/permission")
    public Result<Void> addFullPermissionUserCode(
            String userCode
    ) {
        resourceService.deleteFullPermission(userCode);
        return Result.ok();
    }

    /**
     * 根据菜单id获取菜单树状结构
     */
    @PostMapping("/getMenuTreeWithParentById")
    public Result<List<ResSystemResourceTreeApiDTO>> getMenuTreeWithParentById(
            @RequestBody @Valid MenuTreeWithParentParam param,
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.getMenuTreeWithParent(param,lang));
    }


    @GetMapping("/treeByNoticeId")
    public Result<RenewalTreeApiDTO> treeByNoticeId(
            @NotBlank(message = "消息id不能为空") String noticeId
    ) {
        return Result.ok(resourceService.treeByNoticeId(noticeId));
    }

    /**
     * 用户的菜单权限
     *
     * @return Result<List < ResResourceTreeForAuthApiDTO>>
     */
    @GetMapping("/auth/resource")
    public Result<List<ResResourceTreeForAuthApiDTO>> authResource(
            String system,
            String[] nodeType,
            String parentCode,
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.authResource(system, nodeType, lang, parentCode));
    }

    @GetMapping("/getAllMenuTree")
    public Result<List<ResResourceTreeForAuthApiDTO>> getAllMenuTree(
            String system,
            String[] nodeType,
            String parentCode,
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.getAllMenuTree(system, nodeType, lang, parentCode));
    }

    /**
     * 用户的菜单权限
     *
     * @return Result<List < ResResourceTreeForAuthApiDTO>>
     */
    @GetMapping("/auth/user-order-resource")
    public Result<List<ResResourceTreeForAuthApiDTO>> userOrderResource(
            String system,
            String[] nodeType,
            String parentCode,
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.userOrderResource(system, nodeType, lang, parentCode));
    }

    /**
     * 菜单权限树
     *
     * @return Result<List < ResResourceTreeForAuthApiDTO>>
     */
    @GetMapping("/getTree")
    public Result<List<ResSystemResourceTreeApiDTO>> getTree(
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.getTree(lang));
    }

    /**
     * 获取菜单权限树
     * 如果是超级管理员，返回全部菜单
     * 如果是子管理员，返回用户拥有的的菜单
     * 如果传入 systemCode，返回系统下的菜单
     *
     * @param resourceMenuParam
     * @return
     */
    @PostMapping("/getMenuTree")
    public Result<List<ResourceTreeVO>> getMenuTree(@RequestBody ResourceMenuParam resourceMenuParam) {
        List<ResourceTreeVO> menuTree = resourceService.getMenuTree2(resourceMenuParam);
        menuTree.sort(Comparator.comparing(ResourceTreeVO::getResourceCode));
        return Result.ok(menuTree);
    }

    /**
     * 获取菜单对应的一级目录和菜单
     */
    @GetMapping("/getTwoLevelMenuTree")
    public Result<List<com.imile.resource.api.dto.ResResourceTreeForAuthApiDTO>> getTwoLevelMenuTree(
            String system,
            String[] nodeType,
            String parentCode,
            @RequestHeader String lang
    ) {
        List<com.imile.resource.api.dto.ResResourceTreeForAuthApiDTO> resResourceTreeForAuthApiDTOS = resourceService.getTwoLevelMenuTree(system, nodeType, lang, parentCode);
        return Result.ok(resResourceTreeForAuthApiDTOS);
    }

    /**
     * 获取菜单的系统导航
     *
     * @return
     */
    @GetMapping("/getSystemNavigate")
    public Result<List<SystemNavigateVO>> getSystemNavigate() {
        List<SystemNavigateVO> menuTree = resourceService.getSystemNavigate();
        return Result.ok(menuTree);
    }


    /**
     * 全部菜单权限树
     *
     * @return Result<List < ResResourceTreeForAuthApiDTO>>
     */
    @GetMapping("/getAllTree")
    public Result<List<ResSystemResourceTreeApiDTO>> getAllTree(
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.getAllTree(lang));
    }

    /**
     * 用户的菜单权限
     *
     * @return Result<List < ResResourceTreeForAuthApiDTO>>
     */
    @GetMapping("/getUserTree")
    public Result<List<ResResourceTreeForAuthApiDTO>> getUserTree(
            String userCode,
            String system,
            String[] nodeType,
            String parentCode,
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.getUserTree(userCode, system, nodeType, lang, parentCode));
    }


    /**
     * 获取角色、或用户的菜单权限树
     *
     * @param lang
     * @param roleId
     * @return
     */
    @GetMapping("getTreeByRoleIdOrUserCode")
    public Result<List<ResSystemResourceTreeApiDTO>> getTreeByRoleId(@RequestHeader String lang, Long roleId, String userCode) {
        List<ResSystemResourceTreeApiDTO> result = resourceService.getTreeByRoleId(lang, roleId, userCode);
        return Result.ok(result);
    }


    /**
     * 获取角色、或用户的系统
     *
     * @param roleId
     * @return
     */
    @GetMapping("getSystemCodeByRoleIdOrUserCode")
    public Result<List<String>> getSystemCodeByRoleIdOrUserCode(@RequestHeader String lang, Long roleId, String userCode) {
        List<String> result = resourceService.getSystemCodeByRoleIdOrUserCode(lang, roleId, userCode);
        return Result.ok(result);
    }

    /**
     * 检查菜单 ResourceCode 权限
     */
    @GetMapping("/check/resource/code")
    public Result<List<String>> checkResourceCode(
            @RequestHeader String lang,
            String system,
            String[] nodeType,
            String parentCode,
            String userCode
    ) {
        List<String> result = resourceService.checkResourceCode(lang, system, nodeType, parentCode, userCode);
        return Result.ok(result);
    }


    /**
     * 汇总用户的主数据系统
     *
     * @return
     */
    @GetMapping("getMainDataSystemCodeByUserCode")
    public Result<List<String>> getMainDataSystemCodeByUserCode(String userCode) {
        List<String> result = resourceService.getMainDataSystemCodeByUserCode(userCode);
        return Result.ok(result);
    }


    /**
     * 商家系统菜单列表
     * https://itp.52imile.cn/system/51/interface/api/16735
     */
    @PostMapping("/getClientMenuTree")
    public Result<List<ResSystemResourceTreeApiDTO>> getClientMenuTree() {
        List<ResSystemResourceTreeApiDTO> result = resourceService.getClientMenuTree();
        return Result.ok(result);
    }

    /**
     * 商家角色菜单，默认非管理员角色
     * https://itp.52imile.cn/system/51/interface/api/16878
     *
     * @return Result<List < ResResourceTreeForAuthApiDTO>>
     */
    @GetMapping("/client/auth/resource")
    public Result<List<com.imile.resource.api.dto.ResResourceTreeForAuthApiDTO>> clientAuthResource(
            String clientCode,
            Boolean nonAdministrator,
            String system,
            String[] nodeType,
            String parentCode,
            @RequestHeader String lang
    ) {
        return Result.ok(resourceService.clientAuthResource(clientCode, nonAdministrator, system, nodeType, lang, parentCode));
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/system")
    public Result<ResourceSystemVO> getBySystem(@NotBlank(message = "system不能为空") String system){
        return Result.ok(resourceService.getBySystem(system));
    }

}

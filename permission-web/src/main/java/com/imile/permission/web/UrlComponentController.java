package com.imile.permission.web;

import com.imile.common.result.Result;
import com.imile.permission.integration.resource.ResourceMenuUrlRelationIntegration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限申请
 *
 * <AUTHOR>
 * @since 2024/3/18
 */
@Slf4j
@RestController
@RequestMapping("/UrlComponent")
public class UrlComponentController {

    @Autowired
    private ResourceMenuUrlRelationIntegration resourceMenuUrlRelationIntegration;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;


    @GetMapping("/init")
    public Result<Boolean> init() {
        Map<String, Integer> urlEncodeMap = resourceMenuUrlRelationIntegration.getUrlCodesMap();
        if (MapUtils.isNotEmpty(urlEncodeMap)) {
            for (Map.Entry<String, Integer> entry : urlEncodeMap.entrySet()) {
                redisTemplate.opsForValue().set("PERMISSION:URLCODE:CACHE:" + entry.getKey(), entry.getValue().toString());
            }
        }
        return Result.ok(true);
    }


}

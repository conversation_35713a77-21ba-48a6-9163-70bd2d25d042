package com.imile.permission.web;

import com.imile.common.result.Result;
import com.imile.permission.domain.dataPermission.param.MappingConfigParam;
import com.imile.permission.domain.dataPermission.vo.FieldInfoVO;
import com.imile.permission.domain.dataPermission.vo.MainDataFieldMappingTreeVO;
import com.imile.permission.domain.dataPermission.vo.MappingFieldInfoVO;
import com.imile.permission.service.InterfaceFieldMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/3/24
 */
@RestController
@RequestMapping("/mainData/fieldMapping")
public class MainDataFieldMappingController {

    @Autowired
    private InterfaceFieldMappingService interfaceFieldMappingService;

    /**
     * 获取主数据接口映射后返回值
     * @param sourceTypeCode 主数据id
     * @param search 支持搜索字段模糊匹配值
     */
    @GetMapping("/tree")
    public Result<List<MainDataFieldMappingTreeVO>> tree(@Valid  @NotBlank(message = "主数据sourceTypeCode不可为空") String sourceTypeCode, String type, String search){

        return Result.ok(interfaceFieldMappingService.tree(sourceTypeCode, type, search));
    }


    /**
     * 获取主数据展示字段
     */
    @GetMapping("/displayName")
    public Result<Map<String,String>> displayName(@Valid  @NotBlank(message = "主数据sourceTypeCode不可为空") String sourceTypeCode){

        return Result.ok(interfaceFieldMappingService.displayName(sourceTypeCode));
    }

    /**
     * 获取接口来源字段
     * @param url 接口url
     * @param requestType 请求类型 GET/POST，默认GET
     */
    @GetMapping("/interfaceFields")
    public Result<List<FieldInfoVO>> getInterfaceFields(@Valid  @NotBlank(message = "接口url不可为空") String url,String requestType){
        return Result.ok(interfaceFieldMappingService.parseInterfaceFields(url,requestType));
    }

    /**
     * 获取模板映射
     * @param mappingType 0-列表， 1-树
     */
    @GetMapping("/template")
    public Result<List<MappingFieldInfoVO>> template(Integer mappingType){
        if(mappingType == null){
            mappingType = 1;
        }
        return Result.ok(interfaceFieldMappingService.getTemplateFields(mappingType));
    }

    /**
     * 获取主数据接口字段映射
     */
    @GetMapping("/get")
    public Result<List<MappingFieldInfoVO>> getFields(@Valid  @NotNull(message = "主数据sourceTypeCode不可为空") String sourceTypeCode){
        return Result.ok(interfaceFieldMappingService.getFields(sourceTypeCode));
    }


    /**
     * 获取主数据接口字段映射或者模板
     */
    @GetMapping("/templateOrFields")
    public Result<List<MappingFieldInfoVO>> templateOrFields(String sourceTypeCode,Integer mappingType){
        if(mappingType == null){
            mappingType = 1;
        }
        return Result.ok(interfaceFieldMappingService.templateOrFields(sourceTypeCode,mappingType));
    }

    /**
     * 编辑主数据接口字段映射
     */
    @PostMapping("/edit")
    public Result<Boolean> editFields(@Valid @RequestBody MappingConfigParam param){
        interfaceFieldMappingService.editFields(param);
        return Result.ok(Boolean.TRUE);
    }


}

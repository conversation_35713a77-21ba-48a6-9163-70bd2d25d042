package com.imile.permission.web;

import com.github.easylog.annotation.EasyLog;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.permission.constants.PermissionLogConstant;
import com.imile.permission.domain.common.IdParam;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionVO;
import com.imile.permission.domain.role.vo.RoleListVO;
import com.imile.permission.domain.user.vo.AssociatedUserVO;
import com.imile.permission.domain.workCenter.param.NodePermissionParam;
import com.imile.permission.domain.workCenter.param.WorkCenterBindQueryParam;
import com.imile.permission.domain.workCenter.param.WorkCenterPageQueryParam;
import com.imile.permission.domain.workCenter.param.WorkCenterStep1Param;
import com.imile.permission.domain.workCenter.param.WorkCenterStep2Param;
import com.imile.permission.domain.workCenter.param.WorkCenterWithNodeParam;
import com.imile.permission.domain.workCenter.vo.SourceNodePermission;
import com.imile.permission.domain.workCenter.vo.WorkCenterDetailVO;
import com.imile.permission.domain.workCenter.vo.WorkCenterPermissionVO;
import com.imile.permission.domain.workCenter.vo.WorkCenterVO;
import com.imile.permission.domain.workCenter.vo.WorkCenterWithNodeVO;
import com.imile.permission.service.WorkCenterService;
import com.imile.resource.api.dto.ResSystemResourceTreeApiDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 工作中心控制器
 *
 * <AUTHOR>
 * @since 2023/11/14
 */
@RestController
@RequestMapping("/work/center")
public class WorkCenterController {

    @Autowired
    private WorkCenterService workCenterService;

    /**
     * 禁用工作中心
     *
     * @param idParam idParam
     */
    @PostMapping("/disable")
    @EasyLog(module = PermissionLogConstant.MODULE_FLOW_MANAGE, type = PermissionLogConstant.TYPE_DISABLED)
    public Result<Boolean> disable(@RequestBody IdParam idParam) {
        return Result.ok(workCenterService.disableWorkCenter(idParam.getId()));
    }

    /**
     * 工作中心分页查询
     *
     * @param workCenterPageQueryParam workCenterPageQueryParam
     * @return Result<PaginationResult < RolePageVO>>
     */
    @PostMapping("/page")
    public Result<PaginationResult<WorkCenterVO>> page(@RequestBody WorkCenterPageQueryParam workCenterPageQueryParam) {
        return Result.ok(workCenterService.findWorkCenterPage(workCenterPageQueryParam));
    }

    /**
     * 工作中心查询列表
     */
    @PostMapping("/list")
    public Result<List<WorkCenterVO>> list(@RequestBody WorkCenterPageQueryParam workCenterPageQueryParam) {
        return Result.ok(workCenterService.findWorkCenter(workCenterPageQueryParam));
    }

    /**
     * 保存 saveStep1: 工作中心基础配置
     *
     * @param workCenterStep1Param workCenterStep1Param
     */
    @PostMapping("/save/step1")
    @EasyLog(module = PermissionLogConstant.MODULE_FLOW_MANAGE, type = PermissionLogConstant.TYPE_ADD)
    public Result<Long> saveStep1(@RequestBody @Valid WorkCenterStep1Param workCenterStep1Param) {
        return Result.ok(workCenterService.saveWorkCenterStep1(workCenterStep1Param));
    }

    /**
     * 保存 saveStep2: 流程配置
     *
     * @param workCenterStep2Param workCenterStep2Param
     */
    @PostMapping("/save/step2")
    @EasyLog(module = PermissionLogConstant.MODULE_FLOW_MANAGE, type = PermissionLogConstant.TYPE_ADD)
    public Result<Boolean> saveStep2(@RequestBody WorkCenterStep2Param workCenterStep2Param) {
        workCenterService.saveWorkCenterStep2(workCenterStep2Param);
        return Result.ok(Boolean.TRUE);
    }


    /**
     * 保存 单个节点配置(单个节点提交)
     *
     * @param nodePermissionParam nodePermissionParam
     * @return Result<Boolean>
     */
    @PostMapping("/save/step3")
    @EasyLog(module = PermissionLogConstant.MODULE_FLOW_MANAGE, type = PermissionLogConstant.TYPE_ADD)
    public Result<Boolean> saveStep3(@RequestBody NodePermissionParam nodePermissionParam) {
        workCenterService.saveWorkCenterStep3(nodePermissionParam);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 工作中心详情
     *
     * @param id id
     * @return Result<WorkCenterDetailVO>
     */
    @GetMapping("/detail")
    public Result<WorkCenterDetailVO> detail(Long id) {
        return Result.ok(workCenterService.findWorkCenterById(id));
    }

    /**
     * 获取工作中心未拥有的角色
     *
     * @param workCenterId workCenterId
     * @param roleName     roleName
     * @return Result<List < RoleListVO>>
     */
    @GetMapping("/getRoleNotInWorkCenter")
    public Result<List<RoleListVO>> getRoleNotInWorkCenter(Long workCenterId, String roleName) {
        return Result.ok(workCenterService.getRoleNotInWorkCenter(workCenterId, roleName));
    }

    /**
     * 获取工作中心关联的角色
     *
     * @param workCenterId workCenterId
     * @return Result<List < RoleListVO>>
     */
    @GetMapping("/getRoleInWorkCenter")
    public Result<List<RoleListVO>> getRoleInWorkCenter(Long workCenterId) {
        return Result.ok(workCenterService.getRoleInWorkCenter(workCenterId));
    }

    /**
     * 获取工作中心节点的菜单和数据权限
     *
     * @param workCenterId workCenterId
     * @return Result<List < RoleListVO>>
     */
    @GetMapping("/getPermissionByWorkCenterId")
    public Result<List<SourceNodePermission>> getPermissionByWorkCenterId(@RequestParam(required = true) Long workCenterId) {
        return Result.ok(workCenterService.getPermissionByWorkCenterId(workCenterId));
    }

    /**
     * 获取流程节点的菜单
     * @param workNodeId
     * @return
     */
    @GetMapping("/getMenuTreeByNodeId")
    public Result<List<ResSystemResourceTreeApiDTO>> getMenuTreeByNodeId(@RequestParam(required = true) String workNodeId) {
        return Result.ok(workCenterService.getMenuTreeByNodeId(workNodeId));
    }

    /**
     * 获取流程节点主数据
     * @param workNodeId
     * @return
     */
    @GetMapping("/getMainDataByNodeId")
    public Result<List<SystemDataPermissionVO>> getMainDataByNodeId(@RequestParam(required = true) String workNodeId) {
        return Result.ok(workCenterService.getMainDataByNodeId(workNodeId));
    }

    /**
     * 获取工作中心权限
     * @param workCenterId
     * @return
     */
    @GetMapping("/getWorkCenterPermission")
    public Result<List<WorkCenterPermissionVO>> getWorkCenterPermission(@RequestParam(required = true) Long workCenterId) {
        return Result.ok(workCenterService.getWorkCenterPermission(workCenterId));
    }

    /**
     * 删除工作中心
     */
    @PostMapping("/delete")
    @EasyLog(module = PermissionLogConstant.MODULE_FLOW_MANAGE, type = PermissionLogConstant.TYPE_DELETE)
    public Result<Boolean> delete(@RequestBody IdParam idParam) {
        workCenterService.deleteWorkCenter(idParam.getId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 流程绑定员工列表
     * @param param
     * @return
     */
    @PostMapping("/associatedUserPage")
    public Result<PaginationResult<AssociatedUserVO>> associatedUserPage(@RequestBody
                                                                             WorkCenterBindQueryParam param) {

        PaginationResult<AssociatedUserVO> workCenterList = workCenterService.associatedUserPage(param);
        return Result.ok(workCenterList);
    }

    /**
     * 分页查询流程带节点的数据
     * @param param
     * @return
     */
    @PostMapping("/getWorkCenterWithNodePage")
    public Result<PaginationResult<WorkCenterWithNodeVO>> getWorkCenterWithNodePage(@RequestBody WorkCenterWithNodeParam param) {
        return Result.ok(workCenterService.getWorkCenterWithNodePage(param));
    }
}

package com.imile.permission.web;

import com.imile.common.result.Result;
import com.imile.permission.integration.jcasbin.EnforcerIntegration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/18
 */
@Slf4j
@RestController
@RequestMapping("enforcer")
public class EnforcerController {

    @Autowired
    private EnforcerIntegration enforcerIntegration;

    @GetMapping("clearPolicy")
    public Result<Boolean> clearPolicy() {
        enforcerIntegration.clearPolicy();
        return Result.ok();
    }


    @GetMapping("savePolicy")
    public Result<Boolean> savePolicy() {
        enforcerIntegration.savePolicy();
        return Result.ok();
    }


    @GetMapping("loadPolicy")
    public Result<Boolean> loadPolicy() {
        enforcerIntegration.loadPolicy();
        return Result.ok();
    }


    @GetMapping("deleteRole")
    public Result<Boolean> deleteRole(String role) {
        enforcerIntegration.deleteRole(role);
        return Result.ok();
    }

    @GetMapping("removeFilteredPolicy")
    public Result<Boolean> removeFilteredPolicy(int fieldIndex, String[] fieldValues) {
        return Result.ok(enforcerIntegration.removeFilteredPolicy(fieldIndex, fieldValues));
    }

    @GetMapping("addPolicy")
    public Result<Boolean> addPolicy(String[] fieldValues) {
        List<List<String>> list = Arrays.asList(
                Arrays.asList(fieldValues)
        );
        return Result.ok(enforcerIntegration.addPolicies(list));
    }

    @GetMapping("getFilteredGroupingPolicy")
    public Result<List<List<String>>> getFilteredGroupingPolicy(int fieldIndex, String[] fieldValues) {
        return Result.ok(enforcerIntegration.getFilteredGroupingPolicy(fieldIndex, fieldValues));
    }

    @GetMapping("getPermissionsForUser")
    public Result<List<List<String>>> getPermissionsForUser(String user, String[] domain) {
        return Result.ok(enforcerIntegration.getPermissionsForUser(user, domain));
    }

    @GetMapping("removeFilteredGroupingPolicy")
    public Result<Boolean> removeFilteredGroupingPolicy(int fieldIndex, String[] fieldValues) {
        return Result.ok(enforcerIntegration.removeFilteredGroupingPolicy(fieldIndex, fieldValues));
    }

    @GetMapping("addGroupingPolicy")
    public Result<Boolean> addGroupingPolicy(String[] rules) {
        List<String> list = Arrays.asList(rules);
        return Result.ok(enforcerIntegration.addGroupingPolicy(list));
    }

    @GetMapping("/noCache/addGroupingPolicy")
    public Result<Boolean> noCacheAddGroupingPolicy(String[] rules) {
        List<String> list = Arrays.asList(rules);
        return Result.ok(enforcerIntegration.noCacheAddGroupingPolicy(list));
    }

    @GetMapping("getImplicitPermissionsForUser")
    public Result<List<List<String>>> getImplicitPermissionsForUser(String user, String[] domain) {
        return Result.ok(enforcerIntegration.getImplicitPermissionsForUser(user, domain));
    }

    @GetMapping("getImplicitRolesForUser")
    public Result<List<String>> getImplicitRolesForUser(String name, String[] domain) {
        return Result.ok(enforcerIntegration.getImplicitRolesForUser(name, domain));
    }

    @GetMapping("getRolesForUser")
    public Result<List<String>> getRolesForUser(String name) {
        return Result.ok(enforcerIntegration.getRolesForUser(name));
    }

    @GetMapping("getFilteredPolicy")
    public Result<List<List<String>>> getFilteredPolicy(int fieldIndex, String[] fieldValues) {
        return Result.ok(enforcerIntegration.getFilteredPolicy(fieldIndex, fieldValues));
    }
}

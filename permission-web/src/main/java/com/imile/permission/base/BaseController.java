package com.imile.permission.base;

import com.alibaba.fastjson.JSONArray;
import com.imile.common.constant.ExceptionConstant;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.common.query.BaseQuery;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.permission.constants.RedisConstant;
import com.imile.permission.util.PermissionCollectionUtils;
import com.imile.permission.util.ReflectionUtil;
import com.imile.permission.util.UserInfoUtil;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.encoder.MD5Utils;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * BaseController
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2019/12/11 15:01
 */
@RestController
@Slf4j
public class BaseController {

    @Autowired
    public ImileRedisClient imileRedisClient;

    @Autowired
    public RedissonClient redissonClient;

    private static final String REPEATED_SUBMIT = "Repeated Submit";

    /**
     * 查询登录用户信息
     *
     * @return
     */
    public UserInfoDTO getUserInfo() {
        return UserInfoUtil.getUserInfo();
    }

    public String getLocal() {
        return UserEvnHolder.getLocalStr();
    }

    public ExcelCallBackParam /**/setExcelCallBackParam(HttpServletRequest request, BaseQuery query) {
        // excel回调返回数据
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        log.info("setExcelCallBackParam | query={}", query);

        // 业务分页入参适配
        query.setCurrentPage(callBackParam.getPageNum());
        query.setShowCount(callBackParam.getPageSize());
        query.setTotalResult(callBackParam.getTotalCount());

        // 是否查询count
        Boolean isCount = (query.getCurrentPage() == 1);
        query.setCount(isCount);
        query.setPageEnabled(true);
        dealListField(query);

        return callBackParam;
    }

    private void dealListField(BaseQuery query) {
        try {
            Class<? extends BaseQuery> aClass = query.getClass();
            List<Field> allDeclaredField = ReflectionUtil.getAllDeclaredField(aClass);
            allDeclaredField.forEach(field -> {
                Object o = ReflectionUtil.getFieldValue(query, field.getName());
                if (Objects.isNull(o)) {
                    return;
                }
                if (o instanceof Collection) {
                    List<String> oldList = JSONArray.parseArray(o.toString(), String.class);
                    String str = String.join("", oldList);
                    List<String> newList = JSONArray.parseArray(str, String.class);
                    ReflectionUtil.setFieldValue(query, field, newList);
                }
            });
        } catch (Exception e) {
            log.info("dealListField error", e);
        }
    }

    /**
     * 重复提交校验 5s
     *
     * @param object
     */
    public void repeatedSubmitCheck(Object object) {
        String key = String.format(RedisConstant.RedisPrefix.REPEATED_SUBMIT_KEY, MD5Utils.md5(String.valueOf(object)));
        boolean isSuc = imileRedisClient.setIfNotExist(key, 0, 5L);
        if (!isSuc) {
            throw BusinessException.ofI18nCode(ExceptionConstant.USER_DEFINED_MSG,
                    REPEATED_SUBMIT);
        }
    }

    /**
     * 分页对象转换
     *
     * @param originDatas
     * @param clazz
     * @param <R>
     * @param <T>
     * @return
     */
    public <R, T> PaginationResult<T> convertPage(PaginationResult<R> originDatas, Class<T> clazz) {
        if (originDatas != null) {
            PaginationResult<T> paginationResult = new PaginationResult<>();
            paginationResult.setPagination(originDatas.getPagination());
            paginationResult.setResults(PermissionCollectionUtils.convert(originDatas.getResults(), clazz));
            return paginationResult;
        }
        return null;
    }

}

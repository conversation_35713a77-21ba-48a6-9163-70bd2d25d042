package com.imile.permission.local_rpc;

import com.imile.common.result.Result;
import com.imile.permission.api.RoleManagerApi;
import com.imile.permission.api.dto.RoleAddApiDTO;
import com.imile.permission.api.dto.RoleUpdateApiDTO;
import com.imile.permission.util.RpcResultProcessor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * RoleManagerApi
 *
 * <AUTHOR>
 * @since 2023/12/14
 */
@RestController
@RequestMapping("RoleManagerApi")
public class RoleManagerApiController {

    @Reference(version = "1.0.0")
    private RoleManagerApi roleManagerApi;


    @PostMapping("/addRole")
    public Result<Long> addRole(@RequestBody RoleAddApiDTO roleAddApiDTO) {
        return Result.ok(RpcResultProcessor.process(roleManagerApi.addRole(roleAddApiDTO)));
    }

    @PostMapping("/updateRole")
    public Result<Boolean> updateRole(@RequestBody RoleUpdateApiDTO roleUpdateApiDTO) {
        return Result.ok(RpcResultProcessor.process(roleManagerApi.updateRole(roleUpdateApiDTO)));
    }

    @GetMapping("/deleteRole")
    public Result<Boolean> deleteRole(
            Long roleId,
            Long orgId,
            String userCode,
            String userName) {
        return Result.ok(RpcResultProcessor.process(roleManagerApi.deleteRole(roleId, orgId, userCode, userName)));
    }


}

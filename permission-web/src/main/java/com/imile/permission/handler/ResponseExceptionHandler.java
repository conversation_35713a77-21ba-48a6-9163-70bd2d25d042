package com.imile.permission.handler;

import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.common.result.Result;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcException;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 异常统一处理
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/7/11 15:01
 */
@ControllerAdvice
public class ResponseExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(ResponseExceptionHandler.class);


    @ResponseBody
    @ExceptionHandler(RpcException.class)
    public Result handleLocalizationException(RpcException e) {
        String message = e.getMessage();
        logger.info("业务异常:code=[{}],msg=[{}]", e.getCode(), message);
        // 审批流节点异常特殊处理
        if (e.getCode() == 22012) {
            return Result.getFailResult(String.valueOf(e.getCode()), message);
        }
        String traceId = Optional.of(TraceContext.traceId()).orElse("");
        if (StringUtils.isNotBlank(traceId)) {
            traceId = traceId.substring(traceId.lastIndexOf(".") + 1);
        }

        // 小于 100 字符，提示报错内容
        if (StringUtils.isNotBlank(message)) {
            if (message.length() < 100) {
                Result result = new Result(MsgCodeConstant.RPC_ERROR, message, traceId);
                return result;
            }
        }

        return Result.fail(MsgCodeConstant.RPC_ERROR, traceId);
    }


    @ResponseBody
    @ExceptionHandler(BusinessException.class)
    public Result handleLocalizationException(BusinessException e) {
        logger.info("业务异常:code=[{}],msg=[{}]", e.getCode(), e.getMessage());
        return Result.getFailResult(e.getCode(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result processMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        List<ObjectError> allErrors = bindingResult.getAllErrors();
        // todo 国际化类型处理
        String errorMessage = allErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(",", "[", "]"));
        return Result.getFailResult(MsgCodeConstant.SYSTEM_OP_FAILED, errorMessage);
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public Result handleException(Exception e) {
        logger.error("服务发生异常", e);
        return Result.fail(MsgCodeConstant.SYSTEM_ERROR, MsgCodeConstant.SYSTEM_ERROR);
    }

}

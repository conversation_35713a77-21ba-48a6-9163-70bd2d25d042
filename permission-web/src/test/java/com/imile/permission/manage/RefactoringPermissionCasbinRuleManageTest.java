package com.imile.permission.manage;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.domain.entity.RefactoringPermissionCasbinRuleDO;
import com.imile.permission.domain.permission.dto.PermissionDTO;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class RefactoringPermissionCasbinRuleManageTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;

    @Autowired
    EnforcerManage enforcerManage;

    @Test
    public void testApi() {
        bindingUser(10L, BusinessConstant.SYSTEM, BusinessConstant.SYSTEM);
        refactoringPermissionCasbinRuleManage.deleteRolePermission(1149069431759179776L);
        PermissionDTO permissionByRoleId = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(1L);

        refactoringPermissionCasbinRuleManage.saveRolePermission(1149069431759179776L, permissionByRoleId.getMenuPermissionDTOList(), permissionByRoleId.getDataPermissionDTOList());
        PermissionDTO permissionByRoleId1 = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(1149069431759179776L);

        Map<Long, List<Long>> fullPartiallyMenuByRoleId = refactoringPermissionCasbinRuleManage.getFullPartiallyMenuByRoleId(1L);
        Map<Long, List<Long>> fullPartiallyMenuByRoleId2 = refactoringPermissionCasbinRuleManage.getFullPartiallyMenuByRoleId(1149069431759179776L);

        Map<String, List<String>> userCodeByRoleId = refactoringPermissionCasbinRuleManage.getUserCodeByRoleId(Arrays.asList(1L));
        Map<String, List<String>> userCodeByRoleId2 = enforcerManage.getUserCodeByRoleId(Arrays.asList(1L));

        PermissionDTO permissionByUserCode = refactoringPermissionCasbinRuleManage.getPermissionByUserCode("2103534701");
        PermissionDTO permissionByUserCode2 = enforcerManage.getPermissionByUserCode("2103534701");
        System.out.println(1);

        // enforcerManage.saveRolePermission();
        // enforcerManage.deleteRolePermission();
        // enforcerManage.getPermissionByRoleId();
        // enforcerManage.getFullPartiallyMenuByRoleId();
        // enforcerManage.getUserCodeByRoleId();
        // enforcerManage.getPermissionByUserCode();
    }

    @Test
    public void test() {
        List<RefactoringPermissionCasbinRuleDO> roleAuthorizedLimit1 = refactoringPermissionCasbinRuleManage.getRoleAuthorizedLimit1(1L);
        System.out.println(roleAuthorizedLimit1);
        List<RefactoringPermissionCasbinRuleDO> roleAuthorizedLimit11 = refactoringPermissionCasbinRuleManage.getRoleAuthorizedLimit1(121212L);
        System.out.println(roleAuthorizedLimit11);
        System.out.println();
    }

    private void bindingUser(Long orgId, String userCode, String userName) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setOrgId(orgId);
        userInfoDTO.setUserCode(userCode);
        userInfoDTO.setUserName(userName);
        UcenterUtils.setUserInfo(userInfoDTO);
    }
}

package com.imile.permission.api;

import com.imile.hermes.resource.api.SysDictApi;
import com.imile.hermes.resource.dto.DictDataDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ConfigCenterConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.context.ConfigManager;
import org.junit.jupiter.api.Test;

import java.util.List;

public class SysDictApiTest {


    @Test
    public void test1() {
        RpcResult<List<DictDataDTO>> dictDataByOrgIdAndTypeAndLang = getSysDictApi().findDictDataByOrgIdAndTypeAndLang(10L, "supplierType", "en_US");
        System.out.println(dictDataByOrgIdAndTypeAndLang);
    }


    @Test
    public void test() {
        SysDictApi sysDictApi = getSysDictApi();
        RpcResult<List<DictDataDTO>> result = sysDictApi.findDictDataByTypeCodeAndLocalApi("ResourceSystem", 0L, "en_US");
        List<DictDataDTO> list = result.getResult();
        list.forEach(
                e -> System.out.println(e.getDataValue())
        );

        System.out.println(list);
    }

    public SysDictApi getSysDictApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<SysDictApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("SysDictApiTest"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://10.20.0.1:2181"));
        // Map<String, String> parameters = new HashMap<>();
        // parameters.put("include.spring.env", "false");
        // parameters.put("timeout", "1000000");
        // referenceConfig.setParameters(parameters);
        // referenceConfig.setUrl("dubbo://**********:18108");
        // referenceConfig.setUrl("dubbo://**********:18085");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(SysDictApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        ConfigCenterConfig configCenterConfig = new ConfigCenterConfig();
        configCenterConfig.setTimeout(10000000L);
        ConfigManager.getInstance().setConfigCenter(
                configCenterConfig
        );
        return referenceConfig.get();
    }

}

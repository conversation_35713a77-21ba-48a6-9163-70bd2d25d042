package com.imile.permission.redis;

import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.JSONArray;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.permission.ApplicationTest;
import com.imile.permission.constants.BusinessConstant;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023/12/28
 */
public class ImileRedisClientTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    private ImileRedisClient imileRedisClient;

    @Test
    public void test() {
        imileRedisClient.delete(BusinessConstant.REDIS_PREFIX + BusinessConstant.FULL_PERMISSION);
        JSONArray fullUserCode = imileRedisClient.get(BusinessConstant.REDIS_PREFIX + BusinessConstant.FULL_PERMISSION, JSONArray.class);
        imileRedisClient.set(BusinessConstant.REDIS_PREFIX + BusinessConstant.FULL_PERMISSION, fullUserCode);
        JSONArray fullUserCode2 = imileRedisClient.get(BusinessConstant.REDIS_PREFIX + BusinessConstant.FULL_PERMISSION, JSONArray.class);
        System.out.println();
    }

    @Test
    public void test2() {
        //JSONArray fullUserCode = new JSONArray();
        //fullUserCode.add("2103451701");
        //imileRedisClient.set(BusinessConstant.REDIS_PREFIX + BusinessConstant.FULL_PERMISSION, fullUserCode);
        JSONArray fullUserCode2 = imileRedisClient.get(BusinessConstant.REDIS_PREFIX + BusinessConstant.FULL_PERMISSION, JSONArray.class);
        System.out.println();
    }


}
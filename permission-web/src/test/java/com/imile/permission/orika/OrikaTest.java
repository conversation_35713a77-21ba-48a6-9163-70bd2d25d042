package com.imile.permission.orika;

import com.imile.permission.domain.entity.WorkCenterDO;
import com.imile.permission.domain.relation.query.SysPostRoleRelationQuery;
import com.imile.permission.util.OrikaUtil;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2023/11/10
 */
public class OrikaTest {

    @Test
    public void map() {
        SysPostRoleRelationQuery query = new SysPostRoleRelationQuery();
        query.setId(1L);
        query.setResourceType("Type");
        SysPostRoleRelationQuery map = OrikaUtil.map(query, SysPostRoleRelationQuery.class);
        String resourceType = map.getResourceType();
        System.out.println(resourceType);
        System.out.println(map);
    }

    @Test
    public void map2() {
        WorkCenterDO workCenter = null;
        WorkCenterDO obj = OrikaUtil.map(workCenter, WorkCenterDO.class);
        System.out.println(obj);
    }
}

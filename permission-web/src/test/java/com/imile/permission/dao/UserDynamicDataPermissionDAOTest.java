package com.imile.permission.dao;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.entity.UserDynamicDataPermissionDO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/6
 */

public class UserDynamicDataPermissionDAOTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    UserDynamicDataPermissionDAO userDynamicDataPermissionDAO;

    @Test
    public void test() {


    }

}

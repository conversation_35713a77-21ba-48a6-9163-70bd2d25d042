package com.imile.permission.data.ds;

import com.imile.idwork.IdWorkerUtil;
import org.junit.jupiter.api.Test;

import java.io.*;
import java.nio.charset.StandardCharsets;

public class RoleBindingTest {

    @Test
    public void test() throws Exception {
        String content = "";
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream("D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\ds\\要恢复的棋手角色.txt"), StandardCharsets.UTF_8));

        String outFile = "D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\ds\\1.txt";
        BufferedWriter bufferedWriter = getBufferedWriter(outFile);
        String s = "('%s', 'g', 'U:%s', 'R:%s'),";
        while ((content = bufferedReader.readLine()) != null) {
            bufferedWriter.write(String.format(s, IdWorkerUtil.getId(), content, "1759042275024834562"));
            bufferedWriter.newLine();
        }

        bufferedReader.close();

        bufferedWriter.flush();
        bufferedWriter.close();
    }

    public BufferedWriter getBufferedWriter(String outFile) throws Exception {
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(outFile), StandardCharsets.UTF_8);
        BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter);
        return bufferedWriter;
    }
}

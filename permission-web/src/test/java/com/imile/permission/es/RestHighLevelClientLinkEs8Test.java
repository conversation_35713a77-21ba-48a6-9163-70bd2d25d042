package com.imile.permission.es;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2025/2/14
 */
public class RestHighLevelClientLinkEs8Test {

    @Test
    public void testSelect() throws IOException {
        RestHighLevelClient restHighLevelClient = getRestHighLevelClient();
        GetRequest request = new GetRequest("idx_user_permission_api", "_doc", "**********");

        GetResponse response = restHighLevelClient.get(request, RequestOptions.DEFAULT);

        // 处理响应
        if (response.isExists()) {
            String documentId = response.getId();
            String sourceAsString = response.getSourceAsString();
            System.out.println("Document found with id " + documentId + ": " + sourceAsString);
        } else {
            System.out.println("Document not found with id " + "**********");
        }
    }

    @NotNull
    private RestHighLevelClient getRestHighLevelClient() {
        String host = "es-cn-zim3the6t000a8zej.elasticsearch.aliyuncs.com:9200";
        String username = "elastic";
        String password = "eCr779y5LVsaED8";

        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        RestClientBuilder builder = RestClient.builder(HttpHost.create(host))
                .setHttpClientConfigCallback(
                        httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                );

        RestHighLevelClient restHighLevelClient = new RestHighLevelClient(builder);
        return restHighLevelClient;
    }
}

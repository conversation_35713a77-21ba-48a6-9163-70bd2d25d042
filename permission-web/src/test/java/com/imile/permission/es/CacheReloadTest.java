package com.imile.permission.es;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imile.common.result.Result;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/2
 */
public class CacheReloadTest {

    @Test
    public void esReload() {
        String localhost = "http://localhost:80/other/pushEsWithCache?userCode=";
        String dev = "https://dev-auth.52imile.cn/permission/other/pushEsWithCache?userCode=";
        String uat = "https://uat-auth.imile.com/permission/other/pushEsWithCache?userCode=";

        String path = "/Users/<USER>/project/imile/permission/permission-web/src/test/java/com/imile/permission/es/esUserCode_01";
        long start = System.currentTimeMillis();
        List<String> userCodeList = getUserCodeList(path);
        userCodeList.forEach(userCode -> doEsReload( userCode,dev));
        long end = System.currentTimeMillis();
        System.out.printf("======" + (end - start));
    }

    @Test
    public void roleCacheReload() {
        String localhost = "http://localhost:80/other/putRoleCache?roleId=";
        String dev = "https://dev-auth.52imile.cn/permission/other/putRoleCache?roleId=";
        String uat = "https://uat-auth.imile.com/permission/other/putRoleCache?roleId=";
        String path = "/Users/<USER>/project/imile/permission/permission-web/src/test/java/com/imile/permission/es/roleCacheList";

        long start = System.currentTimeMillis();
        List<Long> roleIdList = getRoleIdList(path);
        roleIdList.forEach(roleId -> doRoleCacheReload(roleId, dev));
        long end = System.currentTimeMillis();
        System.out.printf("======" + (end - start));
    }

    @Test
    public void getUserTree() {
        String localhost = "http://localhost:80/resource/getUserTree?userCode=2103472001&system=HRMS";

        for (int i = 0; i < 100; i++) {
            long start = System.currentTimeMillis();
            doGetUserTree(localhost, i);
            long end = System.currentTimeMillis();
            System.out.printf("耗时：" + (end - start));
        }
    }

    private void doGetUserTree(String exeUrl, int i) {
        HttpResponse<String> updateResponse = Unirest.get(exeUrl)
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", "Bearer 2681f646-4327-425e-a4dd-3a7b24abb864")
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=CHN; cookieTimeZone=4; countryCode=2000001; countryName=UAE; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; ACCESS_TOKEN=Bearer%20b23ee65e-6824-496b-aea6-45fe56137715; _ga_8Y83MQKMZD=GS1.1.**********.1.1.**********.0.0.0; _ga_MTPDGX60Z3=GS1.1.1715853438.1.1.**********.0.0.0; _ga_3F6R7164X1=GS1.1.1715861119.6.0.1715861119.0.0.0; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; currentOcCode=88880008; currentOcCode=88880008; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\"}")
                .header("lang", "zh_CN")
                .header("origin", "https://uat-auth.imile.com")
                .header("priority", "u=1, i")
                .header("referer", "https://uat-auth.imile.com/")
                .header("resource-code", "undefined")
                .header("sec-ch-ua", "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"Windows\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36")
                .asString();

        String updateResponseBody = updateResponse.getBody();
        Result r2 = JSONObject.parseObject(updateResponseBody, Result.class);
        if (!"success".equals(r2.getStatus())) {
            System.out.println(updateResponseBody + "=====" + i);
        } else {
            System.out.println(" success =====" + i);

        }
    }

    private List<Long> getRoleIdList(String path) {
        List<Long> userCodeList = Lists.newArrayList();
        try {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(path), StandardCharsets.UTF_8));
            String roleId = bufferedReader.readLine();
            while (StringUtils.isNotBlank(roleId)) {
                userCodeList.add(Long.parseLong(roleId));
                roleId = bufferedReader.readLine();
            }
        } catch (IOException r) {
            throw new RuntimeException(r);
        }
        return userCodeList;
    }

    private List<String> getUserCodeList(String url) {
        List<String> userCodeList = Lists.newArrayList();
        try {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(url), StandardCharsets.UTF_8));
            String userCode = bufferedReader.readLine();
            while (StringUtils.isNotBlank(userCode)) {
                userCodeList.add(userCode);
                userCode = bufferedReader.readLine();
            }
        } catch (IOException r) {
            throw new RuntimeException(r);
        }
        return userCodeList;
    }

    private void doRoleCacheReload(Long roleId, String url) {
        String exeUrl = url + roleId;
        HttpResponse<String> updateResponse = Unirest.get(exeUrl)
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", "Bearer f3524e9b-b096-4d7c-82d2-2f7cd2984fe1")
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=CHN; cookieTimeZone=4; countryCode=2000001; countryName=UAE; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; ACCESS_TOKEN=Bearer%20b23ee65e-6824-496b-aea6-45fe56137715; _ga_8Y83MQKMZD=GS1.1.**********.1.1.**********.0.0.0; _ga_MTPDGX60Z3=GS1.1.1715853438.1.1.**********.0.0.0; _ga_3F6R7164X1=GS1.1.1715861119.6.0.1715861119.0.0.0; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; currentOcCode=88880008; currentOcCode=88880008; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\"}")
                .header("lang", "zh_CN")
                .header("origin", "https://uat-auth.imile.com")
                .header("priority", "u=1, i")
                .header("referer", "https://uat-auth.imile.com/")
                .header("resource-code", "undefined")
                .header("sec-ch-ua", "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"Windows\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36")
                .asString();

        String updateResponseBody = updateResponse.getBody();
        System.out.println(roleId + "======" + updateResponseBody);
    }

    private void doEsReload(String userCode, String url) {
        String exeUrl = url + userCode;
        HttpResponse<String> updateResponse = Unirest.get(exeUrl)
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", "Bearer f3524e9b-b096-4d7c-82d2-2f7cd2984fe1")
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=CHN; cookieTimeZone=4; countryCode=2000001; countryName=UAE; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; ACCESS_TOKEN=Bearer%20b23ee65e-6824-496b-aea6-45fe56137715; _ga_8Y83MQKMZD=GS1.1.**********.1.1.**********.0.0.0; _ga_MTPDGX60Z3=GS1.1.1715853438.1.1.**********.0.0.0; _ga_3F6R7164X1=GS1.1.1715861119.6.0.1715861119.0.0.0; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; currentOcCode=88880008; currentOcCode=88880008; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\"}")
                .header("lang", "zh_CN")
                .header("origin", "https://uat-auth.imile.com")
                .header("priority", "u=1, i")
                .header("referer", "https://uat-auth.imile.com/")
                .header("resource-code", "undefined")
                .header("sec-ch-ua", "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"Windows\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36")
                .asString();

        String updateResponseBody = updateResponse.getBody();
        Result r2 = JSONObject.parseObject(updateResponseBody, Result.class);
        System.out.println(userCode + "=======" + r2.getStatus());
    }

}

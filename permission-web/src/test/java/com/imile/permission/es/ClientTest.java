package com.imile.permission.es;

import com.alibaba.fastjson.JSON;
import com.imile.permission.api.dto.PermissionApiDTO;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.*;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.xcontent.XContentType;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
public class ClientTest {


    @Test
    public void bulkDelete() throws IOException {
        RestHighLevelClient restHighLevelClient = getRestHighLevelClient();

        BulkRequest bulkRequest = new BulkRequest();
        for (String s : list) {
            DeleteRequest request = new DeleteRequest("idx_rpc_user_manager_api_get_all_permission", "_doc", s);
            bulkRequest.add(request);
        }
        BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        System.out.println(bulk);
    }

    List<String> list = Arrays.asList(
            "D2101999802",
            "D2101999702",
            "2103642602",
            "D2101999302",
            "2103642502",
            "D2101999201",
            "D2101999101",
            "2103642401",
            "D2101999001",
            "2103642301",
            "D2101998901",
            "D2101998802",
            "2103642202",
            "D2101998502",
            "D2101998102",
            "D2101997902",
            "2103642102",
            "2103642001",
            "2103641901",
            "2103641801",
            "2103641702",
            "2103641501",
            "2103641401",
            "D2101997801",
            "2103641302",
            "D2101997701",
            "2103409901",
            "2103641202",
            "2103641101",
            "2103641001",
            "D2101997601",
            "D2101997501",
            "D2101997401",
            "D2101997301",
            "D2101997201",
            "2103640701",
            "2103640602",
            "2103640501",
            "2103640401",
            "2103640302",
            "2103640202",
            "D2101996302",
            "D2101996202",
            "2103640101",
            "2103640002",
            "2103639902",
            "2103639702",
            "2103639602",
            "2103639501",
            "D2101995701",
            "D2101995602",
            "2103639402",
            "2103639302",
            "2103639202",
            "D2101995101",
            "D2101994802",
            "D2101994702",
            "2103639001",
            "D2101994602",
            "2103638901",
            "D2101994502",
            "2103638801",
            "D2101994402",
            "2103638701",
            "2103638601",
            "D2101994301",
            "2103638501",
            "D2101994202",
            "2103638401",
            "D2101994101",
            "D2101994002",
            "2103638301",
            "D2101993902",
            "D2101993802",
            "2103638201",
            "2103638102",
            "2103638001",
            "2103637901",
            "2103637802",
            "D2101993602",
            "D2101993502",
            "D2101993402",
            "D2101993302",
            "D2101993202",
            "2103637701",
            "D2101993102",
            "D2101993002",
            "D2101992902",
            "D2101992601",
            "D2101992502",
            "D2101992401",
            "2103637501",
            "2103637402",
            "2103637301",
            "2103637202",
            "2103637102",
            "2103636902",
            "2103636802",
            "2103636602",
            "2103636502",
            "2103636402",
            "2103636302",
            "2103636201",
            "2103636101",
            "2103635902",
            "2103635802",
            "2103635701",
            "D2101992001",
            "2103635601",
            "D2101991902",
            "2103635502",
            "2103635401",
            "2103635301",
            "2103635202",
            "D2101990502",
            "D2101990402",
            "D2101990202",
            "D2101990102",
            "D2101990002",
            "D2101989901",
            "2103635001",
            "D2101989802",
            "2103634901",
            "2103634801",
            "2103634601",
            "2103634502",
            "D2101989701",
            "2103634401",
            "D2101989502",
            "D2101989402",
            "D2101989302",
            "D2101989202",
            "D2101989102",
            "D2101989002",
            "2103634302",
            "D2101988702",
            "D2101988602",
            "D2101988501",
            "D2101988401",
            "2103634202",
            "2103634102",
            "2103633802",
            "2103633702",
            "2103633602",
            "D2101988101",
            "D2101988002",
            "2103633501",
            "2103633401",
            "2103633301",
            "2103633202",
            "2103633101",
            "D2101987802",
            "2103633001",
            "2103632802",
            "2103632701",
            "2103632601",
            "2103632501",
            "2103632401",
            "2103632301",
            "D2101987601",
            "D2101987501",
            "D2101987301",
            "D2101987201",
            "2103632201",
            "D2101986801",
            "D2101986502",
            "D2101986402",
            "2103632102",
            "2103632002",
            "D2101986002",
            "D2101985902",
            "D2101985802",
            "2103631901",
            "D2101985702",
            "2103631801",
            "D2101985602",
            "D2101985502",
            "D2101985402",
            "D2101985302",
            "D2101985202",
            "D2101985102",
            "D2101985002",
            "D2101984902",
            "D2101984602",
            "D2101984502",
            "D2101984402",
            "D2101984302",
            "D2101984202",
            "2103631602",
            "2103631501",
            "2103631401",
            "D2101984102",
            "D2101984002",
            "2103631301",
            "2103631202",
            "2103631102",
            "2103631002",
            "2103630902",
            "2103630802",
            "2103630702",
            "2103630602",
            "D2101983802",
            "D2101983702",
            "D2101983602",
            "D2101983502",
            "D2101983402",
            "D2101983202",
            "2103630501",
            "2103630401",
            "D2101983102",
            "D2101983002",
            "D2101982902",
            "2103630302",
            "2103630202",
            "2103630102",
            "2103630002",
            "2103629902",
            "D2101982802",
            "2103629802",
            "D2101982702",
            "D2101982602",
            "2103629702",
            "D2101982502",
            "D2101982402",
            "D2101982302",
            "2103629602",
            "D2101982202",
            "D2101982102",
            "D2101982002",
            "2103629502",
            "2103629402",
            "2103629301",
            "D2101981702",
            "D2101981602",
            "2103629201",
            "2103629102",
            "2103629001",
            "2103628901",
            "2103628802",
            "D2101981502",
            "D2101981402",
            "2103628702",
            "D2101981302",
            "D2101981202",
            "2103628602",
            "2103628502",
            "2103628402",
            "D2101980901",
            "2103628202",
            "2103628101",
            "D2101980801",
            "2103628001",
            "2103627902",
            "2103627802",
            "D2101980702",
            "2103627702",
            "2103627602",
            "2103627502",
            "D2101980502",
            "D2101980101",
            "2103627302",
            "2103627202",
            "2103627102",
            "2103627002",
            "D2101979802",
            "D2101979702",
            "D2101979602",
            "2103626802",
            "2103626702",
            "D2101979502",
            "2103626602",
            "D2101979402",
            "D2101979302",
            "D2101979202",
            "D2101979102",
            "D2101979002",
            "D2101978902",
            "D2101978802",
            "2103626501",
            "D2101978702",
            "2103626402",
            "D2101978502",
            "2103626201",
            "2103626102",
            "D2101978402",
            "2103626002",
            "2103625902",
            "2103625802",
            "D2101978202",
            "2103625702",
            "2103625602",
            "2103625501",
            "2103625302",
            "2103625202",
            "2103625101",
            "2103625001",
            "2103624901",
            "2103624801",
            "D2101976202",
            "D2101976102",
            "D2101976002",
            "D2101975902",
            "D2101975802",
            "D2101975701",
            "D2101975602",
            "2103624501",
            "2103624401",
            "D2101974901",
            "2103624201",
            "2103624101",
            "2103623902",
            "2103623802",
            "2103623702",
            "D2101974502",
            "2103623602",
            "D2101974402",
            "2103623102",
            "D2101971202",
            "D2101971302",
            "2103622901",
            "2103622802",
            "2103622702",
            "2103622602",
            "D2101971102",
            "D2101971002",
            "2103622401",
            "2103622202",
            "2103622101",
            "2103621902",
            "2103621801",
            "2103621701",
            "2103621402",
            "2103621201",
            "D2101970702",
            "2103621102",
            "D2101970502",
            "D2101970402",
            "D2101970302",
            "D2101970202",
            "2103621002",
            "2103620802",
            "D2101969802",
            "2103620702",
            "D2101969702",
            "D2101969602",
            "2103620602",
            "2103620402",
            "2103620002",
            "2103619702",
            "2103619602",
            "2103619402",
            "2103619302",
            "2103619201",
            "2103619101",
            "2103619001",
            "2103618901",
            "2103618801",
            "2103618701",
            "2103618601",
            "2103618501",
            "2103618401",
            "2103618301",
            "2103618201",
            "2103618101",
            "D2101969102",
            "D2101969002",
            "2103618001",
            "2103617901",
            "2103617801",
            "2103617701",
            "2103617601",
            "2103617501",
            "2103617401",
            "2103617301",
            "2103617201",
            "2103617101",
            "2103617001",
            "2103616901",
            "2103616801",
            "2103616701",
            "2103616601",
            "2103616501",
            "2103616401",
            "2103616301",
            "2103616201",
            "2103616101",
            "2103616001",
            "2103615901",
            "2103615801",
            "2103615302",
            "2103615202",
            "D2101968502",
            "D2101968402",
            "D2101968302",
            "2103615101",
            "2103615002",
            "D2101968202",
            "2103614902",
            "D2101968102",
            "2103614701",
            "2103614502",
            "2103614402",
            "D2101968002",
            "2103614302",
            "2103614202",
            "D2101967902",
            "2103614102",
            "2103613901",
            "2103613801",
            "2103613701",
            "2103613601",
            "2103613501",
            "2103613401",
            "2103613301",
            "2103613201",
            "2103613101",
            "2103613001",
            "D2101967602",
            "2103612702",
            "2103612602",
            "D2101967502",
            "2103612502",
            "D2101967402",
            "D2101967302",
            "D2101967202",
            "D2101967102",
            "D2101967002",
            "D2101966902",
            "D2101966802",
            "D2101966602",
            "D2101966502",
            "2103612402",
            "D2101966402",
            "D2101966302",
            "2103612302",
            "D2101966202",
            "2103612202",
            "D2101966102",
            "D2101966002",
            "D2101965902",
            "D2101965802",
            "D2101965702",
            "2103612102",
            "2103612002",
            "2103611901",
            "2103611802",
            "D2101965602",
            "21032746",
            "2103611702",
            "2103611401",
            "2103611301",
            "2103611201",
            "2103611101",
            "2103611001",
            "2103610901",
            "2103610801",
            "2103610701",
            "2103610601",
            "2103610501",
            "2103610401",
            "2103610301",
            "D2101965302",
            "D2101965202",
            "2103610201",
            "D2101965102",
            "D2101965002",
            "D2101964902",
            "2103610101",
            "D2101964802",
            "2103610002",
            "D2101964702",
            "D2101964602",
            "D2101964502",
            "D2101964402",
            "D2101964302",
            "D2101964202",
            "D2101964102",
            "D2101964002",
            "2103609702",
            "2103609602",
            "D2101963901",
            "D2101963801",
            "2103609501",
            "2103609402",
            "D2101963702",
            "2103609302",
            "D2101963402",
            "D2101963302",
            "D2101963102",
            "D2101962802",
            "2103609202",
            "D2101962702",
            "D2101962502",
            "2103609101",
            "2103609001",
            "2103608901",
            "2103608802",
            "D2101962202",
            "2103608702",
            "D2101961701",
            "D2101961602",
            "2103608502",
            "D2101960302",
            "D2101960102",
            "2103608402",
            "2103608302",
            "2103608202",
            "2103608102",
            "2103608002",
            "D2101959802",
            "D2101959702",
            "D2101959602",
            "D2101959401",
            "2103607901",
            "2103607801",
            "D2101959202",
            "2103607702",
            "2103607602",
            "D2101959102",
            "D2101959002",
            "D2101958902",
            "2103607502",
            "2103607401",
            "2103607302",
            "2103607201",
            "D2101958602",
            "2103607102",
            "2103607002",
            "2103606901",
            "2103606802",
            "2103606702",
            "2103606602",
            "2103606502",
            "D2101958402",
            "D2101958302",
            "D2101958202",
            "2103606302",
            "2103606202",
            "2103606102",
            "2103606002",
            "2103605901",
            "2103605801",
            "2103605701",
            "2103605601",
            "2103605501",
            "2103605401",
            "D2101958001",
            "D2101957901",
            "D2101957801",
            "D2101957701",
            "D2101957601",
            "2103605302",
            "D2101957501",
            "D2101957401",
            "2103605201",
            "2103605101",
            "D2101957301",
            "2103605002",
            "2103604702",
            "2103604601",
            "D2101957102",
            "2103604501",
            "D2101957002",
            "2103604402",
            "2103604301",
            "D2101956802",
            "2103604201",
            "2103604101",
            "2103604002",
            "2103603901",
            "2103603802",
            "2103603701",
            "D2101956602",
            "2103603501",
            "2103603401",
            "2103603302",
            "D2101955702",
            "2103603201",
            "2103603101",
            "2103603002",
            "D2101955602",
            "D2101955502",
            "2103602902",
            "D2101955202",
            "2103602702",
            "D2101955102",
            "D2101955002",
            "2103602602",
            "D2101954902",
            "D2101954802",
            "D2101954702",
            "2103602502",
            "2103602401",
            "2103602302",
            "2103601902",
            "D2101954502",
            "D2101954402",
            "2103601702",
            "2103601601",
            "2103601501",
            "2103601101",
            "2103600601",
            "2103599701",
            "2103599301",
            "2103598801",
            "2103597601",
            "2103596801",
            "2103596501",
            "2103596401",
            "2103594201",
            "2103594101",
            "2103593801",
            "2103593701",
            "2103592901",
            "2103592701",
            "2103591101",
            "2103590401",
            "2103588201",
            "2103587301",
            "2103586901",
            "2103586601",
            "2103586301",
            "2103586201",
            "2103579301",
            "2103579201",
            "2103517701",
            "2103517101",
            "2103514101",
            "2103512201",
            "2103512001",
            "2103511801",
            "2103511701",
            "2103511501",
            "2103511301",
            "2103511101",
            "2103510801",
            "2103510701",
            "2103509601",
            "2103509401",
            "2103508501",
            "2103503501",
            "2103502701",
            "2103500601",
            "2103498801",
            "2103496501",
            "2103496201",
            "2103496101",
            "2103495501",
            "2103495301",
            "2103492901",
            "2103492801",
            "2103492701",
            "2103492401",
            "2103492201",
            "2103491701",
            "2103491401",
            "2103489601",
            "2103489501",
            "2103489401",
            "2103489201",
            "2103489101",
            "2103489001",
            "2103488901",
            "2103488601",
            "2103488501",
            "2103487501",
            "2103486601",
            "2103486201",
            "2103484601",
            "2103483901",
            "2103482001",
            "2103480201",
            "D2101797401",
            "D2101797301",
            "D2101797201",
            "D2101797101",
            "D2101797001",
            "D2101796901",
            "D2101796801",
            "D2101796701",
            "D2101796601",
            "D2101796501",
            "D2101796401",
            "D2101796301",
            "D2101796101",
            "D2101796001",
            "D2101795901",
            "D2101795801",
            "D2101795701",
            "D2101795601",
            "D2101795501",
            "D2101795401",
            "D2101795301",
            "D2101795201",
            "D2101795101",
            "D2101795001",
            "D2101794901",
            "D2101794801",
            "D2101794701",
            "D2101794601",
            "D2101794501",
            "D2101794401",
            "D2101794301",
            "D2101794201",
            "D2101794101",
            "D2101794001",
            "D2101793901",
            "D2101793801",
            "D2101793701",
            "D2101793601",
            "D2101793501",
            "D2101793401",
            "D2101793301",
            "D2101793201",
            "D2101793101",
            "D2101792801",
            "D2101792701",
            "D2101792501",
            "D2101792401",
            "D2101792301",
            "D2101791901",
            "D2101791801",
            "D2101791701",
            "D2101791601",
            "D2101791501",
            "D2101791401",
            "D2101791301",
            "D2101791201",
            "D2101790801",
            "D2101790601",
            "D2101790401",
            "D2101790301",
            "D2101790001",
            "2103479101",
            "D2101789501",
            "2103478701",
            "2103478601",
            "2103478501",
            "2103478401",
            "2103478301",
            "2103478201",
            "2103478101",
            "D2101789301",
            "D2101789201",
            "D2101789101",
            "2103478001",
            "D2101789001",
            "D2101788901",
            "D2101788801",
            "D2101788701",
            "D2101788601",
            "D2101788401",
            "2103477901",
            "D2101788301",
            "D2101788001",
            "D2101787901",
            "D2101787801",
            "D2101787701",
            "D2101787601",
            "D2101787401",
            "D2101787301",
            "D2101787201",
            "D2101787101",
            "D2101786901",
            "D2101786801",
            "D2101786501",
            "D2101785801",
            "D2101785701",
            "2103474601",
            "D2101785501",
            "D2101785301",
            "D2101785201",
            "D2101785101",
            "D2101785001",
            "D2101784901",
            "2103474501",
            "D2101784501",
            "D2101784401",
            "D2101784101",
            "D2101784001",
            "D2101783601",
            "D2101783501",
            "D2101783401",
            "D2101783301",
            "D2101783201",
            "D2101783101",
            "D2101783001",
            "D2101782801",
            "D2101782601",
            "D2101782501",
            "D2101782201",
            "D2101782001",
            "D2101781901",
            "D2101781801",
            "D2101781701",
            "D2101781101",
            "D2101781001",
            "D2101780801",
            "D2101780401",
            "D2101780301",
            "D2101780201",
            "D2101780101",
            "D2101780001",
            "D2101779901",
            "D2101779801",
            "D2101779701",
            "D2101779601",
            "D2101779501",
            "D2101779401",
            "D2101779301",
            "D2101779201",
            "D2101779001",
            "D2101778801",
            "D2101778701",
            "D2101778601",
            "D2101778401",
            "D2101778301",
            "D2101778201",
            "D2101778101",
            "D2101778001",
            "D2101777901",
            "D2101777801",
            "D2101777601",
            "D2101777501",
            "D2101777401",
            "D2101777301",
            "D2101777201",
            "D2101777101",
            "D2101776901",
            "D2101776801",
            "D2101776701",
            "D2101776601",
            "D2101776501",
            "D2101776401",
            "D2101776301",
            "D2101775501",
            "2103471201",
            "D2101775301",
            "D2101775201",
            "D2101774501",
            "D2101774001",
            "D2101773601",
            "D2101773501",
            "D2101773001",
            "D2101771501",
            "D2101769901",
            "D2101769801",
            "D2101769401",
            "D2101769301",
            "D2101769101",
            "D2101768501",
            "D2101768301",
            "D2101767901",
            "D2101767701",
            "D2101767501",
            "D2101767201",
            "D2101767101",
            "D2101767001",
            "D2101766901",
            "D2101766701",
            "D2101766601",
            "2103468701",
            "D2101764601",
            "2103468601",
            "D2101763701",
            "D2101763401",
            "D2101762901",
            "D2101762401",
            "D2101762301",
            "D2101761701",
            "D2101761601",
            "2103467401",
            "2103467301",
            "2103467201",
            "2103467101",
            "2103467001",
            "2103466901",
            "2103466801",
            "2103466701",
            "2103466501",
            "D2101761101",
            "D2101761001",
            "D2101760901",
            "2103466101",
            "D2101759801",
            "D2101759601",
            "2103465301",
            "D2101759401",
            "D2101759301",
            "D2101759201",
            "D2101759101",
            "D2101759001",
            "D2101758901",
            "D2101758801",
            "D2101758701",
            "D2101758401",
            "2103464901",
            "2103464701",
            "D2101758201",
            "D2101758101",
            "D2101758001",
            "D2101757901",
            "D2101757801",
            "D2101757701",
            "D2101757601",
            "D2101757501",
            "2103464501",
            "2103464401",
            "D2101757401",
            "D2101757301",
            "D2101757201",
            "2103464301",
            "D2101757101",
            "D2101756901",
            "D2101756801",
            "D2101756701",
            "D2101756601",
            "D2101756501",
            "D2101756401",
            "D2101756301",
            "D2101756201",
            "D2101756101",
            "D2101756001",
            "D2101755901",
            "D2101755601",
            "D2101755201",
            "D2101755001",
            "2103463801",
            "D2101754801",
            "D2101754701",
            "D2101754601",
            "D2101754401",
            "D2101754301",
            "D2101753901",
            "D2101753701",
            "D2101751001",
            "D2101750701",
            "D2101748801",
            "D2101748401",
            "D2101748301",
            "D2101748101",
            "D2101748001",
            "D2101747901",
            "D2101747701",
            "D2101747601",
            "D2101747501",
            "2103462201",
            "D2101747301",
            "D2101747201",
            "D2101747001",
            "D2101746901",
            "D2101746701",
            "D2101746601",
            "D2101746401",
            "D2101745701",
            "D2101745201",
            "D2101744901",
            "2103460101",
            "2103460001",
            "D2101744501",
            "D2101744401",
            "2103459101",
            "2103458901",
            "2103458601",
            "D2101743901",
            "D2101743801",
            "D2101743701",
            "D2101743501",
            "D2101743401",
            "D2101743001",
            "D2101742901",
            "D2101742801",
            "D2101742601",
            "D2101742301",
            "D2101742201",
            "D2101742101",
            "D2101742001",
            "2103457401",
            "D2101741201",
            "D2101741101",
            "2103456801",
            "D2101741001",
            "D2101740801",
            "2103456201",
            "D2101739501",
            "D2101739401",
            "D2101739301",
            "D2101739201",
            "D2101739101",
            "D2101738801",
            "2103454701",
            "2103454601",
            "2103454501",
            "2103454401",
            "2103454301",
            "2103454201",
            "2103453501",
            "D2101731201",
            "D2101731101",
            "D2101730701"

    );

    @Test
    public void test2() {
        try {
            MultiGetRequest request = new MultiGetRequest();
            request.add("idx_rpc_user_manager_api_get_all_permission", "_doc", "D2101999802");
            MultiGetResponse mget = getRestHighLevelClient().mget(request, RequestOptions.DEFAULT);

            MultiGetItemResponse[] responses = mget.getResponses();
            for (MultiGetItemResponse respons : responses) {
                GetResponse response = respons.getResponse();
                String sourceAsString = response.getSourceAsString();
                try {
                    PermissionApiDTO permissionApiDTO = permissionApiDTO = JSON.parseObject(sourceAsString, PermissionApiDTO.class);
                    System.out.println(permissionApiDTO.getDataPermissionDTOList().get(0).getDataCodeList());
                } catch (Exception e) {
                    System.out.println(sourceAsString);
                }
            }
            // 处理响应
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    @Test
    public void test() {
        try {
            GetRequest request = new GetRequest("idx_rpc_user_manager_api_get_all_permission", "_doc", "2101440111501");
            GetResponse response = getRestHighLevelClient().get(request, RequestOptions.DEFAULT);
            System.out.println(response);
            // 处理响应
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void testSelect() throws IOException {
        RestHighLevelClient restHighLevelClient = getRestHighLevelClient();
        GetRequest request = new GetRequest("idx_rpc_user_manager_api_get_all_permission", "_doc", "2101440111501");

        GetResponse response = restHighLevelClient.get(request, RequestOptions.DEFAULT);

        // 处理响应
        if (response.isExists()) {
            String documentId = response.getId();
            String sourceAsString = response.getSourceAsString();
            System.out.println("Document found with id " + documentId + ": " + sourceAsString);
        } else {
            System.out.println("Document not found with id " + "2103451701");
        }
    }

    @Test
    public void testDelete() throws IOException {
        RestHighLevelClient restHighLevelClient = getRestHighLevelClient();
        DeleteRequest request = new DeleteRequest("permission", "_doc", "PymKVY4BsuqnhlFexTZY");
        DeleteResponse response = restHighLevelClient.delete(request, RequestOptions.DEFAULT);
        // 处理响应
        String indexName = response.getIndex();
        String deletedId = response.getId();
        System.out.println("Document deleted from index " + indexName + " with id " + deletedId);
    }


    @Test
    public void testInsert() throws IOException {
        String json = "{\n" +
                "  \"user_permission\": \"sample_permission_data2\"\n" +
                "}";
        RestHighLevelClient restHighLevelClient = getRestHighLevelClient();
        IndexRequest request = new IndexRequest("permission")
                .id("2103451701")  // 指定自定义 ID
                .type("_doc")
                .source(json, XContentType.JSON);

        IndexResponse response = restHighLevelClient.index(request, RequestOptions.DEFAULT);

        // 处理响应
        String indexName = response.getIndex();
        String id = response.getId();
        if (response.getResult() == DocWriteResponse.Result.CREATED) {
            System.out.println("Document created in index " + indexName + " with id " + id);
        } else if (response.getResult() == DocWriteResponse.Result.UPDATED) {
            System.out.println("Document updated in index " + indexName + " with id " + id);
        }
    }


    @Test
    public void testMatchAllQuery() throws IOException {
        RestHighLevelClient restHighLevelClient = getRestHighLevelClient();
        SearchRequest searchRequest = new SearchRequest("permission"); // 替换为你的索引名称
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(QueryBuilders.matchAllQuery()); // 使用match_all查询，匹配所有文档
        searchRequest.source(sourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        SearchHits hits = searchResponse.getHits();
        SearchHit[] hitArr = hits.getHits();
        for (SearchHit hit : hitArr) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            Object o = sourceAsMap.get("user_permission");
            System.out.println(o);
        }

        System.out.println(searchResponse);

    }

    @NotNull
    private RestHighLevelClient getRestHighLevelClient() {
        String host = "*********:9200";
        String username = "elastic";
        String password = "imile.20202020";

        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        RestClientBuilder builder = RestClient.builder(HttpHost.create(host))
                .setHttpClientConfigCallback(
                        httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                );

        RestHighLevelClient restHighLevelClient = new RestHighLevelClient(builder);
        return restHighLevelClient;
    }


}

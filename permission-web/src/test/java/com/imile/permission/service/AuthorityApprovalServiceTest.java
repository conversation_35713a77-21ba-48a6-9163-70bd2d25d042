package com.imile.permission.service;

import cn.hutool.core.net.NetUtil;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.applicationApprove.param.PermissionAddApplyParam;
import com.imile.permission.domain.applicationApprove.query.AuthorityApprovalPageQuery;
import com.imile.permission.domain.applicationApprove.query.AuthorityApprovalQuery;
import com.imile.permission.domain.applicationApprove.vo.ApplicationRecordVOListVO;
import com.imile.permission.domain.entity.AuthorityApprovalDO;
import com.imile.permission.manage.AuthorityApprovalManage;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
public class AuthorityApprovalServiceTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    AuthorityApprovalService authorityApprovalService;

    @Autowired
    AuthorityApprovalManage authorityApprovalManage;

    @Test
    public void testAgreeProcessMenuId() {
        AuthorityApprovalDO byId = authorityApprovalManage.getById(1809077227691425795L);
        authorityApprovalService.agreeProcessMenuId(null, byId);
    }

    @Test
    public void test() {
        ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = new ApprovalPushStatusMsgDTO();
        approvalPushStatusMsgDTO.setApprovalId(1200886947409567744L);
        approvalPushStatusMsgDTO.setBizId("1776883100017074177");
        approvalPushStatusMsgDTO.setStatus(2);
        approvalPushStatusMsgDTO.setApprovalPushStatusType("APPROVAL");
        authorityApprovalService.authorityApprovalMqHandler(approvalPushStatusMsgDTO);
    }

    @Test
    public void testDetail() {
        authorityApprovalService.detail(1774631565150728194L, 1L);
    }

    @Test
    public void testGetApplicationRecordList() {
        AuthorityApprovalQuery authorityApprovalQuery = getAuthorityApprovalQuery();
        ApplicationRecordVOListVO applicationRecordList = authorityApprovalService.getApplicationRecordList(authorityApprovalQuery);
        authorityApprovalQuery.setUserCode("11");
        authorityApprovalService.getApplicationRecordList(authorityApprovalQuery);
        AuthorityApprovalPageQuery authorityApprovalPageQuery = new AuthorityApprovalPageQuery();
        authorityApprovalService.getApplicationRecordPage(authorityApprovalPageQuery);
        authorityApprovalPageQuery.setUserCode("22");
        authorityApprovalService.getApplicationRecordPage(authorityApprovalPageQuery);

    }


    @Test
    public void testApply() {
        try {
            authorityApprovalService.addApply(getPermissionApplyParam());
        } catch (Exception e) {
            e.printStackTrace();
        }
        //
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setUserCode(null));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setUserId(null));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setRoleId(null));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setEffectiveTimeDay(null));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setReason(null));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setPermissionApplyType(null));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setPermissionApplyType(2));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        //
        // try {
        //    authorityApprovalManage.apply(getPermissionApplyParam().setEffectiveTimeDay(10));
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}


    }

    public AuthorityApprovalQuery getAuthorityApprovalQuery() {
        AuthorityApprovalQuery authorityApprovalQuery = new AuthorityApprovalQuery();
        return authorityApprovalQuery;
    }

    public PermissionAddApplyParam getPermissionApplyParam() {
        return new PermissionAddApplyParam()
                .setUserCode("2103451701")
                .setUserId(1134921895645290496L)
                .setRoleId(1L)
                .setBusinessType("HR")
                .setEffectiveTimeDay(0)
                .setReason("申请角色")
                ;
    }
}

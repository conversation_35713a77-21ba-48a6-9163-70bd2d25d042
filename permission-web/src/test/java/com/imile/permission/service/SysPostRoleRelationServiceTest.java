package com.imile.permission.service;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.relation.param.BatchBindingParam;
import com.imile.permission.domain.relation.vo.SysPostRoleRelationDetailVO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/11/10
 */
public class SysPostRoleRelationServiceTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }


    @Autowired
    SysPostRoleRelationService sysPostRoleRelationService;

    @Test
    public void testBatchBindingRole() {
        BatchBindingParam batchBindingParam = new BatchBindingParam();
        batchBindingParam.setPostIdList(Arrays.asList(1001L, 1002L, 1003L));
        batchBindingParam.setRoleIdList(Arrays.asList(1L, 2L));
        sysPostRoleRelationService.batchBindingRoleForPost(batchBindingParam);
    }

    @Test
    public void test() {
    }

    @Test
    public void testGetSysPostRoleRelationDetail() {
        SysPostRoleRelationDetailVO sysPostRoleRelationDetail = sysPostRoleRelationService.postRoleDetail(1L);
        System.out.println(sysPostRoleRelationDetail);
    }


}

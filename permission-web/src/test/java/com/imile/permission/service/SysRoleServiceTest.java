package com.imile.permission.service;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.role.vo.SysRoleResultVO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023/11/10
 */
public class SysRoleServiceTest extends ApplicationTest {
    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    SysRoleService sysRoleService;

    @Test
    public void testDisable() {
        sysRoleService.disableRole(1L);
    }

    @Test
    public void testGetById() {
        SysRoleResultVO vo = sysRoleService.findRoleByRoleId(1L);
        System.out.println(vo);
    }

    @Test
    public void testDelete() {
        sysRoleService.deleteRole(1L);
    }

}

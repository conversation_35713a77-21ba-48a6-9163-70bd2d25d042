package com.imile.permission._init;

import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.imile.common.result.Result;
import com.imile.permission.ApplicationTest;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.SysRoleDao;
import com.imile.permission.domain.entity.CasbinRuleDO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.mapper.CasbinRuleMapper;
import com.imile.permission.mapper.SysRoleMapper;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/19
 */
public class ProEnv extends ApplicationTest {


    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
        System.setProperty("spring.datasource.driver-class-name", "com.mysql.cj.jdbc.Driver");
        System.setProperty("spring.datasource.url", "**********************************************************************************************************************************");
        System.setProperty("spring.datasource.username", "root");
        System.setProperty("spring.datasource.password", "123456");
    }

    @Autowired
    SysRoleDao sysRoleDao;

    @Autowired
    SysRoleMapper sysRoleMapper;

    @Autowired
    CasbinRuleMapper casbinRuleMapper;

    @Test
    public void saveRoleMenu() {
        Map<String, TreeNodeContext> map = new HashMap<>();
        Result menuTree = getMenuTree("Bearer 6db55fd4-5c40-4096-99fa-133686c552c0");
        JSONArray resultObject = (JSONArray) menuTree.getResultObject();
        List<ResSystemResourceTree> resSystemResourceTrees = resultObject.toJavaList(ResSystemResourceTree.class);
        for (ResSystemResourceTree resSystemResourceTree : resSystemResourceTrees) {
            // menuId
            String nodeId = resSystemResourceTree.getNodeId();
            // systemPlatform
            String resourceCode = resSystemResourceTree.getResourceCode();
            TreeNodeContext treeNodeContext = new TreeNodeContext();
            treeNodeContext.setMenuId(Long.valueOf(nodeId));
            treeNodeContext.setCurrentMenuId(Long.valueOf(nodeId));
            treeNodeContext.setSystemPlatform(resourceCode);
            map.put(nodeId, treeNodeContext);
            List<ResSystemResourceTree> children = resSystemResourceTree.getChildren();
            processChildren(children, resourceCode, nodeId, map);
        }
        List<SysRoleDO> list = sysRoleDao.list();
        for (SysRoleDO roleDO : list) {
            Long roleId = roleDO.getId();
            Result roleMenu = getRoleMenu(roleId, "Bearer 6db55fd4-5c40-4096-99fa-133686c552c0");

            JSONArray roleMenuArr = (JSONArray) roleMenu.getResultObject();
            if (CollectionUtils.isNotEmpty(roleMenuArr)) {
                List<String> roleMenuList = roleMenuArr.toJavaList(String.class);
                List<CasbinRuleDO> casList = roleMenuList.stream()
                        .map(
                                e -> {
                                    CasbinRuleDO casbinRuleDO = new CasbinRuleDO();
                                    casbinRuleDO.setPtype("p");
                                    casbinRuleDO.setV0(BusinessConstant.CASBIN_R_COLON + roleId);
                                    TreeNodeContext treeNodeContext = map.get(e);
                                    if (Objects.nonNull(treeNodeContext)) {
                                        casbinRuleDO.setV1(BusinessConstant.CASBIN_SP_COLON + treeNodeContext.getSystemPlatform());
                                        casbinRuleDO.setV2(BusinessConstant.CASBIN_RM_COLON + treeNodeContext.getMenuId());
                                        casbinRuleDO.setV3(BusinessConstant.CASBIN_M_COLON + treeNodeContext.getCurrentMenuId());
                                        casbinRuleDO.setV4(BusinessConstant.POUND);
                                        casbinRuleDO.setV5(BusinessConstant.POUND);
                                    } else {
                                        casbinRuleDO.setV1(BusinessConstant.CASBIN_SP_COLON + "-1");
                                        casbinRuleDO.setV2(BusinessConstant.CASBIN_RM_COLON + "-1");
                                        casbinRuleDO.setV3(BusinessConstant.CASBIN_M_COLON + e);
                                        casbinRuleDO.setV4(BusinessConstant.POUND);
                                        casbinRuleDO.setV5(BusinessConstant.POUND);
                                    }
                                    return casbinRuleDO;

                                }
                        ).collect(Collectors.toList());
                casbinRuleMapper.insertCasbinRuleList(casList);
            }
        }
        System.out.println();
    }

    private void processChildren(List<ResSystemResourceTree> children, String systemPlatform, String rootMenuId, Map<String, TreeNodeContext> map) {
        if (CollectionUtils.isNotEmpty(children)) {
            for (ResSystemResourceTree child : children) {
                String nodeId = child.getNodeId();
                TreeNodeContext treeNodeContext = new TreeNodeContext();
                treeNodeContext.setMenuId(Long.valueOf(rootMenuId));
                treeNodeContext.setCurrentMenuId(Long.valueOf(nodeId));
                treeNodeContext.setSystemPlatform(systemPlatform);
                map.put(nodeId, treeNodeContext);
                List<ResSystemResourceTree> nested = child.getChildren();
                if (CollectionUtils.isNotEmpty(nested)) {
                    processChildren(nested, systemPlatform, rootMenuId, map);
                }
            }
        }
    }


    @Test
    public void saveRole() {
        Integer current = 1;
        String authorization = "Bearer 6db55fd4-5c40-4096-99fa-133686c552c0";
        saveRole(current, authorization);
    }

    private void saveRole(Integer current, String authorization) {
        Result result = getRoleResult(current, authorization);
        JSONObject resultObject = (JSONObject) result.getResultObject();
        Integer totalPage = resultObject.getJSONObject("pagination").getInteger("totalPage");
        JSONArray array = resultObject.getJSONArray("results");
        List<PermRoleManageListDTO> list = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            PermRoleManageListDTO object = array.getObject(i, PermRoleManageListDTO.class);
            list.add(object);
        }
        List<SysRoleDO> roleList = list.stream()
                .map(e -> {
                    SysRoleDO roleDO = new SysRoleDO();
                    roleDO.setId(e.getId());
                    roleDO.setRoleName(e.getRoleName());
                    return roleDO;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(roleList)) {
            sysRoleMapper.insertRoleList(roleList);
        }
        if (totalPage > current) {
            saveRole(++current, authorization);
        }

    }

    private Result getRoleResult(Integer current, String authorization) {
        HttpResponse<String> response = Unirest.post("https://scs.imile.com/hermes/perm/role/queryRoles")
                .header("authority", "dev-scs.52imile.cn")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", authorization)
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=UAE; userCountry=UAE; cookieTimeZone=4; countryName=UAE; countryCode=C115; countryName=UAE; ACCESS_TOKEN=Bearer%207405207a-6107-4f82-a234-1c6ff4b114d2; ACCESS_TOKEN_SAS=Bearer%207405207a-6107-4f82-a234-1c6ff4b114d2; UserInfo={%22password%22:%22$2a$10$XcYcAnCOG0s8MuAlsgcsqOXLuo41zc7RRR1tHrsBaArWaWqX4ekku%22%2C%22username%22:%22test-neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1134922065288314900%2C%22mobile%22:%22%22%2C%22userCode%22:%************%22%2C%22userName%22:%22test-neil%22%2C%22orgId%22:10%2C%22ocId%22:788850620139442200%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:null%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%22S2102353%22%2C%22secondType%22:null%2C%22vendorCode%22:%*********%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:null%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; TIMEZONE=+4; TIMEZONE_COUNTRY=UAE; timezone=+4; timezoneCountry=UAE; LANG=zh_CN; _ga_3F6R7164X1=GS1.1.**********.115.0.**********.0.0.0; IMILE_ACCESS_TOKEN=7405207a-6107-4f82-a234-1c6ff4b114d2; settleOrg={%22id%22:%22823321544150220801%22%2C%22orgId%22:%2210%22%2C%22companyOrgId%22:%2210%22%2C%22downstreamOrgIds%22:%2211%2C34931%22%2C%22companyName%22:%22imile%20Delivery%20Services%20LLC%22%2C%22companyShortName%22:%22iMile%22%2C%22registerCountry%22:%22UAE%22%2C%22isSettleCenter%22:1%2C%22isSettleCenterDesc%22:null%2C%22businessArea%22:%22KSA%2CUAE%2CCHL%2CCHN%2CMEX%2CKWT%2CBHR%2COMN%2CJOR%2CMAR%2CQAT%2CRSA%2CBRA%2CAUS%2CLBN%2CTUR%2CITA%22%2C%22dutyParagraph%22:%22chensr.110.112%22%2C%22companyStatus%22:%22Working%22%2C%22companyStatusDesc%22:null%2C%22contractTemplate%22:%22[{%5C%22fileName%5C%22:%5C%22%E4%B8%AD%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%E6%94%B9.pdf%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321005852778496/%E4%B8%AD%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%E6%94%B9.pdf%5C%22%2C%5C%22fileType%5C%22:%5C%22pdf%5C%22%2C%5C%22langType%5C%22:%5C%22ch-en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321544325914625/%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileType%5C%22:%5C%22png%5C%22%2C%5C%22langType%5C%22:%5C%22en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321612219113473/%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileType%5C%22:%5C%22png%5C%22%2C%5C%22langType%5C%22:%5C%22ar-en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E8%8B%B1%E8%A5%BF%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C.docx%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308151115336002739793921/%E6%88%91%E6%98%AF%E8%8B%B1%E8%A5%BF%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C.docx%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22%2C%5C%22langType%5C%22:%5C%22en-es%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22templateType%5C%22:%5C%22MEX%5C%22%2C%5C%22originalFileName%5C%22:%5C%22%E3%80%82%E3%80%82%E3%80%82%E3%80%82.docx%5C%22%2C%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E6%A0%87%E5%87%86MEX%E7%BA%AF%E8%8B%B1%E5%90%88%E5%90%8C.pdf%5C%22%2C%5C%22imageUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/7/202307041100145345423380480/%E3%80%82%E3%80%82%E3%80%82%E3%80%82.docx%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2022/5/20220509947605666569306112/%E6%88%91%E6%98%AF%E6%A0%87%E5%87%86MEX%E7%BA%AF%E8%8B%B1%E5%90%88%E5%90%8C.pdf%5C%22%2C%5C%22langType%5C%22:%5C%22en%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22}]%22%2C%22contractTemplateList%22:null%2C%22accountSetCode%22:%2210%22%2C%22comprehensiveBaseCurrency%22:%22AED%22%2C%22decimalPlaces%22:%2206%22}; cookieTimeZone=4; countryCode=C115; IMILE_TIME_ZONE=+4; page_key=SCSRoleManagement; LANG=zh_CN")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"**********\",\"moduleId\":10007,\"client\":\"pc\",\"settleOrgId\":\"10\",\"sysName\":\"TMS\"}")
                .header("lang", "zh_CN")
                .header("origin", "https://dev-scs.52imile.cn")
                .header("referer", "https://dev-scs.52imile.cn/")
                .header("request-token", "")
                .header("resource-code", "SCSRoleManagement")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("sys-name", "SCS")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .body("{\n    \"showCount\": 20,\n    \"currentPage\": " + current + ",\n    \"roleName\": \"\"\n}")
                .asString();

        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        return result;
    }

    private Result getMenuTree(String authorization) {
        HttpResponse<String> response = Unirest.get("https://scs.imile.com/hermes/res/orgResource/getTree?isRemoveAction=true")
                .header("authority", "dev-scs.52imile.cn")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", authorization)
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=UAE; userCountry=UAE; cookieTimeZone=4; countryName=UAE; countryCode=C115; countryName=UAE; ACCESS_TOKEN=Bearer%207405207a-6107-4f82-a234-1c6ff4b114d2; ACCESS_TOKEN_SAS=Bearer%207405207a-6107-4f82-a234-1c6ff4b114d2; TIMEZONE=+4; TIMEZONE_COUNTRY=UAE; timezone=+4; timezoneCountry=UAE; IMILE_ACCESS_TOKEN=7405207a-6107-4f82-a234-1c6ff4b114d2; settleOrg={%22id%22:%22823321544150220801%22%2C%22orgId%22:%2210%22%2C%22companyOrgId%22:%2210%22%2C%22downstreamOrgIds%22:%2211%2C34931%22%2C%22companyName%22:%22imile%20Delivery%20Services%20LLC%22%2C%22companyShortName%22:%22iMile%22%2C%22registerCountry%22:%22UAE%22%2C%22isSettleCenter%22:1%2C%22isSettleCenterDesc%22:null%2C%22businessArea%22:%22KSA%2CUAE%2CCHL%2CCHN%2CMEX%2CKWT%2CBHR%2COMN%2CJOR%2CMAR%2CQAT%2CRSA%2CBRA%2CAUS%2CLBN%2CTUR%2CITA%22%2C%22dutyParagraph%22:%22chensr.110.112%22%2C%22companyStatus%22:%22Working%22%2C%22companyStatusDesc%22:null%2C%22contractTemplate%22:%22[{%5C%22fileName%5C%22:%5C%22%E4%B8%AD%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%E6%94%B9.pdf%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321005852778496/%E4%B8%AD%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%E6%94%B9.pdf%5C%22%2C%5C%22fileType%5C%22:%5C%22pdf%5C%22%2C%5C%22langType%5C%22:%5C%22ch-en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321544325914625/%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileType%5C%22:%5C%22png%5C%22%2C%5C%22langType%5C%22:%5C%22en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321612219113473/%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileType%5C%22:%5C%22png%5C%22%2C%5C%22langType%5C%22:%5C%22ar-en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E8%8B%B1%E8%A5%BF%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C.docx%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308151115336002739793921/%E6%88%91%E6%98%AF%E8%8B%B1%E8%A5%BF%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C.docx%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22%2C%5C%22langType%5C%22:%5C%22en-es%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22templateType%5C%22:%5C%22MEX%5C%22%2C%5C%22originalFileName%5C%22:%5C%22%E3%80%82%E3%80%82%E3%80%82%E3%80%82.docx%5C%22%2C%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E6%A0%87%E5%87%86MEX%E7%BA%AF%E8%8B%B1%E5%90%88%E5%90%8C.pdf%5C%22%2C%5C%22imageUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/7/202307041100145345423380480/%E3%80%82%E3%80%82%E3%80%82%E3%80%82.docx%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2022/5/20220509947605666569306112/%E6%88%91%E6%98%AF%E6%A0%87%E5%87%86MEX%E7%BA%AF%E8%8B%B1%E5%90%88%E5%90%8C.pdf%5C%22%2C%5C%22langType%5C%22:%5C%22en%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22}]%22%2C%22contractTemplateList%22:null%2C%22accountSetCode%22:%2210%22%2C%22comprehensiveBaseCurrency%22:%22AED%22%2C%22decimalPlaces%22:%2206%22}; cookieTimeZone=4; countryCode=C115; IMILE_TIME_ZONE=+4; page_key=SCSRoleManagement; UserInfo={%22id%22:1134922065288314900%2C%22orgId%22:10%2C%22userCode%22:%************%22%2C%22userName%22:%22test-neil%22%2C%22email%22:%22%22%2C%22mobile%22:%22%22%2C%22firstLogin%22:false%2C%22userType%22:%22OWN%22%2C%22wechatId%22:null}; _ga_3F6R7164X1=GS1.1.**********.116.0.**********.0.0.0; LANG=zh_CN; IMILE_LANG=zh_CN; LANG=zh_CN")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"**********\",\"moduleId\":10007,\"client\":\"pc\",\"settleOrgId\":\"10\",\"sysName\":\"TMS\"}")
                .header("lang", "zh_CN")
                .header("referer", "https://dev-scs.52imile.cn/")
                .header("request-token", "")
                .header("resource-code", "SCSRoleManagement")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("sys-name", "SCS")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .asString();
        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        return result;
    }

    private Result getRoleMenu(Long roleId, String authorization) {
        HttpResponse<String> response = Unirest.get("https://scs.imile.com/hermes/perm/role/queryRoleResourceIds?roleId=" + roleId)
                .header("authority", "dev-scs.52imile.cn")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", authorization)
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=UAE; userCountry=UAE; cookieTimeZone=4; countryName=UAE; countryCode=C115; countryName=UAE; ACCESS_TOKEN=Bearer%207405207a-6107-4f82-a234-1c6ff4b114d2; ACCESS_TOKEN_SAS=Bearer%207405207a-6107-4f82-a234-1c6ff4b114d2; TIMEZONE=+4; TIMEZONE_COUNTRY=UAE; timezone=+4; timezoneCountry=UAE; IMILE_ACCESS_TOKEN=7405207a-6107-4f82-a234-1c6ff4b114d2; settleOrg={%22id%22:%22823321544150220801%22%2C%22orgId%22:%2210%22%2C%22companyOrgId%22:%2210%22%2C%22downstreamOrgIds%22:%2211%2C34931%22%2C%22companyName%22:%22imile%20Delivery%20Services%20LLC%22%2C%22companyShortName%22:%22iMile%22%2C%22registerCountry%22:%22UAE%22%2C%22isSettleCenter%22:1%2C%22isSettleCenterDesc%22:null%2C%22businessArea%22:%22KSA%2CUAE%2CCHL%2CCHN%2CMEX%2CKWT%2CBHR%2COMN%2CJOR%2CMAR%2CQAT%2CRSA%2CBRA%2CAUS%2CLBN%2CTUR%2CITA%22%2C%22dutyParagraph%22:%22chensr.110.112%22%2C%22companyStatus%22:%22Working%22%2C%22companyStatusDesc%22:null%2C%22contractTemplate%22:%22[{%5C%22fileName%5C%22:%5C%22%E4%B8%AD%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%E6%94%B9.pdf%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321005852778496/%E4%B8%AD%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%E6%94%B9.pdf%5C%22%2C%5C%22fileType%5C%22:%5C%22pdf%5C%22%2C%5C%22langType%5C%22:%5C%22ch-en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321544325914625/%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileType%5C%22:%5C%22png%5C%22%2C%5C%22langType%5C%22:%5C%22en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308011110321612219113473/%E6%88%91%E6%98%AF%E7%BA%AF%E8%8B%B1%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C%20(1).png%5C%22%2C%5C%22fileType%5C%22:%5C%22png%5C%22%2C%5C%22langType%5C%22:%5C%22ar-en%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E8%8B%B1%E8%A5%BF%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C.docx%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308151115336002739793921/%E6%88%91%E6%98%AF%E8%8B%B1%E8%A5%BF%E6%A0%87%E5%87%86%E5%90%88%E5%90%8C.docx%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22%2C%5C%22langType%5C%22:%5C%22en-es%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22}%2C{%5C%22templateType%5C%22:%5C%22MEX%5C%22%2C%5C%22originalFileName%5C%22:%5C%22%E3%80%82%E3%80%82%E3%80%82%E3%80%82.docx%5C%22%2C%5C%22fileName%5C%22:%5C%22%E6%88%91%E6%98%AF%E6%A0%87%E5%87%86MEX%E7%BA%AF%E8%8B%B1%E5%90%88%E5%90%8C.pdf%5C%22%2C%5C%22imageUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/7/202307041100145345423380480/%E3%80%82%E3%80%82%E3%80%82%E3%80%82.docx%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2022/5/20220509947605666569306112/%E6%88%91%E6%98%AF%E6%A0%87%E5%87%86MEX%E7%BA%AF%E8%8B%B1%E5%90%88%E5%90%8C.pdf%5C%22%2C%5C%22langType%5C%22:%5C%22en%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22}]%22%2C%22contractTemplateList%22:null%2C%22accountSetCode%22:%2210%22%2C%22comprehensiveBaseCurrency%22:%22AED%22%2C%22decimalPlaces%22:%2206%22}; cookieTimeZone=4; countryCode=C115; IMILE_TIME_ZONE=+4; page_key=SCSRoleManagement; UserInfo={%22id%22:1134922065288314900%2C%22orgId%22:10%2C%22userCode%22:%************%22%2C%22userName%22:%22test-neil%22%2C%22email%22:%22%22%2C%22mobile%22:%22%22%2C%22firstLogin%22:false%2C%22userType%22:%22OWN%22%2C%22wechatId%22:null}; _ga_3F6R7164X1=GS1.1.**********.116.0.**********.0.0.0; LANG=zh_CN; IMILE_LANG=zh_CN; LANG=zh_CN")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"**********\",\"moduleId\":10007,\"client\":\"pc\",\"settleOrgId\":\"10\",\"sysName\":\"TMS\"}")
                .header("lang", "zh_CN")
                .header("referer", "https://dev-scs.52imile.cn/")
                .header("request-token", "")
                .header("resource-code", "SCSRoleManagement")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("sys-name", "SCS")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .asString();

        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        return result;
    }
}

package com.imile.permission.dubbo;

import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.rpc.service.GenericService;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/3/15
 */
public class GenericTest {

    @Test
    public void genericInvoke() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<GenericService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setGeneric(true);

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("userManagerApiTest"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        //referenceConfig.setUrl("dubbo://************:18108");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface("com.imile.hrms.api.organization.api.DeptApi");
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        GenericService genericService = referenceConfig.get();
        Object o = genericService.$invoke("getDeptTree", new String[]{}, new Object[]{});
        System.out.println(o);
    }
}

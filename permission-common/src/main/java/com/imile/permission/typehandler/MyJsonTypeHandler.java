package com.imile.permission.typehandler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@Slf4j
@MappedTypes({JSON.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class MyJsonTypeHandler extends AbstractJsonTypeHandler<JSON> {

    @Override
    protected JSON parse(String json) {
        if (ObjectUtil.isEmpty(json)) {
            return null;
        }
        JSON result = null;
        char c = json.charAt(0);
        if (c == '{') {
            result = JSON.parseObject(json);
        } else if (c == '[') {
            result = JSON.parseArray(json);
        } else {
            log.warn("json 转换失败，数据为非 json 类型");
        }
        return result;
    }

    @Override
    protected String toJson(JSON obj) {
        return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullListAsEmpty, SerializerFeature.WriteNullStringAsEmpty);
    }
}

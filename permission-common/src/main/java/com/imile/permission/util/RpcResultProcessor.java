package com.imile.permission.util;

import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.rpc.RpcException;

/**
 * rpc 调用结果处理
 *
 * <AUTHOR>
 */
@Slf4j
public class RpcResultProcessor {

    /**
     * 远程调用结果
     *
     * @param rpcResult 结果
     * @param <T>       数据类新
     * @return 结果
     */
    public static <T> T process(RpcResult<T> rpcResult) {
        if (rpcResult != null && rpcResult.isSuccess()) {
            return rpcResult.getResult();
        }
        log.warn("remote call error!{}", rpcResult);
        if (rpcResult == null) {
            throw new RpcException("remote call return null rpc result!");
        }
        throw new RpcException(Integer.parseInt(rpcResult.getErrorCode()), rpcResult.getMessage());
    }

    /**
     * 原理远程调用结果
     *
     * @param rpcResult 结果
     * @param <T>       数据类新
     * @return 结果
     */
    public static <T> T process(RpcResult<T> rpcResult, T defaultResult) {
        if (rpcResult != null && rpcResult.isSuccess()) {
            return ObjectUtils.defaultIfNull(rpcResult.getResult(), defaultResult);
        }
        log.warn("remote call error!{}", rpcResult);
        if (rpcResult == null) {
            throw new RpcException("rmote call return null rpc result!");
        }
        throw new RpcException(Integer.parseInt(rpcResult.getErrorCode()), rpcResult.getMessage());
    }

    /**
     * 远程调用结果
     * 为空不抛异常
     *
     * @param rpcResult 结果
     * @param <T>       数据类新
     * @return 结果
     */
    public static <T> T processNull(RpcResult<T> rpcResult) {
        if (rpcResult == null) {
            throw new RpcException("rmote call return null rpc result!");
        }
        if (rpcResult.isSuccess()) {
            return rpcResult.getResult();
        }
        log.warn("remote call error!{}", rpcResult);
        throw new RpcException(Integer.parseInt(rpcResult.getErrorCode()), rpcResult.getMessage());
    }
}

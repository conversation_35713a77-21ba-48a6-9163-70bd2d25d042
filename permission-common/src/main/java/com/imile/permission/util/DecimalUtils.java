package com.imile.permission.util;

import cn.hutool.core.util.StrUtil;
import com.imile.permission.constants.DigitConstant;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.Objects;

/**
 * @ClassDescription:
 * @author: long.gao
 * @date: 2023.11.08
 */
public class DecimalUtils {

    /**
     * 加法计算（result = x + y）
     *
     * @param x 被加数（可为null）
     * @param y 加数 （可为null）
     * @return 和 （可为null）
     */
    public static BigDecimal add(BigDecimal x, BigDecimal y) {
        if (x == null) {
            return y;
        }
        if (y == null) {
            return x;
        }
        return x.add(y);
    }

    /**
     * 加法计算（result = a + b + c + d）
     *
     * @param a 被加数（可为null）
     * @param b 加数（可为null）
     * @param c 加数（可为null）
     * @param d 加数（可为null）
     * @return BigDecimal （可为null）
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b, BigDecimal c, BigDecimal d) {
        BigDecimal ab = add(a, b);
        BigDecimal cd = add(c, d);
        return add(ab, cd);
    }

    /**
     * 累加计算(result=x + result)
     *
     * @param x      被加数（可为null）
     * @param result 和 （可为null,若被加数不为为null，result默认值为0）
     * @return result 和 （可为null）
     */
    public static BigDecimal accumulate(BigDecimal x, BigDecimal result) {
        if (x == null) {
            return result;
        }
        if (result == null) {
            result = new BigDecimal("0");
        }
        return result.add(x);
    }

    /**
     * 减法计算(result = x - y)
     *
     * @param x 被减数（可为null）
     * @param y 减数（可为null）
     * @return BigDecimal 差 （可为null）
     */
    public static BigDecimal subtract(BigDecimal x, BigDecimal y) {
        if (x == null || y == null) {
            return null;
        }
        return x.subtract(y);
    }

    /**
     * 乘法计算(result = x × y)
     *
     * @param x 乘数(可为null)
     * @param y 乘数(可为null)
     * @return BigDecimal 积
     */
    public static BigDecimal multiply(BigDecimal x, BigDecimal y) {
        if (x == null || y == null) {
            return null;
        }
        return x.multiply(y);
    }

    /**
     * 多个数相乘
     *
     * @param multiplier 乘数
     * @return
     */
    public static BigDecimal multiply(BigDecimal ... multiplier) {
        if (Arrays.stream(multiplier).anyMatch(Objects::isNull)) {
            return null;
        }
        return Arrays.stream(multiplier).reduce(DecimalUtils::multiply).orElse(null);
    }

    /**
     * 除法计算(result = x ÷ y)
     *
     * @param x 被除数（可为null）
     * @param y 除数（可为null）
     * @return 商 （
     */
    public static BigDecimal divide(BigDecimal x, BigDecimal y, Integer scale, Integer roundingMode) {
        if (x == null || y == null || y.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        // 结果为0.000..时，不用科学计数法展示x
        return x.divide(y, scale, roundingMode);
    }

    /**
     * 转为字符串(防止返回可续计数法表达式)
     *
     * @param x 要转字符串的小数
     * @return String
     */
    public static String toPlainString(BigDecimal x) {
        if (x == null) {
            return null;
        }
        return x.toPlainString();
    }

    /**
     * 保留小数位数
     *
     * @param x     目标小数
     * @param scale 要保留小数位数
     * @return BigDecimal 结果四舍五入
     */
    public static BigDecimal scale(BigDecimal x, int scale) {
        if (x == null) {
            return null;
        }
        return x.setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 保留小数位数
     *
     * @param x     目标小数
     * @param scale 要保留小数位数
     * @param round 舍入类型
     * @return BigDecimal
     */
    public static BigDecimal scale(BigDecimal x, int scale,int round) {
        if (x == null) {
            return null;
        }
        return x.setScale(scale, round);
    }


    /**
     * 整型转为BigDecimal
     *
     * @param x(可为null)
     * @return BigDecimal (可为null)
     */
    public static BigDecimal toBigDecimal(Integer x) {
        if (x == null) {
            return null;
        }
        return new BigDecimal(x.toString());
    }

    /**
     * 长整型转为BigDecimal
     *
     * @param x(可为null)
     * @return BigDecimal (可为null)
     */
    public static BigDecimal toBigDecimal(Long x) {
        if (x == null) {
            return null;
        }
        return new BigDecimal(x.toString());
    }

    /**
     * 双精度型转为BigDecimal
     *
     * @param x(可为null)
     * @return BigDecimal (可为null)
     */
    public static BigDecimal toBigDecimal(Double x) {
        if (x == null) {
            return null;
        }
        return new BigDecimal(x.toString());
    }

    /**
     * 单精度型转为BigDecimal
     *
     * @param x(可为null)
     * @return BigDecimal (可为null)
     */
    public static BigDecimal toBigDecimal(Float x) {
        if (x == null) {
            return null;
        }
        return new BigDecimal(x.toString());
    }

    /**
     * 字符串型转为BigDecimal
     *
     * @param x(可为null)
     * @return BigDecimal (可为null)
     */
    public static BigDecimal toBigDecimal(String x) {
        if (x == null || StrUtil.EMPTY.equals(x)) {
            return null;
        }
        return new BigDecimal(x);
    }

    /**
     * 对象类型转为BigDecimal
     *
     * @param x(可为null)
     * @return BigDecimal (可为null)
     */
    public static BigDecimal toBigDecimal(Object x) {
        if (x == null) {
            return null;
        }
        BigDecimal result = null;
        try {
            result = toBigDecimal(x.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 倍数计算，用于单位换算
     *
     * @param x        目标数(可为null)
     * @param multiple 倍数 (可为null)
     * @return BigDecimal (可为null)
     */
    public static BigDecimal multiple(BigDecimal x, Integer multiple) {
        if (x == null || multiple == null) {
            return null;
        }
        return DecimalUtils.multiply(x, toBigDecimal(multiple));
    }

    /**
     * 去除小数点后的0（如: 输入1.000返回1）
     *
     * @param x 目标数(可为null)
     * @return
     */
    public static BigDecimal stripTrailingZeros(BigDecimal x) {
        if (x == null) {
            return null;
        }
        return x.stripTrailingZeros();
    }

    /**
     * converRemoveZeroString
     *
     * @param x     目标小数
     * @param scale 要保留小数位数
     * @return String 清空0，并转成字符串
     */
    public static String converRemoveZeroString(BigDecimal x, int scale, int round) {
        if (x == null) {
            return null;
        }
        return DecimalUtils.toPlainString(DecimalUtils.stripTrailingZeros(x.setScale(scale, round)));
    }

    public static boolean isZeroOrNull(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return true;
        }
       return Objects.equals(BigDecimal.ZERO.compareTo(bigDecimal), DigitConstant.ZERO);
    }

    public static String toPercentString(BigDecimal bigDecimal, Integer minimumFractionDigits, Integer maximumFractionDigits) {
        if (Objects.isNull(bigDecimal)) {
            return "";
        }
        NumberFormat percentFormat = NumberFormat.getPercentInstance();
        // 设置百分比精度
        percentFormat.setMinimumFractionDigits(minimumFractionDigits);
        percentFormat.setMaximumFractionDigits(maximumFractionDigits);

        // 将BigDecimal转换为百分比字符串
        return percentFormat.format(bigDecimal);
    }

    public static String toPercentString(BigDecimal bigDecimal, Integer imumFractionDigits) {
        if (Objects.isNull(bigDecimal)) {
            return "";
        }
        return toPercentString(bigDecimal, imumFractionDigits, imumFractionDigits);
    }

    /**
     * BigDecimal是否相等
     *
     * @param x x
     * @param y y
     * @return 是否相等
     */
    public static boolean equals(BigDecimal x, BigDecimal y) {
        // 直接返回比较结果，当两个都为null时返回true，否则调用compareTo方法。
        return (Objects.isNull(x) ?  Objects.isNull(y) : x.compareTo(y) == 0);
    }

    /**
     * x是否大于y
     *
     * @param x x
     * @param y y
     * @return 是否相等
     */
    public static boolean greaterThan(BigDecimal x, BigDecimal y) {
        return (Objects.nonNull(x) && x.compareTo(y) > 0);
    }

}


/**
 * Copyright (c) 2005-2010 springside.org.cn
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * <p>
 * $Id: ReflectionUtil.java 1211 2010-09-10 16:20:45Z calvinxiu $
 */
package com.imile.permission.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.Closeable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 反射工具类.
 * <p>
 * 提供访问私有变量,获取泛型类型Class, 提取集合中元素的属性, 转换字符串到对象等Util函数.
 *
 * <AUTHOR>
 */
public class ReflectionUtil {

    private ReflectionUtil(){}
    private static final String BOOLEAN_STR = "boolean";
    private static final String IS_STR = "is";
    private static final String GET_STR = "get";



    protected static Logger logger = LoggerFactory.getLogger(ReflectionUtil.class);

    /**
     * 调用Getter方法.
     *
     * @throws InvocationTargetException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static Object invokeGetterMethod(Object obj,
                                            String propertyName) throws
            SecurityException,
            InvocationTargetException,
            IllegalAccessException,
            IllegalArgumentException {
        Assert.notNull(obj, "object不能为空");
        //获取get方法
        Method method = getGetMethod(obj, propertyName);
        if (method != null) {
            return method.invoke(obj, new Object[]{});
        }
        return null;

    }

    /**
     * 获取get方法
     *
     * @param obj
     * @param propertyName
     * @return
     */
    public static Method getGetMethod(Object obj, String propertyName) {
        String getterMethodName = StringUtils.capitalize(propertyName);
        Method method = null;
        for (Class<?> superClass = obj
                .getClass(); superClass != Object.class; superClass = superClass.getSuperclass()) {
            for (Method smethod : superClass.getDeclaredMethods()) {
                String start = BOOLEAN_STR.equals(smethod.getReturnType().getName()) ? IS_STR : GET_STR;
                String methodName = start + getterMethodName;
                //方法名匹配并且方法参数未0个
                if (smethod.getName().equals(methodName)) {
                    method = smethod;
                    break;
                }
            }
        }
        return method;
    }


    /**
     * 直接读取对象属性值, 无视private/protected修饰符, 不经过getter函数.
     */
    public static Object getFieldValue(final Object obj, final String fieldName) {
        Field field = getAccessibleField(obj, fieldName);

        if (field == null) {
            throw new IllegalArgumentException(
                    "Could not find field [" + fieldName + "] on target [" + obj + "]");
        }

        Object result = null;
        try {
            result = field.get(obj);
        } catch (IllegalAccessException e) {
            logger.error(ReflectionUtil.class.toString(), e);
        }
        return result;
    }

    /**
     * 直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.
     */
    public static void setFieldValue(final Object obj, final String fieldName, final Object value) {
        Field field = getAccessibleField(obj, fieldName);

        if (field == null) {
            throw new IllegalArgumentException(
                    "Could not find field [" + fieldName + "] on target [" + obj + "]");
        }

        try {
            field.set(obj, value);
        } catch (IllegalAccessException e) {
            logger.error(ReflectionUtil.class.toString(), e);
        }
    }

    /**
     * 直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.
     */
    public static void setFieldValue(final Object obj, Field field, final Object value) {
        try {
            field.setAccessible(true);
            field.set(obj, value);
        } catch (IllegalAccessException e) {
            logger.error(ReflectionUtil.class.toString(), e);
        }
    }

    /**
     * 循环向上转型, 获取对象的DeclaredField,	 并强制设置为可访问.
     * <p>
     * 如向上转型到Object仍无法找到, 返回null.
     */
    public static Field getAccessibleField(final Object obj, final String fieldName) {
        Assert.notNull(obj, "object不能为空");
        Assert.hasText(fieldName, "fieldName");
        for (Class<?> superClass = obj
                .getClass(); superClass != Object.class; superClass = superClass.getSuperclass()) {
            try {
                Field field = superClass.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {//NOSONAR
                // Field不在当前类定义,继续向上转型
            }
        }
        return null;
    }

    /**
     * 通过反射, 获得Class定义中声明的父类的泛型参数的类型.
     * 如无法找到, 返回Object.class.
     * eg.
     * public UserDao extends HibernateDao<User>
     *
     * @param clazz The class to introspect
     * @return the first generic declaration, or Object.class if cannot be determined
     */
    @SuppressWarnings({
            "rawtypes",
            "unchecked"
    })
    public static <T> Class<T> getSuperClassGenricType(final Class clazz) {
        return getSuperClassGenricType(clazz, 0);
    }

    /**
     * 通过反射, 获得Class定义中声明的父类的泛型参数的类型.
     * 如无法找到, 返回Object.class.
     * <p>
     * 如public UserDao extends HibernateDao<User,Long>
     *
     * @param clazz clazz The class to introspect
     * @param index the Index of the generic ddeclaration,start from 0.
     * @return the index generic declaration, or Object.class if cannot be determined
     */
    @SuppressWarnings("rawtypes")
    public static Class getSuperClassGenricType(final Class clazz, final int index) {

        Type genType = clazz.getGenericSuperclass();

        if (!(genType instanceof ParameterizedType)) {
            logger.warn("{}'s superclass not ParameterizedType", clazz.getSimpleName());
            return Object.class;
        }

        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();

        if (index >= params.length || index < 0) {
            logger.warn(String.format("Index {} ,Size of {} Parameterized Type {}", index, clazz.getSimpleName(), params.length));
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            logger.warn(String.format("{} not set the actual class on superclass generic parameter", clazz.getSimpleName()));
            return Object.class;
        }

        return (Class) params[index];
    }


    /**
     * 得到所有的Field
     *
     * @param clazz Class
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static List<Field> getAllDeclaredField(Class clazz) {
        List<Field> list = new LinkedList<>();
        for (Class<?> superClass = clazz; superClass != Object.class; superClass = superClass
                .getSuperclass()) {
            Field[] declaredFields = superClass.getDeclaredFields();
            for (Field f : declaredFields) {
                Collections.addAll(list, f);
            }
        }
        return list;
    }

    /**
     * 直接调用对象方法,无视private/protected修饰符.
     */
    public static Object invokeMethod(final Object object, final String methodName,
                                      final Class<?>[] parameterTypes,
                                      final Object[] parameters) throws InvocationTargetException {
        Method method = getDeclaredMethod(object, methodName, parameterTypes);
        if (method == null) {
            throw new IllegalArgumentException(
                    "Could not find method [" + methodName + "] on target [" + object + "]");
        }
        method.setAccessible(true);

        try {
            return method.invoke(object, parameters);
        } catch (IllegalAccessException e) {
        }

        return null;
    }

    /**
     * 循环向上转型,获取对象的DeclaredMethod.
     */
    protected static Method getDeclaredMethod(Object object, String methodName,
                                              Class<?>[] parameterTypes) {
        for (Class<?> superClass = object
                .getClass(); superClass != Object.class; superClass = superClass.getSuperclass()) {
            try {
                return superClass.getDeclaredMethod(methodName, parameterTypes);
            } catch (NoSuchMethodException e) {
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.
     * 如向上转型到Object仍无法找到, 返回null.
     * <p>
     * 用于方法需要被多次调用的情况. 先使用本函数先取得Method,然后调用Method.invoke(Object obj, Object... args)
     *
     * @throws NoSuchMethodException
     */
    public static Method getAccessibleMethod(final Object obj, final String methodName,
                                             final Class<?>... parameterTypes) throws NoSuchMethodException {
        Assert.notNull(obj, "object不能为空");

        for (Class<?> superClass = obj
                .getClass(); superClass != Object.class; superClass = superClass.getSuperclass()) {
            try {
                Method method = superClass.getDeclaredMethod(methodName, parameterTypes);

                method.setAccessible(true);
                return method;


            } catch (NoSuchMethodException e) {//NOSONAR
                // Method不在当前类定义,继续向上转型
                throw e;
            }
        }
        return null;
    }

    /**
     * 将反射时的checked exception转换为unchecked exception.
     */
    public static RuntimeException convertReflectionExceptionToUnchecked(Exception e) {
        if (e instanceof IllegalAccessException || e instanceof IllegalArgumentException
                || e instanceof NoSuchMethodException) {
            return new IllegalArgumentException("Reflection Exception.", e);
        } else if (e instanceof InvocationTargetException) {
            return new RuntimeException("Reflection Exception.",
                    ((InvocationTargetException) e).getTargetException());
        } else if (e instanceof RuntimeException) {
            return (RuntimeException) e;
        }
        return new RuntimeException("Unexpected Checked Exception.", e);
    }

    @SuppressWarnings({
            "rawtypes"
    })
    public static <T> Object getValue(T targetObj, String fieldStr) {

        if (fieldStr == null || "".equals(fieldStr)) {
            return targetObj;
        }

        String[] fields = fieldStr.split("\\.");
        Object value = targetObj;
        String field;
        for (int i = 0; i < fields.length; i++) {
            field = fields[i];
            try {
                if (value instanceof Map) {
                    value = ((Map) value).get(field);
                } else if (value instanceof List) {
                    value = ((List) value).get(new Integer(field));
                } else {
                    value = invokeGetterMethod(value, field);
                }
            } catch (Exception e) {
                value = null;
                break;
            }
        }
        return value;
    }

    @SuppressWarnings({
            "unchecked",
            "rawtypes"
    })
    public static void setValue(Object obj, String fieldName, Object value) throws Exception {
        if (obj instanceof Map) {
            ((Map) obj).put(fieldName, value);
        } else {
            try {
                invokeSetterMethod(obj, fieldName, value);
            } catch (Exception e) {
                throw new Exception("set方法调用失败，请检查字段" + fieldName + "对应的set方法");
            }
        }
    }

    /**
     * 调用Setter方法.使用value的Class来查找Setter方法.
     *
     * @throws InvocationTargetException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static void invokeSetterMethod(Object obj, String propertyName,
                                          Object value) throws InvocationTargetException,
            IllegalAccessException,
            IllegalArgumentException {
        invokeSetterMethod(obj, propertyName, value, null);
    }

    /**
     * 调用Setter方法.
     *
     * @param propertyType 用于查找Setter方法,为空时使用value的Class替代.
     * @throws InvocationTargetException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static void invokeSetterMethod(Object obj, String propertyName, Object value,
                                          Class<?> propertyType) throws InvocationTargetException,
            IllegalAccessException,
            IllegalArgumentException {
        String setterMethodName = "set" + StringUtils.capitalize(propertyName);
        Method method = null;
        for (Class<?> superClass = obj
                .getClass(); superClass != Object.class; superClass = superClass.getSuperclass()) {
            for (Method smethod : superClass.getDeclaredMethods()) {
                if (smethod.getName().equals(setterMethodName)) {
                    method = smethod;
                    break;
                }
            }
        }
        method.invoke(obj, value);
    }

    public static void close(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                logger.info("close error:{}", closeable, e);
            }
        }
    }

}

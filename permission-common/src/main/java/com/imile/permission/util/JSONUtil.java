package com.imile.permission.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/11/5
 */
public class JSONUtil {
    public static Boolean checkJson(String json) {
        if (StringUtils.isBlank(json)) {
            return false;
        }
        try {
            JSON.parseObject(json);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public static String getMapSortFieldJson(String json) {
        if (StringUtils.isBlank(json)) {
            return "";
        }
        String jsonString = JSON.toJSONString(JSON.parseObject(json), SerializerFeature.MapSortField);
        return jsonString;
    }

    public static JSONObject parseObject(String optionalParameter) {
        if (StringUtils.isBlank(optionalParameter)) {
            return new JSONObject();
        }
        try {
            return JSON.parseObject(optionalParameter);
        } catch (Exception e) {
            return new JSONObject();
        }
    }

    public static JSONArray parseArray(String ruleJson) {
        if (StringUtils.isBlank(ruleJson)) {
            return new JSONArray();
        }
        try {
            return JSON.parseArray(ruleJson);
        } catch (Exception e) {
            return new JSONArray();
        }
    }
}

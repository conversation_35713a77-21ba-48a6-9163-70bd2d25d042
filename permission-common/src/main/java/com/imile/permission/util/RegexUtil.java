package com.imile.permission.util;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */
public class RegexUtil {
    private static String urlPattern = "^(https?|http)://.*$";
    private static Pattern pattern = Pattern.compile(urlPattern);

    private static Pattern numberPattern = Pattern.compile("-?\\d+");

    public static boolean isNumber(String str) {
        return numberPattern.matcher(str).matches();
    }

    public static boolean isValidUrl(String url) {
        // 使用正则表达式进行URL校验
        return pattern.matcher(url).matches();
    }

    public static String appendIfOptionalParameterMissing(String baseRequestUrl, String optionalParameter, String userCode) {
        // 分离URL的基本部分和查询字符串部分
        int queryStartIndex = baseRequestUrl.indexOf('?');
        String baseUrl = queryStartIndex == -1 ? baseRequestUrl : baseRequestUrl.substring(0, queryStartIndex);
        String query = queryStartIndex == -1 ? "" : baseRequestUrl.substring(queryStartIndex + 1);

        // 将查询字符串解析为Map
        Map<String, String> queryParams = parseQuery(query);

        // 检查是否已经有 userCode 参数
        if (!queryParams.containsKey(optionalParameter)) {
            // 如果没有 userCode 参数，添加它
            queryParams.put(optionalParameter, userCode);
        }

        // 重新构建查询字符串
        String newQuery = buildQuery(queryParams);

        // 构建新的URL
        return baseUrl + (newQuery.isEmpty() ? "" : "?" + newQuery);
    }


    /**
     * 解析查询字符串为Map
     *
     * @param query 查询字符串
     * @return 包含查询参数的Map
     */
    private static Map<String, String> parseQuery(String query) {
        Map<String, String> params = new HashMap<>();
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0) {
                try {
                    String key = URLEncoder.encode(pair.substring(0, idx), StandardCharsets.UTF_8.toString());
                    String value = URLEncoder.encode(pair.substring(idx + 1), StandardCharsets.UTF_8.toString());
                    params.put(key, value);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return params;
    }

    /**
     * 从Map重建查询字符串
     *
     * @param queryParams 包含查询参数的Map
     * @return 查询字符串
     */
    private static String buildQuery(Map<String, String> queryParams) {
        StringBuilder queryBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : queryParams.entrySet()) {
            if (queryBuilder.length() > 0) {
                queryBuilder.append('&');
            }
            try {
                queryBuilder.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.toString()));
                queryBuilder.append('=');
                queryBuilder.append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.toString()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return queryBuilder.toString();
    }

    public static boolean validateExtensionTagCode(String code) {
        String regex = "^[a-zA-Z0-9]{1,20}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(code);
        return matcher.matches();
    }
}


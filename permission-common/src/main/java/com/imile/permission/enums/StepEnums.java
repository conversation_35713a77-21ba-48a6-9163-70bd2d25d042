package com.imile.permission.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 错误状态码
 *
 * <AUTHOR>
 * @since 2023/11/7
 */
@Getter
public enum StepEnums {

    STEP_ONE(1, "第一步"),
    STEP_TWO(2, "第二步"),
    STEP_THREE(3, "第三步"),
    ;
    private Integer code;
    private String desc;

    StepEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }





}

package com.imile.permission.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Getter
@AllArgsConstructor
public enum RoleTypeEnum {

    // 角色类型 1 业务角色，2 默认角色，3 CSP供应商管理员角色, 4 司机角色，5 CSP网点角色, 6 供应商类型角色，7 国家供应商类型角色
    BUSINESS_ROLE(1, "业务角色", "business role"),
    DEFAULT_ROLE(2, "默认角色", "default role"),
    CSP_SUPPLIER_ADMIN(3, "CSP供应商管理员角色认角色", "csp supplier admin"),
    DRIVER(4, "司机角色认角色", "driver"),
    CSP_NETWORK_OPERATOR(5, "CSP网点角色认角色", "csp network operator"),
    SUPPLIER(6, "供应商类型角色", "supplier"),
    COUNTRY_SUPPLIER(7, "国家供应商类型角色", "country supplier"),
    ;


    private Integer code;
    private String desc;
    private String descEn;

    /**
     * 商家默认角色
     * @param roleType
     * @param authScene
     * @return
     */
    public static boolean isClientDefaultRole(Integer roleType, Integer authScene) {
        return DEFAULT_ROLE.getCode().equals(roleType) && RoleAuthSceneEnum.CLIENT.getCode().equals(authScene);
    }

    public static boolean isEmployeeDefaultRole(Integer roleType, Integer authScene) {
        return DEFAULT_ROLE.getCode().equals(roleType) && RoleAuthSceneEnum.EMPLOYEE.getCode().equals(authScene);
    }

    public static RoleTypeEnum getInstance(Integer code) {
        for (RoleTypeEnum c : RoleTypeEnum.values()) {
            if (Objects.equals(c.getCode(), code)) {
                return c;
            }
        }
        return null;
    }

    public static String getCnOrEnDesc(Integer code, Locale locale) {

        if(Objects.isNull(code) || Objects.isNull(locale)) {
            return null;
        }
        for (RoleTypeEnum c : RoleTypeEnum.values()) {
            if (Objects.equals(c.getCode(), code)) {
                String desc = locale.equals(Locale.US) ? c.getDescEn() : c.getDesc();
                return desc;
            }
        }
        return null;
    }
}

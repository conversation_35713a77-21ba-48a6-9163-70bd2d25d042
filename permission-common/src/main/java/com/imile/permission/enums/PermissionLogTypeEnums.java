package com.imile.permission.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */
@Getter
public enum PermissionLogTypeEnums {

    ADD("ADD","新增"),
    DELETE("DELETE","删除"),
    UPDATE("UPDATE","更新"),
    DISABLED("DISABLED","停启用"),
    AUTH("AUTH","授权"),
    ;
    private String code;
    private String name;

    PermissionLogTypeEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }
}

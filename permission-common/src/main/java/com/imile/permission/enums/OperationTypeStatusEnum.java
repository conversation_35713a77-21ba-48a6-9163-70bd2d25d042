package com.imile.permission.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/4/6
 */
@Getter
public enum OperationTypeStatusEnum {
    ADD("ADD", "新增", "<PERSON>"),
    RECYCLE("RECY<PERSON><PERSON>", "回收", "Revoke"),
    ;

    /**
     * 前缀
     */
    private String code;
    private String cn;
    private String en;

    OperationTypeStatusEnum(String code, String cn, String en) {
        this.code = code;
        this.cn = cn;
        this.en = en;
    }


    public static OperationTypeStatusEnum getInstance(String code) {
        for (OperationTypeStatusEnum c : OperationTypeStatusEnum.values()) {
            if (Objects.equals(c.getCode(), code)) {
                return c;
            }
        }
        return null;
    }

    public static String getCnOrEnDesc(String code, Locale lang) {
        if(StringUtils.isEmpty(code) || Objects.isNull(lang)) {
            return null;
        }
        for (OperationTypeStatusEnum c : OperationTypeStatusEnum.values()) {
            if (Objects.equals(c.getCode(), code)) {
                String desc = lang.equals(Locale.US) ? c.getEn() : c.getCn();
                return desc;
            }
        }
        return null;
    }
}

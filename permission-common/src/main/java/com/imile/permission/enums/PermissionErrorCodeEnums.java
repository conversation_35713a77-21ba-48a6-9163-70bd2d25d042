package com.imile.permission.enums;

/**
 * 错误状态码
 *
 * <AUTHOR>
 * @since 2023/11/7
 */
public enum PermissionErrorCodeEnums {
    /**
     * 不为空
     */
    ROLENAMECN_NOT_NULL("60001", "ROLENAMECN_NOT_NULL", "roleNameCn 不能为空"),
    ROLENAMEEN_NOT_NULL("60002", "ROLENAMEEN_NOT_NULL", "roleNameEn 不能为空"),
    ROLE_NOT_NULL("60003", "ROLE_NOT_NULL", "角色不能为空"),
    ROLE_ID_NOT_NULL("60004", "ROLE_ID_NOT_NULL", "角色ID不能为空"),
    ROLE_RELATION_NOT_NULL("60005", "ROLE_RELATION_NOT_NULL", "角色关联信息不能为空"),
    POST_ID_NOT_NULL("60005", "POST_ID_NOT_NULL", "岗位ID不能为空"),
    ROLE_NAME_NOT_NULL("60005", "ROLE_NAME_NOT_NULL", "角色名称不能为空"),
    POST_NOT_NULL("60005", "POST_NOT_NULL", "岗位不能为空"),
    ID_NOT_NULL("60005", "ID_NOT_NULL", "ID不能为空"),
    ROLE_IS_USED("60005", "ROLE_IS_USED", "角色已被使用"),
    CONFIGURATION_STATUS_NOT_NULL("60005", "CONFIGURATION_STATUS_NOT_NULL", "配置状态不能为空"),
    WORK_CENTER_NODE_ID_NOT_NULL("60005", "WORK_CENTER_NODE_ID_NOT_NULL", "工作中心节点ID不能为空"),
    WORK_CENTER_NAME_NOT_NULL("60005", "WORK_CENTER_NAME_NOT_NULL", "工作中心名称不能为空"),
    WORK_CENTER_ID_NOT_NULL("60005", "WORK_CENTER_ID_NOT_NULL", "工作中心ID不能为空"),
    BASE_NODE_NOT_NULL("60005", "BASE_NODE_NOT_NULL", "节点数据不能为空"),
    AUTHORIZATION_TYPE_NOT_NULL("60005", "AUTHORIZATION_TYPE_NOT_NULL", "授权类型不能为空"),
    MENU_PERMISSION_NOT_NULL("60005", "MENU_PERMISSION_NOT_NULL", "菜单权限不能为空"),
    BASIC_DATA_PERMISSION_NOT_NULL("60005", "BASIC_DATA_PERMISSION_NOT_NULL", "基础数据权限不能为空"),
    DATA_PERMISSION_NOT_NULL("60005", "DATA_PERMISSION_NOT_NULL", "数据权限不能为空"),
    SOURCE_NODE_ID_NOT_NULL("60005", "SOURCE_NODE_ID_NOT_NULL", "工作中心节点ID不能为空"),
    NODE_PERMISSION_LIST_NOT_NULL("60005", "NODE_PERMISSION_LIST_NOT_NULL", "工作中心节点不能为空"),
    STEP_NODE_NOT_NULL("60005", "STEP_NODE_NOT_NULL", "工作中心节点不能为空"),
    TYPE_CODE_NOT_NULL("60005", "TYPE_CODE_NOT_NULL", "数据类型/动态数据标识不能为空"),
    TYPE_NAME_NOT_NULL("60005", "TYPE_NAME_NOT_NULL", "权限类型名称不能为空"),
    IS_ASSOCIATED_MAIN_DATA_NOT_NULL("60005", "IS_ASSOCIATED_MAIN_DATA_NOT_NULL", "是否关联主数据不能为空"),
    SINGLE_SYSTEM_NOT_NULL("60005", "SINGLE_SYSTEM_NOT_NULL", "系统类型不能为空"),
    ASSOCIATED_MAIN_DATA_TYPE_CODE_NOT_NULL("60005", "ASSOCIATED_MAIN_DATA_TYPE_CODE_NOT_NULL", "关联主数据类型不能为空"),
    DATA_LIST_NOT_NULL("60005", "DATA_LIST_NOT_NULL", "数据内容不能为空"),
    OPTIONAL_PARAMETER_NOT_NULL("60005", "OPTIONAL_PARAMETER_NOT_NULL", "可选参数不能为空"),
    DESCRIPTION_NOT_NULL("60005", "DESCRIPTION_NOT_NULL", "描述不能为空"),
    DESCRIPTION_EN_NOT_NULL("60005", "DESCRIPTION_EN_NOT_NULL", "descriptionEn 不能为空"),
    ROLE_PARENT_ID_NOT_NULL("60005", "ROLE_PARENT_ID_NOT_NULL", "roleParentIdNotNull 角色父ID不能为空"),
    NOT_CSP_ADMIN_ROLE("60005", "NOT_CSP_ADMIN_ROLE", "notCspAdminRole 非 CSP 管理员角色"),
    MANY_CSP_ADMIN_ROLE("60005", "MANY_CSP_ADMIN_ROLE", "manyCspAdminRole 多个CSP管理员角色"),
    ROLE_PARENT_ID_NOT_CSP_ADMIN_ROLE("60005", "ROLE_PARENT_ID_NOT_CSP_ADMIN_ROLE", "roleParentIdNotCspAdminRole 父角色非CSP管理员角色"),
    PERMISSION_MORE_CSP_ADMIN_ROLE("60005", "PERMISSION_MORE_CSP_ADMIN_ROLE", "permissionMoreCspAdminRole 角色权限不可以超过CSP管理员角色"),
    IS_ACCESS_APPROVAL_FLOW_NOT_NULL("60005", "IS_ACCESS_APPROVAL_FLOW_NOT_NULL", "isAccessApprovalFlow 不能为空"),
    DRIVER_ROLE_EXIST("60005", "DRIVER_ROLE_EXIST", "{0} 国家下的 {1} 司机职能已经配置了角色 {2}"),
    SUPPLIER_ROLE_EXIST("60005", "SUPPLIER_ROLE_EXIST", "{0} 供应商类型已经配置了角色 {1}"),
    COUNTRY_SUPPLIER_ROLE_EXIST("60005", "COUNTRY_SUPPLIER_ROLE_EXIST", "{0} 国家下的 {1} 供应商类型已经配置了角色 {2}"),
    ROLE_TYPE_NOT_EXIST("60005", "ROLE_TYPE_NOT_EXIST", "角色类型不存在"),
    DATA_CODE_NOT_NULL("60005", "DATA_CODE_NOT_NULL", "权限数据CODE不能为空"),
    USER_CODE_NOT_NULL("60005", "USER_CODE_NOT_NULL", "用户CODE不能为空"),
    NOTICE_NOT_NULL("60005", "NOTICE_NOT_NULL", "消息不存在"),
    SOURCE_ID_NOT_NULL("60005", "SOURCE_ID_NOT_NULL", "资源 ID 不能为空"),
    SOURCE_TYPE_NOT_NULL("60005", "SOURCE_TYPE_NOT_NULL", "资源类型不能为空"),
    APPROVAL_ID_NOT_NULL("60005", "APPROVAL_ID_NOT_NULL", "approvalId 不能为空"),
    REASON_NOT_NULL("60005", "REASON_NOT_NULL", "reason不能为空"),
    RISK_LEVEL_NOT_NULL("60005", "RISK_LEVEL_NOT_NULL", "riskLevel不能为空"),
    BUSINESS_TYPE_NOT_NULL("60005", "BUSINESS_TYPE_NOT_NULL", "businessType不能为空"),
    PERMISSION_TYPE_NOT_NULL("60005", "PERMISSION_TYPE_NOT_NULL", "permissionType不能为空"),
    USER_ID_NOT_NULL("60005", "USER_ID_NOT_NULL", "用户ID不能为空"),
    EFFECTIVE_TIME_DAY_NOT_NULL("60005", "EFFECTIVE_TIME_DAY_NOT_NULL", "effectiveTimeDay不能为空"),
    DEPT_ID_NOT_NULL("60005", "DEPT_ID_NOT_NULL", "deptId不能为空"),
    DATA_STRUCTURES_NOT_NULL("60005", "DATA_STRUCTURES_NOT_NULL", "数据类型不能为空"),
    AUTHORITY_APPROVAL_NOT_NULL("60005", "AUTHORITY_APPROVAL_NOT_NULL", "权限申请不存在"),
    DATA_URL_NOT_NULL("60005", "DATA_URL_NOT_NULL", "数据URL不能为空"),
    BUSINESS_BASIC_DATA_CONFIG_EXIST("60005", "BUSINESS_BASIC_DATA_CONFIG_EXIST", "业务主数据配置已存在"),
    SYSTEM_CODE_NOT_NULL("60005", "SYSTEM_CODE_NOT_NULL", "系统 Code 不能为空"),
    FLOW_TYPE_NOT_NULL("60005", "FLOW_TYPE_NOT_NULL", "流程类型不能为空"),
    FLOW_TYPE_NOT_EXIST("60005", "FLOW_TYPE_NOT_EXIST", "流程类型不存在"),
    SYSTEM_CODE_NOT_EXIST("60005", "SYSTEM_CODE_NOT_EXIST", "系统code不存在"),
    FLOW_MAIN_DATA_NOT_NULL("60005", "FLOW_MAIN_DATA_NOT_NULL", "主数据提交内容为空"),
    FLOW_FUNCTION_DATA_NOT_NULL("60005", "FLOW_FUNCTION_DATA_NOT_NULL", "函数数据提交内容为空"),
    FLOW_BASE_DATA_NOT_NULL("60005", "FLOW_BASE_DATA_NOT_NULL", "基础数据提交内容为空"),
    RULE_NAME_NOT_NULL("60005", "RULE_NAME_NOT_NULL", "数据维度名称为空"),
    RULE_CODE_NOT_NULL("60005", "RULE_CODE_NOT_NULL", "数据维度参数为空"),
    RULE_TYPE_NOT_NULL("60005", "RULE_TYPE_NOT_NULL", "数据维度类型为空"),
    CLIENT_CODE_NOT_NULL("60005", "CLIENT_CODE_NOT_NULL", "Client Code 不能为空"),
    AUTH_SCENE_IS_NULL("60005", "AUTH_SCENE_IS_NULL", "authScene 不能为空"),
    ROLE_TYPE_IS_NULL("60005", "ROLE_TYPE_IS_NULL", "roleType 不能为空"),
    ORG_ID_NOT_NULL("60005", "ORG_ID_NOT_NULL", "orgId 不能为空"),
    CODE_NOT_NULL("60005", "CODE_NOT_NULL", "code 不能为空"),
    ROLE_NAME_EN_NOT_NULL("60005", "ROLE_NAME_EN_NOT_NULL", "roleNameEn 不能为空"),
    IS_DISABLE_NOT_NULL("60005", "IS_DISABLE_NOT_NULL", "isDisable 不能为空"),
    ADMIN_ORG_NOT_NULL("60005", "ADMIN_ORG_NOT_NULL", "adminOrg 不能为空"),
    OWN_STATION_CODE_NOT_NULL("60005", "OWN_STATION_CODE_NOT_NULL", "ownStationCode 不能为空"),
    MENU_PERMISSION_API_LIST_NOT_NULL("60005", "MENU_PERMISSION_API_LIST_NOT_NULL", "menuPermissionApiList 不能为空"),
    COUNTRY_CODE_NOT_NULL("60005", "COUNTRY_CODE_NOT_NULL", "countryCode 不能为空"),
    XMILE_ROLE_TYPE_NOT_NULL("60005", "XMILE_ROLE_TYPE_NOT_NULL", "xmileRoleType 不能为空"),

    // 新增的供应商权限相关错误码
    PARAM_NOT_NULL("60005", "PARAM_NOT_NULL", "参数不能为空"),
    QUERY_PARAM_NOT_NULL("60005", "QUERY_PARAM_NOT_NULL", "查询参数不能为空"),
    ROLE_BASIC_PARAM_NOT_NULL("60005", "ROLE_BASIC_PARAM_NOT_NULL", "角色基础参数不能为空"),
    MENU_PERMISSION_LIST_NOT_NULL("60005", "MENU_PERMISSION_LIST_NOT_NULL", "菜单权限列表不能为空"),
    PRIMARY_ACCOUNT_ROLE_IS_NULL("60005", "PRIMARY_ACCOUNT_ROLE_IS_NULL", "主账号角色为空"),
    PERMISSION_MORE_PRIMARY_ACCOUNT("60005", "PERMISSION_MORE_PRIMARY_ACCOUNT", "权限不能超过主账号"),
    SYSTEM_CODE_LIST_NOT_NULL("60005", "SYSTEM_CODE_LIST_NOT_NULL", "系统编码列表不能为空"),
    ROLE_COUNTRY_LIST_NOT_NULL("60005", "ROLE_COUNTRY_LIST_NOT_NULL", "角色国家列表不能为空"),
    FUNCTIONAL_NOT_NULL("60005", "FUNCTIONAL_NOT_NULL", "司机职能不能为空"),
    SUPPLIER_TYPE_NOT_NULL("60005", "SUPPLIER_TYPE_NOT_NULL", "供应商类型不能为空"),
    IS_DISABLE_STATUS_NOT_NULL("60005", "IS_DISABLE_STATUS_NOT_NULL", "禁用状态不能为空"),
    ACCOUNT_CODE_NOT_NULL("60005", "ACCOUNT_CODE_NOT_NULL", "账号编码不能为空"),
    PRIMARY_ACCOUNT_CODE_NOT_NULL("60005", "PRIMARY_ACCOUNT_CODE_NOT_NULL", "主账号编码不能为空"),

    SUPER_ADMIN_CAN_CREATE_DEFAULT_ROLE("60006", "SUPER_ADMIN_CAN_CREATE_DEFAULT_ROLE", "只有超管能操作员工默认角色"),
    CANNOT_DELETE_OR_DISABLE_ROLES("60006", "CANNOT_DELETE_OR_DISABLE_ROLES", "不能删除或禁用默认角色"),
    ONLY_ONE_DEFAULT_ROLE_CAN_CREATE("60007", "ONLY_ONE_DEFAULT_ROLE_CAN_CREATE", "只能创建一个商家默认角色"),
    USE_CASE_TYPE_NOT_NULL("60007", "USE_CASE_TYPE_NOT_NULL", "应用类型不允许为空"),
    USE_CASE_DESCRIPTION_NOT_NULL("60007", "USE_CASE_DESCRIPTION_NOT_NULL", "使用场景解释不允许为空"),
    SOURCE_TYPE_CODE_NOT_NULL("60007", "SOURCE_TYPE_CODE_NOT_NULL", "来源数据类型不能为空"),
    DATA_CONFIG_FLOATING_WINDOW_STATUS_NOT_NULL("60007", "DATA_CONFIG_FLOATING_WINDOW_STATUS_NOT_NULL", "浮窗配置不允许为空"),

    EXTENSION_TAG_CODE_NOT_NULL("90023", "EXTENSION_TAG_CODE_NOT_NULL", "数据扩展标签不允许为空"),
    EXTENSION_TAG_CODE_NOT_MATCH_RULE("90023", "EXTENSION_TAG_CODE_NOT_MATCH_RULE", "数据扩展标签不符合规范"),
    EXTENSION_TAG_CODE_DUPLICATION("90023", "EXTENSION_TAG_CODE_DUPLICATION", "数据扩展标签重复"),
    EXTENSION_TAG_NAME_DUPLICATION("90023", "EXTENSION_TAG_NAME_DUPLICATION", "数据扩展标签名称重复"),
    EXTENSION_TAG_NOT_EXCEED_5("90023", "EXTENSION_TAG_NOT_EXCEED_5", "数据扩展标签最多存在5个"),
    EXTENSION_TAG_CODE_TOO_LONG("90023", "EXTENSION_TAG_CODE_TOO_LONG", "数据扩展标签过长"),
    EXTENSION_TAG_CODE_NOT_UPDATE("90023", "EXTENSION_TAG_CODE_NOT_UPDATE", "数据扩展标签不可变更"),
    EXTENSION_TAG_NAME_NOT_NULL("90023", "EXTENSION_TAG_NAME_NOT_NULL", "数据扩展标签名称不能为空"),

    /**
     * 为空
     */
    ROLE_IS_NULL("60501", "ROLE_IS_NULL", "角色为空"),
    POST_IS_NULL("60501", "POST_IS_NULL", "岗位为空"),
    POST_ID_IS_NULL("60501", "POST_ID_IS_NULL", "岗位ID为空"),
    NODE_ID_IS_NULL("60501", "NODE_ID_IS_NULL", "节点ID为空"),
    NODE_NAME_CN_IS_NOT_NULL("60501", "NODE_NAME_CN_IS_NOT_NULL", "中文节点名称不能为空"),
    NODE_NAME_IS_NOT_NULL("60501", "NODE_NAME_IS_NOT_NULL", "节点名称不能为空"),
    NODE_NAME_EN_IS_NOT_NULL("60501", "NODE_NAME_EN_IS_NOT_NULL", "英文节点名称不能为空"),
    USERINFODTO_IS_NULL("60501", "USERINFODTO_IS_NULL", "userInfoDTO 为空 "),
    RELATION_TYPE_IS_NULL("60501", "RELATION_TYPE_IS_NULL", "关联类型（主数据、函数数据）为空"),
    RELATION_TYPE_CODE_IS_NULL("60501", "RELATION_TYPE_CODE_IS_NULL", "关联数据类型为空"),
    RULE_CODE_IS_NULL("60501", "RULE_CODE_IS_NULL", "规则编码为空"),
    RULE_NAME_IS_NULL("60501", "RULE_NAME_IS_NULL", "规则名称为空"),
    RULE_TYPE_IS_NULL("60501", "RULE_TYPE_IS_NULL", "规则数据类型为空"),
    OP_IS_NULL("60501", "OP_IS_NULL", "操作符为空"),
    VALUE_TYPE_IS_NULL("60501", "VALUE_TYPE_IS_NULL", "值类型为空"),
    VALUE_CONTENT_IS_NULL("60501", "VALUE_CONTENT_IS_NULL", "值列表为空"),
    OP_NOT_SUPPORT("60501", "OP_NOT_SUPPORT", "操作符号不支持"),
    RULE_TYPE_NOT_EXIST("60501", "RULE_TYPE_NOT_EXIST", "规则数据类型不存在"),
    /**
     * 不存在
     */
    NOT_EXIST("70001", "NOT_EXIST", "数据不存在"),
    ROLE_NOT_EXIST("70001", "ROLE_NOT_EXIST", "角色不存在"),
    POST_NOT_EXIST("70002", "POST_NOT_EXIST", "岗位不存在，或被禁用"),
    HR_POST_NOT_EXIST("70002", "HR_POST_NOT_EXIST", "HR系统岗位不存在"),
    WORK_CENTER_POST_NOT_EXIST("70002", "WORK_CENTER_POST_NOT_EXIST", "HR系统岗位不存在"),
    WORK_CENTER_NOT_EXIST("70002", "WORK_CENTER_NOT_EXIST", "工作中心不存在"),
    AUTHORIZATION_TYPE_NOT_EXIST("60005", "AUTHORIZATION_TYPE_NOT_EXIST", "授权类型不存在"),
    DATA_PERMISSION_NOT_EXIST("60005", "DATA_PERMISSION_NOT_EXIST", "数据权限不存在"),
    TYPE_CODE_NOT_EXIST("60005", "TYPE_CODE_NOT_EXIST", "数据类型不存在"),
    USER_NOT_EXIST("60005", "USER_NOT_EXIST", "用户不存在"),
    BUSINESS_BASIC_DATA_CONFIG_NOT_EXIST("60005", "BUSINESS_BASIC_DATA_CONFIG_NOT_EXIST", "业务主数据配置不存在"),
    USER_ROLE_RELATION_NOT_EXIST("70010", "USER_ROLE_RELATION_NOT_EXIST", "用户角色綁定关系不存在"),
    USER_WORK_CENTER_RELATION_NOT_EXIST("70011", "USER_WORK_CENTER_RELATION_NOT_EXIST", "用户流程绑定关系不存在"),
    DATA_CODE_NOT_EXIST("70011", "DATA_CODE_NOT_EXIST", "DataCode 不存在"),
    DATA_URL_EXIST("70011", "DATA_URL_EXIST", "URL 重复"),
    OPTIONAL_PARAMETER_EXIST("70011", "OPTIONAL_PARAMETER_EXIST", "可选参数不存在"),
    DESCRIPTION_EXIST("70011", "DESCRIPTION_EXIST", "描述不存在"),
    DATA_CODE_EXIST("70011", "DATA_CODE_EXIST", "数据Code重复"),
    RULE_CODE_EXIST("60005", "RULE_CODE_EXIST", "数据维度参数名重复"),
    RULE_NAME_EXIST("60005", "RULE_NAME_EXIST", "数据维度名称重复"),
    CLIENT_DEFAULT_ROLE_EXIST("60005", "CLIENT_DEFAULT_ROLE_EXIST", "默认角色已存在"),
    /**
     * 已存在
     */
    ROLE_EXIST("70501", "ROLE_EXIST", "角色已存在"),
    CSP_ADMIN_ROLE_EXIST("70501", "CSP_ADMIN_ROLE_EXIST", "CSP管理员角色已存在"),
    POST_ROLE_RELATION_EXIST("70502", "POST_ROLE_RELATION_EXIST", "岗位与角色绑定关系已存在"),
    ROLE_NAME_EXIST("70502", "ROLE_NAME_EXIST", "角色中文名称已存在"),
    ROLE_NAME_EN_EXIST("70502", "ROLE_NAME_EN_EXIST", "角色英文名称已存在"),
    WORK_CENTER_NAME_EXIST("70502", "WORK_CENTER_NAME_EXIST", "工作中心名称已存在"),
    TYPE_CODE_EXIST("70502", "TYPE_CODE_EXIST", "数据类型/动态数据标识已重复"),
    /**
     * 重复
     */
    ROLE_INFORMATION_DUPLICATION("80001", "ROLE_INFORMATION_DUPLICATION", "角色信息重复"),
    DATA_CODE_DUPLICATION("80001", "DATA_CODE_DUPLICATION", "数据CODE信息重复"),
    /**
     * 其他
     */
    EDGES_STEP_NODE_ERROR("90001", "EDGES_STEP_NODE_ERROR", "节点关系与节点内容不符"),

    ZOOKEEPER_WRITE_PATH_ERROR("90002", "ZOOKEEPER_WRITE_PATH_ERROR", "获取写节点异常"),
    NO_WRITE_NODES_ARE_AVAILABLE("90003", "NO_WRITE_NODES_ARE_AVAILABLE", "没有可用的写节点"),
    DATA_URL_FORMAT_ERROR("90003", "DATA_URL_FORMAT_ERROR", "URL格式错误"),
    DATA_URL_IS_BLACK_URL("91000", "DATA_URL_IS_BLACK_URL", "URL在黑名单内，存在安全风险"),
    DATA_CHANGES_ARE_TOO_LARGE("90004", "DATA_CHANGES_ARE_TOO_LARGE", "数据变化太大，组织架构权限需要手动刷新"),
    PERMISSION_TYPE_NONSUPPORT("90005", "PERMISSION_TYPE_NONSUPPORT", "permissionType不支持"),
    POST_ID_LENGTH_GREATER_THAN_100("90005", "POST_ID_LENGTH_GREATER_THAN_100", "岗位列表长度不能大于100"),
    EFFECTIVE_TIME_DAY_LESS_THAN_0("60005", "EFFECTIVE_TIME_DAY_LESS_THAN_0", "过期时间天数不可以小于0"),
    THE_PERMISSION_HAS_BEEN_PERMANENTLY_AUTHORIZED("90005", "THE_PERMISSION_HAS_BEEN_PERMANENTLY_AUTHORIZED", "已有该权限，请勿重复申请"),
    AN_AUDIT_PROCESS_IS_IN_PLACE("90005", "AN_AUDIT_PROCESS_IS_IN_PLACE", "已存在审核流程"),
    AN_ONGOING_MENU_APPLICATION_PROCESS_FOR_THIS_SYSTEM_EXIST("90005", "AN_ONGOING_MENU_APPLICATION_PROCESS_FOR_THIS_SYSTEM_EXIST", "存在进行中的该系统菜单申请流程"),
    THE_APPROVAL_PROCESS_DOES_NOT_EXIST("90005", "THE_APPROVAL_PROCESS_DOES_NOT_EXIST", "审批流程不存在"),
    THERE_ARE_MULTIPLE_APPROVAL_PROCESSES("90005", "THERE_ARE_MULTIPLE_APPROVAL_PROCESSES", "存在多个审批流程"),
    THE_APPROVAL_SHALL_NOT_BE_RENEWED_UNLESS_REJECTED("90005", "THE_APPROVAL_SHALL_NOT_BE_RENEWED_UNLESS_REJECTED", "非驳回不可重新发起审批"),
    ROLE_IS_DISABLED("90005", "ROLE_IS_DISABLED", "角色被禁用"),
    THE_ROLE_IS_DELETED("90005", "THE_ROLE_IS_DELETED", "角色被删除"),
    TEXT_LENGTH_EXCEEDS_500_CHARACTERS("90005", "TEXT_LENGTH_EXCEEDS_500_CHARACTERS", "文本超长 500 字符"),
    REQUEST_DATA_EXCEEDS_LIMIT("90005", "REQUEST_DATA_EXCEEDS_LIMIT", "申请数据过多"),
    CASBIN_VALUE_NOT_NULL("90006", "CASBIN_VALUE_NOT_NULL", "casbin的值解析异常"),
    USER_CODE_LIST_TOO_LONG("90006", "USER_CODE_LIST_TOO_LONG", "userCodeList长度不能大于1000"),
    NO_PERMISSION("90006", "NO_PERMISSION", "无权限"),
    DONT_SUPER_ADMIN("90006", "DONT_SUPER_ADMIN", "不是超级管理员"),
    DONT_ADMIN("90006", "DONT_ADMIN", "不是管理员"),
    NO_SYSTEM_PERMISSIONS("90006", "NO_SYSTEM_PERMISSIONS", "没有系统权限"),
    SUBMIT_UNAUTHORIZED_DATA("90006", "SUBMIT_UNAUTHORIZED_DATA", "提交未授权数据"),
    SYSTEM_PERMISSION_UPDATE("90006", "SYSTEM_PERMISSION_UPDATE", "系统权限变更"),
    SYSTEM_CODE_GREATER_THAN_ONE("90006", "SYSTEM_CODE_GREATER_THAN_ONE", "系统code数量大于1"),
    SUB_ADMIN_DISABLE("90007", "SUB_ADMIN_DISABLE", "子管理员停用"),

    ADMINISTRATOR_STATUS_UPDATED("90010", "ADMINISTRATOR_STATUS_UPDATED", "管理员状态已更新"),
    WORK_CENTER_BINDING_USER("90008", "WORK_CENTER_BINDING_USER", "流程已绑定员工，不能删除"),

    EMPLOYEE_HAS_RESIGNED_OR_DISABLED("90009", "EMPLOYEE_HAS_RESIGNED_OR_DISABLED", "员工已离职或已禁用"),

    PERMISSION_TYPE_NOT_UPDATE("90008", "PERMISSION_TYPE_NOT_UPDATE", "审批类型无法修改"),
    WORK_CENTER_DISABLE("90008", "WORK_CENTER_DISABLE", "流程已停用"),
    SEND_EMAIL_ERROR("90009", "SEND_EMAIL_ERROR", "发送邮件失败"),
    REDIS_CACHE_ADD_FAILED("90009", "REDIS_CACHE_ADD_FAILED", "缓存添加失败"),
    REDIS_CACHE_DELETE_FAILED("90010", "REDIS_CACHE_DELETE_FAILED", "角色缓存删除失败"),
    ES_WRITE_FAILED("90011", "ES_WRITE_FAILED", "ES缓存写入失败"),
    REDISSON_RATE_LIMIT("90012", "REDISSON_RATE_LIMIT", "接口被限流，请稍后重试"),
    DATA_IS_REFERENCED_CANNOT_BE_DELETED("90012", "DATA_IS_REFERENCED_CANNOT_BE_DELETED", "数据被引用，不可删除"),
    DATA_URL_NOT_UPDATE("90012", "DATA_URL_NOT_UPDATE", "URL 不可变更"),
    OPTIONAL_PARAMETER_NOT_UPDATE("90012", "OPTIONAL_PARAMETER_NOT_UPDATE", "可选参数不可变更"),
    DESCRIPTION_NOT_UPDATE("90012", "DESCRIPTION_NOT_UPDATE", "描述不可变更"),
    CALL_URL_ERROR("90012", "CALL_URL_ERROR", "调用 URL 异常"),
    DATA_CODE_MORE_THAN_30_CHARACTERS("90012", "DATA_CODE_MORE_THAN_30_CHARACTERS", "data code 大于 30 字符"),
    USER_CODE_EXCEED_LIMIT("90013", "USER_CODE_EXCEED_LIMIT", "userCode数量超出限制"),
    OPTIONAL_PARAMETER_FORMAT_ERROR("90014", "OPTIONAL_PARAMETER_FORMAT_ERROR", "optionalParameter 格式错误"),
    DATA_URL_OPTIONAL_PARAMETER_EXIST("90015", "DATA_URL_OPTIONAL_PARAMETER_EXIST", "dataUrl 和 optionalParameter 组合已存在"),
    RULE_NAME_NOT_UPDATE("90016", "RULE_NAME_NOT_UPDATE", "ruleName 不可变更"),
    RULE_TYPE_NOT_UPDATE("90017", "RULE_TYPE_NOT_UPDATE", "ruleType 不可变更"),
    RULE_NAME_TOO_LONG("90018", "RULE_NAME_TOO_LONG", "ruleName 长度超出限制"),
    NUMBER_DATA_ERROR("90019", "NUMBER_DATA_ERROR", "数字数据异常"),
    MAIN_DATA_LIST_NOT_NULL("90020", "MAIN_DATA_LIST_NOT_NULL", "数据申请内容不能为空"),
    DEFAULT_CLIENT_ROLE_CANNOT_BE_MODIFIED("90021", "DEFAULT_CLIENT_ROLE_CANNOT_BE_MODIFIED", "默认商家角色不可变更"),
    CHILD_ROLE_CANNOT_BE_DELETED("90022", "CHILD_ROLE_CANNOT_BE_DELETED", "子角色不允许删除"),
    MERCHANT_CANNOT_ASSIGN_EMPLOYEE_ROLE("90023", "MERCHANT_CANNOT_ASSIGN_EMPLOYEE_ROLE", "子角色不允许删除"),
    CODE_TOO_LONG("90023", "CODE_TOO_LONG", "code 过长"),
    ROLE_NAME_TOO_LONG("90023", "ROLE_NAME_TOO_LONG", "role name 过长"),
    ROLE_NAME_EN_TOO_LONG("90023", "ROLE_NAME_EN_TOO_LONG", "role name en 过长"),
    DESCRIPTION_TOO_LONG("90023", "DESCRIPTION_TOO_LONG", "description 过长"),
    MENU_PERMISSION_TOO_MUCH("90023", "MENU_PERMISSION_TOO_MUCH", "menu permission 过多"),

    ROLE_BATCH_SIZE_EXCEED("90021", "ROLE_BATCH_SIZE_EXCEED", "角色单次处理数量限制不得超过50"),
    DATA_BEING_REFERENCED("90024", "DATA_BEING_REFERENCED", "数据被引用不可被删除"),
    SAME_SYSTEM_REFERENCE_ERROR("90024", "SAME_SYSTEM_REFERENCE_ERROR", "同系统不可进行数据引用"),
    OWNER_ONLY_EDIT_ERROR("90024", "OWNER_ONLY_EDIT_ERROR", "仅创建者可编辑"),
    SUBJECT_MODEL_CODE_NOT_NULL("90024", "SUBJECT_MODEL_CODE_NOT_NULL", "system code 不能为空"),
    SUBJECT_MODEL_NOT_EXIST("90024", "SUBJECT_MODEL_NOT_EXIST", "subject model code 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_NOT_NULL", "subject model 不存在"),
    SUBJECT_MODEL_DEFINE_JSON_METHOD_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_METHOD_NOT_NULL", "subject model define json 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_URL_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_URL_NOT_NULL", "subject model define json method 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_MAIN_ID_MAPPING_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_MAIN_ID_MAPPING_NOT_NULL", "subject model define json url 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_NODE_ID_MAPPING_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_NODE_ID_MAPPING_NOT_NULL", "subject model define json main id mapping 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_PARENT_NODE_ID_MAPPING_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_PARENT_NODE_ID_MAPPING_NOT_NULL", "subject model define json node id mapping 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_NAME_MAPPING_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_NAME_MAPPING_NOT_NULL", "subject model define json parent node id mapping 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_STATUS_MAPPING_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_STATUS_MAPPING_NOT_NULL", "subject model define json name mapping 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_CHILDREN_MAPPING_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_CHILDREN_MAPPING_NOT_NULL", "subject model define json status mapping 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_HEADER_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_HEADER_NOT_NULL", "subject model define json children mapping 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_PARAM_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_PARAM_NOT_NULL", "subject model define json header 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_LANG_DEFINE_NAME_MAPPING_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_LANG_DEFINE_NAME_MAPPING_NOT_NULL", "subject model define json param 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_SUBJECT_MODEL_CODE_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_SUBJECT_MODEL_CODE_NOT_NULL", "subject model define json lang define name mapping 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_MAIN_ID_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_MAIN_ID_NOT_NULL", "subject model define json url 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_TYPE_CODE_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_TYPE_CODE_NOT_NULL", "subject model define json subject model code 不能为空"),
    SUBJECT_MODEL_DEFINE_JSON_DATA_LIST_NOT_NULL("90024", "SUBJECT_MODEL_DEFINE_JSON_DATA_LIST_NOT_NULL", "subject model define json main id 不能为空"),


    RENEWAL_ONLY_SELF("60005", "RENEWAL_ONLY_SELF", "不可续期他人权限"),
    NOTICE_HAS_ALREADY_APPLIED("60005", "NOTICE_HAS_ALREADY_APPLIED", "该消息的权限已申请续期"),
    NOTICE_HAS_ALREADY_RENEWAL("60005", "NOTICE_HAS_ALREADY_RENEWAL", "权限已续期"),
    MENU_NOT_EXIST("60005", "MENU_NOT_EXIST", "菜单不存在"),
    USER_NOT_IN_WHITELIST("60005", "USER_NOT_IN_WHITELIST", "非白名单用户"),


    INTERFACE_RETURN_EMPTY("60005", "INTERFACE_RETURN_EMPTY", "业务接口未返回数据"),
    INTERFACE_PARSE_ERROR("60005", "INTERFACE_PARSE_ERROR", "解析接口返回数据失败,请检查配置接口的返回数据"),
    JSON_READ_TREE_ERROR("60005", "JSON_READ_TREE_ERROR", "转化JSON树状结构失败"),

    INTERFACE_ILLEGAL("60005", "INTERFACE_ILLEGAL", "非法接口"),

    FIELD_MAPPING_NOT_EXIST("60005", "FIELD_MAPPING_NOT_EXIST", "未配置字段映射"),
    TARGET_FIELD_DUPLICATION("60005", "TARGET_FIELD_DUPLICATION", "目标字段不可重复"),
    SOURCE_FIELD_DUPLICATION("60005", "SOURCE_FIELD_DUPLICATION", "来源字段不可重复"),
    SEARCH_FIELD_DUPLICATION("60005", "SEARCH_FIELD_DUPLICATION", "至多1个字段支持搜索"),
    JSON_PARSE_ERROR_PRIMITIVE_VALUE("60005", "JSON_PARSE_ERROR_PRIMITIVE_VALUE", "返回为基本类型"),
    USED_MAIN_DATA_FILED_CANNOT_UPDATE("60005", "USED_MAIN_DATA_FILED_CANNOT_UPDATE", "主数据已被使用，不可修改来源字段"),

    TREE_NO_CHILDREN_MAPPING("60005", "TREE_NO_CHILDREN_MAPPING", "树结构必须配置children映射"),
    NO_ID_MAPPING("60005", "NO_ID_MAPPING", "未配置id映射"),
    NO_NAME_MAPPING("60005", "NO_NAME_MAPPING", "未配置name映射"),
    CONFIG_ID_NOT_NULL("60005", "CONFIG_ID_NOT_NULL", "configId 不能为空"),
    DATA_NAME_DUPLICATION("60005", "DATA_NAME_DUPLICATION", "dataName 重复"),
    DATA_NAME_EN_DUPLICATION("60005", "DATA_NAME_EN_DUPLICATION", "dataNameEn 重复"),
    DATA_NAME_EN_NOT_NULL("60005", "DATA_NAME_EN_NOT_NULL", "dataNameEn 不能为空"),
    DATA_NAME_NOT_NULL("60005", "DATA_NAME_NOT_NULL", "dataName 不能为空"),
    DIMENSION_DATA_CODE_INFO_LIST_NOT_NULL("60005", "DIMENSION_DATA_CODE_INFO_LIST_NOT_NULL", "dimensionDataCodeInfoList 不能为空"),
    DIMENSION_DATA_URL_NOT_NULL("60005", "DIMENSION_DATA_URL_NOT_NULL", "dimensionDataUrl 不能为空"),
    DIMENSION_SUPPORT_MULTI_SELECT_NOT_NULL("60005", "DIMENSION_SUPPORT_MULTI_SELECT_NOT_NULL", "dimensionSupportMultiSelect 不能为空"),
    DIMENSION_TYPE_CODE_DUPLICATION("60005", "DIMENSION_TYPE_CODE_DUPLICATION", "dimensionTypeCode 重复"),
    DIMENSION_TYPE_CODE_NOT_NULL("60005", "DIMENSION_TYPE_CODE_NOT_NULL", "dimensionTypeCode 不能为空"),
    DIMENSION_TYPE_ENUM_ERROR("60005", "DIMENSION_TYPE_ENUM_ERROR", "类型错误"),
    DIMENSION_TYPE_NAME_EN_NOT_NULL("60005", "DIMENSION_TYPE_NAME_EN_NOT_NULL", "dimensionTypeNameEn 不能为空"),
    DIMENSION_TYPE_NAME_NOT_NULL("60005", "DIMENSION_TYPE_NAME_NOT_NULL", "dimensionTypeName 不能为空"),
    DIMENSION_TYPE_NOT_NULL("60005", "DIMENSION_TYPE_NOT_NULL", "dimensionType 不能为空"),
    REQUEST_TYPE_NOT_NULL("60005", "REQUEST_TYPE_NOT_NULL", "requestType 不能为空"),
    TYPE_NAME_EN_NOT_NULL("60005", "TYPE_NAME_EN_NOT_NULL", "typeNameEn 不能为空"),
    MULTI_DIMENSION_NOT_NULL("60005", "MULTI_DIMENSION_NOT_NULL", "multiDimension 不能为空"),
    DIMENSION_NOT_NULL("60005", "DIMENSION_NOT_NULL", "dimension 不能为空"),
    DIMENSION_TYPE_ERROR("60005", "DIMENSION_TYPE_ERROR", "dimensionType 类型错误"),
    DATA_NAME_NOT_UPDATE("60005", "DATA_NAME_NOT_UPDATE", "dataName 不可变更"),
    DATA_NAME_EN_NOT_UPDATE("60005", "DATA_NAME_EN_NOT_UPDATE", "dataNameEn 不可变更"),
    SINGLE_DIMENSION_NOT_NULL("60005", "SINGLE_DIMENSION_NOT_NULL", "单维度对象不可为空"),
    CUSTOM_DYNAMIC_ID_NOT_NULL("60005", "CUSTOM_DYNAMIC_ID_NOT_NULL", "动态数据ID 不可为空"),
    DYNAMIC_DATA_DIMENSION_CONFIG_NOT_EXIST("60005", "DYNAMIC_DATA_DIMENSION_CONFIG_NOT_EXIST", "动态数据不存在"),
    DIMENSION_DATA_LIST_NOT_NULL("60005", "DIMENSION_DATA_LIST_NOT_NULL", "维度数据不能为空"),
    ID_TOO_LONG("60005", "ID_TOO_LONG", "ID 过长"),
    LIMIT_SIZE_TOO_LONG("60005", "LIMIT_SIZE_TOO_LONG", "limit 过长"),


    ARRAY_FIELD_MAPPING_ARRAY("60005", "ARRAY_FIELD_MAPPING_ARRAY", "Array类型字段来源类型必须为Array"),
    BATCH_ADD_ROLE_EXCEED_LIMIT("90013", "BATCH_ADD_ROLE_EXCEED_LIMIT", "批量新增角色权限数量超过限制"),
    BATCH_REMOVE_ROLE_EXCEED_LIMIT("90013", "BATCH_REMOVE_ROLE_EXCEED_LIMIT", "批量移除角色权限数量超过限制"),

    APPLY_DATA_NOT_NULL("60005", "APPLY_DATA_NOT_NULL", "申请的数据为空"),
    USER_TOO_LONG("60005", "USER_TOO_LONG", "用户过长"),
    ROLE_TOO_LONG("60005", "ROLE_TOO_LONG", "角色过长"),
    MENU_TOO_LONG("60005", "MENU_TOO_LONG", "菜单过长"),
    DATACODE_TOO_LONG("60005", "DATACODE_TOO_LONG", "数据过长"),
    BATCH_AUTHORIZATION_DIRECT_SAVE_LOCK_EXIST_INFO("60005", "BATCH_AUTHORIZATION_DIRECT_SAVE_LOCK_EXIST_INFO", "批量授权任务进行中，请稍后重试"),
    BATCH_AUTHORIZATION_DIRECT_SAVE_LOCK_EXIST("60005", "BATCH_AUTHORIZATION_DIRECT_SAVE_LOCK_EXIST", "批量授权任务进行中"),
    BATCH_AUTHORIZATION_DIRECT_SAVE_FAILURE("60005", "BATCH_AUTHORIZATION_DIRECT_SAVE_FAILURE", "批量授权失败"),

    TYPE_CODE_RESERVED("60005", "TYPE_CODE_RESERVED", "该typeCode为保留编码"),
    NO_PERMISSION_CHANGE("60005", "NO_PERMISSION_CHANGE", "无权限变动")
    ;


    /**
     * 业务错误码
     */
    private final String code;
    /**
     * 错误描述(国际化文件)
     */
    private final String desc;
    /**
     * 错误描述(方便调用方理解)
     */
    private final String message;

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }


    /**
     * @param code    code
     * @param desc    desc
     * @param message message
     */
    private PermissionErrorCodeEnums(String code, String desc, String message) {
        this.code = code;
        this.desc = desc;
        this.message = message;
    }

    /**
     * @param code code
     * @return PermissionErrorCodeEnums
     */
    public static PermissionErrorCodeEnums getEnumByCode(String code) {
        for (PermissionErrorCodeEnums e : PermissionErrorCodeEnums.values()) {
            if (e.code.equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * @param code code
     * @return String
     */
    public static String getDescByCode(String code) {
        for (PermissionErrorCodeEnums e : PermissionErrorCodeEnums.values()) {
            if (e.code.equalsIgnoreCase(code)) {
                return e.getDesc();
            }
        }
        return null;
    }

    /**
     * @param code code
     * @return String
     */
    public static String getMessageByCode(String code) {
        for (PermissionErrorCodeEnums e : PermissionErrorCodeEnums.values()) {
            if (e.code.equalsIgnoreCase(code)) {
                return e.getMessage();
            }
        }
        return null;
    }
}

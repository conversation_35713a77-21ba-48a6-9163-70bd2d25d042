package com.imile.permission.enums;


import lombok.Getter;

@Getter
public enum PermissionDataTypeEnum {
    BASIC_DATA(1, "基础数据"),
    MAIN_DATA(2, "主数据"),
    DYNAMIC_DATA(3, "动态函数"),
    SINGLE_DIMENSION_DATA(4, "单维度数据"),
    MULTI_DIMENSION_DATA(5, "多维度数据"),
    ;

    PermissionDataTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    /**
     * 前缀
     */
    private Integer code;

    /**
     * 模块说明
     */
    private String desc;


    public static PermissionDataTypeEnum getEnumByCode(Integer code) {
        for (PermissionDataTypeEnum value : PermissionDataTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}


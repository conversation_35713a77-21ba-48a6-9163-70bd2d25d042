package com.imile.permission.enums;

import lombok.Getter;

@Getter
public enum DataErrorStatus {

    DISABLED("DISABLED", "已禁用", "DISABLED"),
    OFFLINE("OFFLINE", "已下线", "OFFLINE"),

    ;
    /**
     * 前缀
     */
    private String code;
    private String cn;
    private String en;

    DataErrorStatus(String code, String cn, String en) {
        this.code = code;
        this.cn = cn;
        this.en = en;
    }

}

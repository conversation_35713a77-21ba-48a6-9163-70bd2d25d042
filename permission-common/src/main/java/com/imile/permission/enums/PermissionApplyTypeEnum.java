package com.imile.permission.enums;


import lombok.Getter;

@Getter
public enum PermissionApplyTypeEnum {
    Role(1, "角色申请单"),
    WORK_CENTER(2, "流程申请"),
    MENU(3, "菜单申请"),
    MAIN_DATA(4, "主数据申请"),
    DATA(5, "数据申请"),
    ;

    PermissionApplyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    /**
     * 前缀
     */
    private Integer code;

    /**
     * 模块说明
     */
    private String desc;


    public static PermissionApplyTypeEnum getEnumByCode(Integer code) {
        for (PermissionApplyTypeEnum value : PermissionApplyTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}


package com.imile.permission.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassDescription:
 * @author: long.gao
 * @date: 2023.12.19
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequestLog {

    /**
     * 日志内容
     *
     * @return
     */
    String value() default "";

    boolean isPrintResult() default  true;
}

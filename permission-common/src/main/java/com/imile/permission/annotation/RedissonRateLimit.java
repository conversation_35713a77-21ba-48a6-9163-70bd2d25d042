package com.imile.permission.annotation;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 暂时没有地方使用，用的时候需要测试一下
 */
@Target({ElementType.METHOD}) // 此注解只能用在方法上
@Retention(RetentionPolicy.RUNTIME) // 注解的作用域为JVM运行时
public @interface RedissonRateLimit {
    /**
     * 限流标识key，每个http接口都应该有一个唯一的key。
     *
     * @return
     */
    String key();

    /**
     * 限流的时间(单位为:秒)，比如1分钟内最多1000个请求。注意我们这个限流器不是很精确，但误差不会太大
     *
     * @return
     */
    long timeOut();

    /**
     * 限流的次数，比如1分钟内最多1000个请求。注意count的值不能小于1,必须大于等于1
     *
     * @return
     */
    long count();
}

package com.imile.permission.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * henry
 * 数字字典转换
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HyperLink {

    /**
     * 指定value 值写入字段
     *
     * @return
     */
    String ref();
}

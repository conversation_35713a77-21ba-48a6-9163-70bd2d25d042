package com.imile.permission.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 通过数据字典的数据进行转换, 此类数据使用分隔符写在一个字段中
 * 在将数据字典dataCode转换为dataValue时,dataCode可能存储了多个
 * Noah
 * 数字字典转换
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface WithDictSeparator {
    /**
     * 指定字典类型
     *
     * @return
     */
    String typeCode();

    /***
     * 元数据分隔符
     * @return
     */
    String sSeparator() default ",";

    /**
     * 目标数据分隔符
     *
     * @return
     */
    String tSeparator() default ",";

    /**
     * 指定value 值写入字段
     *
     * @return
     */
    String ref();
}

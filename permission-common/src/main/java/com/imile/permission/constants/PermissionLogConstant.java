package com.imile.permission.constants;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */
public class PermissionLogConstant {

    public static final String MODULE_SUB_ADMIN_MANAGER = "SUB_ADMIN_MANAGE";
    public static final String MODULE_POST_MANAGE = "POST_MANAGE";
    public static final String MODULE_DATA_PERMISSION_MANAGE = "DATA_PERMISSION_MANAGE";
    public static final String MODULE_ROLE_MANAGE = "ROLE_MANAGE";
    public static final String MODULE_FLOW_MANAGE = "FLOW_MANAGE";
    public static final String MODULE_USER_AUTH_MANAGE = "MODULE_USER_AUTH_MANAGE";
    public static final String MODULE_USER_COLVER_APPLY = "USER_COLVER_APPLY";

    public static final String RPC_USER_DATA_PERMISSION = "RPC_USER_DATA_PERMISSION";
    public static final String RPC_USER_ROLE = "RPC_USER_ROLE";
    public static final String RPC_ROLE_MANAGE = "RPC_ROLE_MANAGE";



    public static final String TYPE_ADD = "ADD";
    public static final String TYPE_DELETE = "DELETE";
    public static final String TYPE_UPDATE = "UPDATE";
    public static final String TYPE_DISABLED = "DISABLED";
    public static final String TYPE_BIND_ROLE = "BIND_ROLE";
    public static final String TYPE_BIND_WORK_FLOW = "BIND_WORK_FLOW";
    public static final String TYPE_BIND_PERMISSION = "BIND_PERMISSION";
    public static final String TYPE_REFRESH_PERMISSION = "REFRESH_PERMISSION";
    public static final String TYPE_CLOVER_APPLY_ROLE = "CLOVER_APPLY_ROLE";
    public static final String TYPE_CLOVER_APPLY_FLOW = "CLOVER_APPLY_FLOW";
    public static final String TYPE_CLOVER_APPLY_MENU = "CLOVER_APPLY_MENU";
    public static final String TYPE_CLOVER_RECYCLE = "CLOVER_RECYCLE";
    public static final String TYPE_EDIT_CUSTOM_DYNAMIC_FUNCTION_LIST = "EDIT_CUSTOM_DYNAMIC_FUNCTION_LIST";
    public static final String TYPE_EDIT_CUSTOM_DYNAMIC_RULE_LIST = "EDIT_CUSTOM_DYNAMIC_RULE_LIST";

    public static final String TYPE_USER_BIND_DATA_PERMISSION = "TYPE_USER_BIND_DATA_PERMISSION";
    public static final String TYPE_USER_ROLE_EDIT = "TYPE_USER_ROLE_EDIT";
    public static final String TYPE_USER_ROLE_BIND = "TYPE_USER_ROLE_BIND";
    public static final String TYPE_USER_ROLE_REMOVE = "TYPE_USER_ROLE_REMOVE";

    public static final String TYPE_ROLE_ADD = "TYPE_ROLE_ADD";
    public static final String TYPE_ROLE_UPDATE = "TYPE_ROLE_UPDATE";
    public static final String TYPE_ROLE_DELETE = "TYPE_ROLE_DELETE";
    public static final String TYPE_ROLE_DISABLE = "TYPE_ROLE_DISABLE";
    public static final String TYPE_ROLE_PERMISSION_BATCH_SAVE = "TYPE_ROLE_DISABLE";
    public static final String MODULE_AUTHORIZATION_SUBJECT_MODEL = "AUTHORIZATION_SUBJECT_MODEL";


    public static final String TYPE_MODEL_UPDATE = "MODEL_UPDATE";
    public static final String TYPE_SYSTEM_MODEL_UPDATE = "SYSTEM_MODEL_UPDATE";



    public static final String MODULE_REPORT_EXCEL_ROLE_MANAGE = "REPORT_EXCEL_ROLE_MANAGE";
    public static final String TYPE_ROLE_PAGE_EXPORT = "ROLE_PAGE_EXPORT";
    public static final String TYPE_AUTH_SOURCE_USER_PAGE_EXPORT = "AUTH_SOURCE_USER_PAGE_EXPORT";
    public static final String TYPE_USER_PAGE_EXPORT = "USER_PAGE_EXPORT";
    public static final String TYPE_BATCH_DIRECT_SAVE = "BATCH_DIRECT_SAVE";
    public static final String TYPE_BATCH_BIND_ROLE = "BATCH_BIND_ROLE";
}

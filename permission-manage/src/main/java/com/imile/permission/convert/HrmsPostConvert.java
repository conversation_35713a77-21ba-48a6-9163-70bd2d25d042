package com.imile.permission.convert;

import com.google.common.collect.Lists;
import com.imile.hrms.api.base.result.PostDTO;
import com.imile.hrms.api.organization.dto.EntPostApiDTO;
import com.imile.hrms.api.organization.query.SelectUserInfoApiQuery;
import com.imile.permission.domain.relation.vo.PostVO;
import com.imile.permission.domain.subadmin.param.UserParam;
import com.imile.permission.domain.user.dto.UserInfoDetailQuery;
import com.imile.util.user.UserEvnHolder;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @since 2024/6/11
 */
public class HrmsPostConvert {

    public static SelectUserInfoApiQuery convertToUserInfoApiQuery(UserParam userParam) {
        if (userParam == null) {
            return null;
        }
        SelectUserInfoApiQuery query = new SelectUserInfoApiQuery();
        query.setUserCodeOrName(userParam.getUserCodeOrName());
        query.setPageNum(userParam.getCurrentPage());
        query.setPageSize(userParam.getShowCount());
        return query;
    }


    public static UserInfoDetailQuery convertToUserInfoDetailQuery(UserParam userParam) {
        if (userParam == null) {
            return null;
        }
        UserInfoDetailQuery query = new UserInfoDetailQuery();
        query.setUserCodeOrName(userParam.getUserCodeOrName());
        query.setPageNum(userParam.getCurrentPage());
        query.setPageSize(userParam.getShowCount());
        return query;
    }

    public static List<PostVO> getPostVOS(List<EntPostApiDTO> results, Locale local) {
        if (CollectionUtils.isEmpty(results)) {
            return Lists.newArrayList();
        }
        List<PostVO> list = Lists.newArrayList();
        results.forEach(postApiDTO -> {

            PostVO postVO = new PostVO();
            postVO.setPostId(postApiDTO.getId());
            postVO.setPostName(local.equals(Locale.US) ? postApiDTO.getPostNameEn() : postApiDTO.getPostNameCn());
            postVO.setStatus(postApiDTO.getStatus());
            list.add(postVO);
        });
        return list;
    }

    public static List<PostVO> postDTOToPostVO(List<PostDTO> results) {
        if (CollectionUtils.isEmpty(results)) {
            return Lists.newArrayList();
        }
        List<PostVO> list = Lists.newArrayList();
        results.forEach(postApiDTO -> {

            PostVO postVO = new PostVO();
            postVO.setPostId(Long.valueOf(postApiDTO.getPostCode()));
            postVO.setPostName(postApiDTO.getPostName());
            postVO.setStatus(postApiDTO.getStatus());
            list.add(postVO);
        });
        return list;
    }
}

package com.imile.permission.convert;

import com.google.common.collect.Lists;
import com.imile.hrms.api.organization.dto.OrgUserInfoDetailApiDTO;
import com.imile.permission.domain.user.dto.UserCodeDTO;
import com.imile.permission.domain.user.vo.AssociatedUserVO;
import com.imile.permission.enums.DataErrorStatus;
import com.imile.util.user.UserEvnHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/14
 */
public class AssociatedUserConvert {

    public static List<AssociatedUserVO> convertToUser(List<OrgUserInfoDetailApiDTO> orgUserInfoDetailApiDTOS,
                                                       List<UserCodeDTO> results, Map<Long, String> deptChainMergedNameMap) {

        if (CollectionUtils.isEmpty(results)) {
            return Lists.newArrayList();
        }

        List<AssociatedUserVO> voList = new ArrayList<>();
        Map<String, OrgUserInfoDetailApiDTO> map = Maps.newHashMap();
        // 禁用账号检查
        if (CollectionUtils.isNotEmpty(orgUserInfoDetailApiDTOS)) {
            map = orgUserInfoDetailApiDTOS.stream().collect(
                    Collectors.toMap(OrgUserInfoDetailApiDTO::getUserCode, Function.identity(), (v1, v2) -> v1));
        }

        Map<String, OrgUserInfoDetailApiDTO> finalMap = map;
        results.forEach(e -> {
            AssociatedUserVO associatedUserVO = new AssociatedUserVO();

            OrgUserInfoDetailApiDTO orgUserInfoDetailApiDTO = finalMap.get(e.getUserCode());
            if (Objects.nonNull(orgUserInfoDetailApiDTO)) {
                associatedUserVO.setUserCode(e.getUserCode());
                associatedUserVO.setUserName(orgUserInfoDetailApiDTO.getUserName());
                associatedUserVO.setDeptId(orgUserInfoDetailApiDTO.getDeptId());
                associatedUserVO.setDeptName(deptChainMergedNameMap.get(orgUserInfoDetailApiDTO.getDeptId()));
                associatedUserVO.setPostName(orgUserInfoDetailApiDTO.getPostName());
            } else {
                String name = UserEvnHolder.getLocal().equals(Locale.US) ?
                        DataErrorStatus.OFFLINE.getEn() : DataErrorStatus.OFFLINE.getCn();
                associatedUserVO.setUserCode(e + String.format("(%s)", name));
            }
            voList.add(associatedUserVO);
        });
        return voList;
    }
}

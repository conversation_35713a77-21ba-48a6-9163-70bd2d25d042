package com.imile.permission.convert;

import com.imile.permission.domain.dataPermission.query.DataEqualRightsQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/6
 */
public class DataEqualRightsConvert {

    public static DataEqualRightsQuery convertQuery(List<String> systemCodeList, Integer type) {
        DataEqualRightsQuery query = new DataEqualRightsQuery();
        query.setSystemCodeList(systemCodeList);
        query.setType(type);
        return query;
    }
}

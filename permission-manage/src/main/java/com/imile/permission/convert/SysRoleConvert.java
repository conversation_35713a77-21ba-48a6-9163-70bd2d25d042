package com.imile.permission.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.domain.applicationApprove.vo.UserDefaultRoleVO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.permission.dto.AdminDTO;
import com.imile.permission.domain.role.dto.RoleDetailDTO;
import com.imile.permission.domain.role.dto.RolePropertiesDTO;
import com.imile.permission.domain.role.dto.SysRoleDTO;
import com.imile.permission.domain.role.param.RoleAddPermissionRuleParam;
import com.imile.permission.domain.role.param.RoleBasicParam;
import com.imile.permission.domain.role.param.RoleUpdatePermissionRuleParam;
import com.imile.permission.domain.role.vo.ParentChildRoleVO;
import com.imile.permission.domain.role.vo.RoleEditableVO;
import com.imile.permission.domain.role.vo.RolePageVO;
import com.imile.permission.enums.RoleApplyTypeEnum;
import com.imile.permission.enums.RoleAuthSceneEnum;
import com.imile.permission.enums.RoleTypeEnum;
import com.imile.permission.util.OrikaUtil;
import com.imile.permission.util.PermissionCollectionUtils;
import com.imile.util.user.UserEvnHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
public class SysRoleConvert {
    public static RoleDetailDTO doToRoleDetailDTO(SysRoleDO sysRoleDO) {
        if (sysRoleDO == null) {
            return null;
        }

        RoleDetailDTO roleDetailDTO = new RoleDetailDTO();
        roleDetailDTO.setRoleName(sysRoleDO.getRoleName());
        roleDetailDTO.setDescription(sysRoleDO.getDescription());
        roleDetailDTO.setRoleNameEn(sysRoleDO.getRoleNameEn());
        roleDetailDTO.setDescriptionEn(sysRoleDO.getDescriptionEn());
        roleDetailDTO.setBusinessType(sysRoleDO.getBusinessType());
        roleDetailDTO.setIsAccessApprovalFlow(sysRoleDO.getIsAccessApprovalFlow());
        roleDetailDTO.setRoleType(sysRoleDO.getRoleType());
        roleDetailDTO.setAuthScene(sysRoleDO.getAuthScene());
        roleDetailDTO.setParentId(sysRoleDO.getParentId());

        String rolePropertiesJson = sysRoleDO.getRolePropertiesJson();
        if (StringUtils.isNotBlank(rolePropertiesJson)) {
            RolePropertiesDTO rolePropertiesDTO = JSON.parseObject(rolePropertiesJson, RolePropertiesDTO.class);
            if (Objects.nonNull(rolePropertiesDTO)) {
                roleDetailDTO.setApplyType(rolePropertiesDTO.getApplyType());
                roleDetailDTO.setEmploymentTypeList(rolePropertiesDTO.getEmploymentTypeList());
            }
        }
        return roleDetailDTO;


    }

    public static SysRoleDO convertAddRoleDO(RoleAddPermissionRuleParam roleAddPermissionRuleParam) {

        if (roleAddPermissionRuleParam == null || roleAddPermissionRuleParam.getRoleBasicParam() == null) {
            return null;
        }

        RoleBasicParam roleBasicParam = roleAddPermissionRuleParam.getRoleBasicParam();
        SysRoleDO sysRoleDO = new SysRoleDO();
        sysRoleDO.setRoleName(roleBasicParam.getRoleName());
        sysRoleDO.setRoleNameEn(roleBasicParam.getRoleNameEn());
        sysRoleDO.setDescription(roleBasicParam.getDescription());
        sysRoleDO.setDescriptionEn(roleBasicParam.getDescriptionEn());
        sysRoleDO.setBusinessType(roleBasicParam.getBusinessType());
        sysRoleDO.setIsAccessApprovalFlow(roleBasicParam.getIsAccessApprovalFlow());
        sysRoleDO.setRoleType(roleBasicParam.getRoleType());
        sysRoleDO.setAuthScene(roleBasicParam.getAuthScene());
//        sysRoleDO.setParentId(roleBasicParam.getParentId());
        // 角色属性
        sysRoleDO.setRolePropertiesJson(buildRolePropertiesJson(roleBasicParam));
        // 激活
        sysRoleDO.setIsDisable(BusinessConstant.N);
        sysRoleDO.setRoleCountry(JSON.toJSONString(roleBasicParam.getRoleCountryList()));

        // 系统平台
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(roleAddPermissionRuleParam.getMenuPermissionDTOList()).orElse(Collections.emptyList());

        List<String> systemPlatformList = menuPermissionDTOList.stream()
                .map(MenuPermissionDTO::getSystemPlatform)
                .collect(Collectors.toList());
        if (PermissionCollectionUtils.isNotEmpty(systemPlatformList, StringUtils::isNotBlank)) {
            systemPlatformList = systemPlatformList.stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            sysRoleDO.setSystemPlatformJson(JSONArray.toJSONString(systemPlatformList));
        }
        // 关联系统
        List<String> systemCodeList = roleBasicParam.getSystemCodeList();
        if (CollectionUtils.isNotEmpty(systemCodeList)) {
            sysRoleDO.setMultipleSystem(JSONArray.toJSONString(systemCodeList));
        }
        return sysRoleDO;
    }

    public static SysRoleDO convertUpdateRoleDO(RoleUpdatePermissionRuleParam roleUpdatePermissionRuleParam) {

        if (roleUpdatePermissionRuleParam == null || roleUpdatePermissionRuleParam.getRoleBasicParam() == null) {
            return null;
        }

        RoleBasicParam roleBasicParam = roleUpdatePermissionRuleParam.getRoleBasicParam();
        SysRoleDO sysRoleDO = new SysRoleDO();
        sysRoleDO.setId(roleUpdatePermissionRuleParam.getId());
        sysRoleDO.setRoleName(roleBasicParam.getRoleName());
        sysRoleDO.setRoleNameEn(roleBasicParam.getRoleNameEn());
        sysRoleDO.setDescription(roleBasicParam.getDescription());
        sysRoleDO.setDescriptionEn(roleBasicParam.getDescriptionEn());
        sysRoleDO.setBusinessType(roleBasicParam.getBusinessType());
        sysRoleDO.setIsAccessApprovalFlow(roleBasicParam.getIsAccessApprovalFlow());
        sysRoleDO.setRoleType(roleBasicParam.getRoleType());
        sysRoleDO.setAuthScene(roleBasicParam.getAuthScene());
//        sysRoleDO.setParentId(roleBasicParam.getParentId());
        // 角色属性
        sysRoleDO.setRolePropertiesJson(buildRolePropertiesJson(roleBasicParam));
        // 激活
        // sysRoleDO.setIsDisable(BusinessConstant.N);
        sysRoleDO.setRoleCountry(JSON.toJSONString(roleBasicParam.getRoleCountryList()));

        // 系统平台
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(roleUpdatePermissionRuleParam.getMenuPermissionDTOList()).orElse(Collections.emptyList());

        List<String> systemPlatformList = menuPermissionDTOList.stream()
                .map(MenuPermissionDTO::getSystemPlatform)
                .collect(Collectors.toList());
        if (PermissionCollectionUtils.isNotEmpty(systemPlatformList, StringUtils::isNotBlank)) {
            systemPlatformList = systemPlatformList.stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            sysRoleDO.setSystemPlatformJson(JSONArray.toJSONString(systemPlatformList));
        }
        // 关联系统
        List<String> systemCodeList = roleBasicParam.getSystemCodeList();
        if (CollectionUtils.isNotEmpty(systemCodeList)) {
            sysRoleDO.setMultipleSystem(JSONArray.toJSONString(systemCodeList));
        }
        return sysRoleDO;
    }

    private static String buildRolePropertiesJson(RoleBasicParam roleBasicParam) {

        List<String> employmentTypeList = roleBasicParam.getEmploymentTypeList();
        Integer applyType = roleBasicParam.getApplyType();

        RolePropertiesDTO rolePropertiesDTO = new RolePropertiesDTO();
        rolePropertiesDTO.setApplyType(applyType);
        rolePropertiesDTO.setEmploymentTypeList(employmentTypeList);
        return JSON.toJSONString(rolePropertiesDTO);
    }

    public static ParentChildRoleVO.RoleVO sysRoleDOToRoleVO(SysRoleDO role) {
        if (Objects.isNull(role)) {
            return null;
        }

        ParentChildRoleVO.RoleVO roleVO = new ParentChildRoleVO.RoleVO();
        roleVO.setId(role.getId());
        roleVO.setIsDisable(role.getIsDisable());
        roleVO.setRoleName(UserEvnHolder.getLocal().equals(Locale.US) ? role.getRoleNameEn() : role.getRoleName());
        roleVO.setDescription(role.getDescription());
        roleVO.setBusinessType(role.getBusinessType());
        String multipleSystem = role.getMultipleSystem();
        roleVO.setMultipleSystem(multipleSystem);
        roleVO.setParentId(role.getParentId());
        if (StringUtils.isNotBlank(multipleSystem)) {
            roleVO.setSystemCodeList(JSONArray.parseArray(multipleSystem, String.class));
        }
        String roleCountry = role.getRoleCountry();
        if (StringUtils.isNotBlank(roleCountry)) {
            roleVO.setRoleCountryList(JSON.parseArray(roleCountry, String.class));
        }
        roleVO.setRoleType(role.getRoleType());
        return roleVO;
    }

    public static RoleEditableVO convertRoleEditableVO(SysRoleDO role, Locale local, AdminDTO adminDTO) {
        if (Objects.isNull(role)) {
            return null;
        }
        RoleEditableVO roleVO = OrikaUtil.map(role, RoleEditableVO.class);
        roleVO.setRoleCountryList(JSON.parseArray(role.getRoleCountry(), String.class));
        roleVO.setRoleName(local.equals(Locale.US) ? role.getRoleNameEn() : role.getRoleName());
        roleVO.setDescription(local.equals(Locale.US) ? role.getDescriptionEn() : role.getDescription());
        roleVO.setRoleTypeDesc(RoleTypeEnum.getCnOrEnDesc(role.getRoleType(), local));
        String rolePropertiesJson = role.getRolePropertiesJson();
        if (StringUtils.isNotEmpty(rolePropertiesJson)) {
            RolePropertiesDTO rolePropertiesDTO = JSON.parseObject(rolePropertiesJson, RolePropertiesDTO.class);
            roleVO.setEmploymentTypeList(rolePropertiesDTO.getEmploymentTypeList());
            roleVO.setEmploymentTypeDescList(Optional.ofNullable(rolePropertiesDTO.getEmploymentTypeList()).orElse(Lists.newArrayList()).stream().map(e ->
                            UserEvnHolder.getLocal().equals(Locale.US) ? EmploymentTypeEnum.valueOfCode(e).getDescEn() : EmploymentTypeEnum.valueOfCode(e).getDesc())
                    .collect(Collectors.toList()));
        }
        roleVO.setSystemCodeList(JSONArray.parseArray(role.getMultipleSystem(), String.class));
        roleVO.setEditable(false);
        // 超管可以编辑
        if (adminDTO.getIsSuperAccount()) {
            roleVO.setEditable(true);
        }
        // 子管理员，包含角色的系统，可以编辑
        if (adminDTO.getIsSysSubAdmin() && CollectionUtils.isNotEmpty(adminDTO.getSystemList()) && adminDTO.getSystemList().containsAll(roleVO.getSystemCodeList())) {
            roleVO.setEditable(true);
        }
        return roleVO;
    }

    public static List<SysRoleDTO> convertSysRoleDTO(List<SysRoleDO> select) {
        List<SysRoleDTO> sysRoleDTOS = Optional.ofNullable(select).orElse(Collections.emptyList()).stream().map(SysRoleConvert::convertSysRoleDTO).filter(Objects::nonNull).collect(Collectors.toList());
        return sysRoleDTOS;
    }

    public static SysRoleDTO convertSysRoleDTO(SysRoleDO sysRoleDO) {
        if (Objects.isNull(sysRoleDO)) {
            return null;
        }
        SysRoleDTO sysRoleDTO = new SysRoleDTO();
        sysRoleDTO.setId(sysRoleDO.getId());
        sysRoleDTO.setRoleName(sysRoleDO.getRoleName());
        sysRoleDTO.setRoleNameEn(sysRoleDO.getRoleNameEn());
        sysRoleDTO.setDescription(sysRoleDO.getDescription());
        sysRoleDTO.setDescriptionEn(sysRoleDO.getDescriptionEn());
        sysRoleDTO.setBusinessType(sysRoleDO.getBusinessType());
        sysRoleDTO.setIsAccessApprovalFlow(sysRoleDO.getIsAccessApprovalFlow());
        sysRoleDTO.setRoleType(sysRoleDO.getRoleType());
        sysRoleDTO.setAuthScene(sysRoleDO.getAuthScene());
        sysRoleDTO.setIsDisable(sysRoleDO.getIsDisable());
        sysRoleDTO.setRoleCountry(sysRoleDO.getRoleCountry());
        sysRoleDTO.setSystemPlatformJson(sysRoleDO.getSystemPlatformJson());
        sysRoleDTO.setMultipleSystem(sysRoleDO.getMultipleSystem());
        sysRoleDTO.setSystemCodeList(JSONArray.parseArray(sysRoleDO.getMultipleSystem(), String.class));
        if (StringUtils.isNotEmpty(sysRoleDO.getRolePropertiesJson())) {
            RolePropertiesDTO rolePropertiesDTO = JSON.parseObject(sysRoleDO.getRolePropertiesJson(), RolePropertiesDTO.class);
            sysRoleDTO.setApplyType(rolePropertiesDTO.getApplyType());
            sysRoleDTO.setEmploymentTypeList(rolePropertiesDTO.getEmploymentTypeList());
        }
        Optional.ofNullable(sysRoleDO.getRoleCountry()).ifPresent(country -> sysRoleDTO.setRoleCountryList(JSON.parseArray(country, String.class)));
        return sysRoleDTO;
    }

    public static List<RoleEditableVO> convertRoleEditable(List<SysRoleDTO> postDefaultRoleList, Locale local) {
        List<RoleEditableVO> list = new ArrayList<>();
        Optional.ofNullable(postDefaultRoleList).orElse(Collections.emptyList()).forEach(e -> {
            RoleEditableVO roleEditableVO = new RoleEditableVO();
            roleEditableVO.setId(e.getId());
            roleEditableVO.setIsDisable(e.getIsDisable());
            roleEditableVO.setMultipleSystem(e.getMultipleSystem());
            roleEditableVO.setRoleType(e.getRoleType());
            roleEditableVO.setEditable(false);
            roleEditableVO.setRoleTypeDesc(RoleTypeEnum.getCnOrEnDesc(e.getRoleType(), local));
            roleEditableVO.setRoleName(local.equals(Locale.US) ? e.getRoleNameEn() : e.getRoleName());
            roleEditableVO.setDescription(local.equals(Locale.US) ? e.getDescriptionEn() : e.getDescription());
            roleEditableVO.setRoleCountryList(JSON.parseArray(e.getRoleCountry(), String.class));
            roleEditableVO.setEmploymentTypeList(e.getEmploymentTypeList());
            roleEditableVO.setEmploymentTypeDescList(Optional.ofNullable(e.getEmploymentTypeList()).orElse(Lists.newArrayList()).stream().map(r ->
                            UserEvnHolder.getLocal().equals(Locale.US) ? EmploymentTypeEnum.valueOfCode(r).getDescEn() : EmploymentTypeEnum.valueOfCode(r).getDesc())
                    .collect(Collectors.toList()));
            roleEditableVO.setSystemCodeList(JSONArray.parseArray(e.getMultipleSystem(), String.class));
            list.add(roleEditableVO);
        });
        return list;
    }

    public static RolePageVO convertRolePageVO(SysRoleDO roleDO) {
        RolePageVO vo = OrikaUtil.map(roleDO, RolePageVO.class);
        String rolePropertiesJson = roleDO.getRolePropertiesJson();
        if (StringUtils.isNotEmpty(rolePropertiesJson)) {
            RolePropertiesDTO rolePropertiesDTO = JSON.parseObject(rolePropertiesJson, RolePropertiesDTO.class);
            vo.setApplyType(rolePropertiesDTO.getApplyType());
            vo.setEmploymentTypeList(rolePropertiesDTO.getEmploymentTypeList());
            vo.setEmploymentTypeDescList(Optional.ofNullable(rolePropertiesDTO.getEmploymentTypeList()).orElse(Lists.newArrayList()).stream().map(e ->
                            UserEvnHolder.getLocal().equals(Locale.US) ? EmploymentTypeEnum.valueOfCode(e).getDescEn() : EmploymentTypeEnum.valueOfCode(e).getDesc())
                    .collect(Collectors.toList()));
            vo.setApplyType(rolePropertiesDTO.getApplyType());
            vo.setApplyTypeDesc(RoleApplyTypeEnum.getCnOrEnDesc(rolePropertiesDTO.getApplyType(), UserEvnHolder.getLocal()));
        }
        vo.setSystemCodeList(JSONArray.parseArray(roleDO.getMultipleSystem(), String.class));
        vo.setRoleCountryList(JSON.parseArray(roleDO.getRoleCountry(), String.class));
        vo.setRoleName(UserEvnHolder.getLocal().equals(Locale.US) ? roleDO.getRoleNameEn() : roleDO.getRoleName());
        vo.setDescription(UserEvnHolder.getLocal().equals(Locale.US) ? roleDO.getDescriptionEn() : roleDO.getDescription());
        vo.setRoleTypeDesc(RoleTypeEnum.getCnOrEnDesc(roleDO.getRoleType(), UserEvnHolder.getLocal()));
        vo.setAuthSceneDesc(RoleAuthSceneEnum.getCnOrEnDesc(roleDO.getAuthScene(), UserEvnHolder.getLocal()));
        vo.setParentId(roleDO.getParentId());
        return vo;
    }

    public static List<UserDefaultRoleVO> convertUserDefaultRoleList(String userCode, List<SysRoleDTO> userDefaultRole, Locale local) {
        List<UserDefaultRoleVO> userDefaultRoleVOList = Lists.newArrayList();
        Optional.ofNullable(userDefaultRole).orElse(Collections.emptyList()).forEach(e -> {
            convertUserDefaultRole(userCode, e, local).ifPresent(userDefaultRoleVOList::add);
        });
        return userDefaultRoleVOList;
    }

    private static Optional<UserDefaultRoleVO> convertUserDefaultRole(String userCode, SysRoleDTO e, Locale local) {
        if (Objects.isNull(e)) {
            return Optional.empty();
        }
        UserDefaultRoleVO roleVO = new UserDefaultRoleVO();
        roleVO.setUserCode(userCode);
        roleVO.setRoleId(e.getId());
        roleVO.setRoleType(e.getRoleType());
        roleVO.setRoleTypeDesc(RoleTypeEnum.getCnOrEnDesc(e.getRoleType(), UserEvnHolder.getLocal()));
        roleVO.setRoleName(local.equals(Locale.US) ? e.getRoleNameEn() : e.getRoleName());
        roleVO.setSystemCodeList(e.getSystemCodeList());
        roleVO.setDescription(local.equals(Locale.US) ? e.getDescriptionEn() : e.getDescription());
        roleVO.setSensitiveLevel(e.getSensitiveLevel());
        return Optional.of(roleVO);
    }

    public static SysRoleDO getChildRole(SysRoleDO sysRoleDO) {
        if (Objects.isNull(sysRoleDO)) {
            return null;
        }
        SysRoleDO childRole = new SysRoleDO();
        childRole.setRoleName(sysRoleDO.getRoleName() + "-非管理员");
        childRole.setIsDisable(sysRoleDO.getIsDisable());
        childRole.setDescription(sysRoleDO.getDescription());
        childRole.setSystemPlatformJson(sysRoleDO.getSystemPlatformJson());
        childRole.setRegisteredSource(sysRoleDO.getRegisteredSource());
        childRole.setAdminRole(sysRoleDO.getAdminRole());
        childRole.setAdminOrg(sysRoleDO.getAdminOrg());
        childRole.setBusinessType(sysRoleDO.getBusinessType());
        childRole.setMultipleSystem(sysRoleDO.getMultipleSystem());
        childRole.setRoleNameEn(sysRoleDO.getRoleNameEn() + "-NonAdministrator");
        childRole.setDescriptionEn(sysRoleDO.getDescriptionEn());
        childRole.setIsAccessApprovalFlow(sysRoleDO.getIsAccessApprovalFlow());
        childRole.setRoleCountry(sysRoleDO.getRoleCountry());
        childRole.setRoleType(sysRoleDO.getRoleType());
        childRole.setRolePropertiesJson(sysRoleDO.getRolePropertiesJson());
        childRole.setParentId(sysRoleDO.getId());
        childRole.setAuthScene(sysRoleDO.getAuthScene());

        return childRole;
    }
}

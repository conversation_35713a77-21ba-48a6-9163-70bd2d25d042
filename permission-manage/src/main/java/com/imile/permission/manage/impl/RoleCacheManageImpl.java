package com.imile.permission.manage.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.permission.casbin.RedisCacheTimeHelper;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.constants.RedisConstant;
import com.imile.permission.convert.RoleCacheConvert;
import com.imile.permission.dao.BusinessBasicDataPermissionConfigDAO;
import com.imile.permission.dao.SysRoleDao;
import com.imile.permission.domain.dataPermission.dto.DataCodeExtensionTagDTO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.permission.dto.PermissionDTO;
import com.imile.permission.domain.role.dto.RoleCacheDTO;
import com.imile.permission.domain.role.dto.RoleInfoDTO;
import com.imile.permission.domain.role.query.SysRoleQuery;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.manage.DataPermissionManage;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.manage.RoleCacheManage;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Service
@Slf4j
public class RoleCacheManageImpl implements RoleCacheManage {

    private static final Logger log = LoggerFactory.getLogger(RoleCacheManageImpl.class);
    @Autowired
    private ImileRedisClient imileRedisClient;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SysRoleDao sysRoleDao;
    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;
    @Autowired
    private RedisCacheTimeHelper redisCacheTimeHelper;
    @Autowired
    private BusinessBasicDataPermissionConfigDAO businessBasicDataPermissionConfigDAO;

    @Autowired
    private DataPermissionManage dataPermissionManage;

    @Override
    public RoleCacheDTO addRoleCache(RoleCacheDTO roleCacheDTO) {
        Long roleId = roleCacheDTO.getRoleId();
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_IS_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleCacheDTO.getRoleCountryList()), PermissionErrorCodeEnums.ROLE_IS_NULL);
        String roleCacheKey = String.format(RedisConstant.ROLE_CACHE_PREFIX, roleId);
        Long defaultTime = redisCacheTimeHelper.getDefaultTime();
        boolean set = imileRedisClient.set(roleCacheKey, JSON.toJSONString(roleCacheDTO), defaultTime);
        BusinessLogicException.checkTrue(!set, PermissionErrorCodeEnums.REDIS_CACHE_ADD_FAILED);
        log.info("RoleCacheManageImpl | addRoleCache key is {} , value is {} expireTime is {}",
                roleCacheKey, JSON.toJSONString(roleCacheDTO), defaultTime);
        return roleCacheDTO;
    }

    @Override
    public void deleteRoleCache(Long roleId) {
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_IS_NULL);

        String roleCacheKey = String.format(RedisConstant.ROLE_CACHE_PREFIX, roleId);
        imileRedisClient.delete(roleCacheKey);

        log.info("RoleCacheManageImpl | deleteRoleCache key is {} ", roleCacheKey);
    }

    private <T> List<T> mGet(Collection<String> keys, Class<T> clazz) {
        List<String> list = redisTemplate.opsForValue().multiGet(keys);
        List<String> collect = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<T> result = collect.stream().map(item -> JSON.parseObject(item, clazz)).collect(Collectors.toList());
        return result;
    }

    @Override
    public RoleCacheDTO getRoleCacheAndDB(Long roleId) {
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_IS_NULL);

        List<RoleCacheDTO> roleCacheByIdList = getRoleCacheAndDBByIdList(Collections.singletonList(roleId));
        if (CollectionUtils.isEmpty(roleCacheByIdList)) {
            return null;
        }
        RoleCacheDTO permissionApiDTO = roleCacheByIdList.get(0);

        log.info("RoleCacheManageImpl | getRoleCache key is {} , value is {}", roleId, JSON.toJSONString(permissionApiDTO));
        return permissionApiDTO;
    }

    @Override
    public List<RoleCacheDTO> getRoleCacheAndDBByIdList(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Lists.newArrayList();
        }
        // 查询该roleId是否被禁用或删除；只保留启用的roleIdList
        List<Long> enableRoleIdList = sysRoleDao.selectNotDisableRoleId(roleIdList);
        log.info("RoleCacheManageImpl | getRoleCacheByIdList roleIdList is {} , enableRoleIdList is {}", roleIdList, enableRoleIdList);

        if (CollectionUtils.isEmpty(enableRoleIdList)) {
            return Lists.newArrayList();
        }
        List<String> roleCacheKeyList = enableRoleIdList.stream().map(
                roleId -> String.format(RedisConstant.ROLE_CACHE_PREFIX, roleId)).collect(Collectors.toList());
        List<RoleCacheDTO> roleCacheDTOList = this.mGet(roleCacheKeyList, RoleCacheDTO.class);
        roleCacheDTOList.forEach( item -> {
            item.getDataPermissionList().forEach( p -> {
                if(CollectionUtils.isEmpty(p.getDataTagList()) && CollectionUtils.isNotEmpty(p.getDataCodeList())){
                    List<DataCodeExtensionTagDTO> dataTagList = Lists.newArrayList();
                    p.getDataCodeList().forEach( dc ->{
                        DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                        tag.setDataCode(dc);
                        tag.setExtensionTagCodeList(Lists.newArrayList());
                        dataTagList.add(tag);
                    } );
                    p.setDataTagList(dataTagList);
                }
            });
        });
        List<Long> roleCacheIdList = roleCacheDTOList.stream().map(RoleCacheDTO::getRoleId).collect(Collectors.toList());
        // 过滤出缓存中不存在的roleId
        List<Long> notExistRoleIdList = enableRoleIdList.stream().filter(
                roleId -> !roleCacheIdList.contains(roleId)).collect(Collectors.toList());

        List<RoleCacheDTO> roleFormDBList = getFromDBAndAddCache(notExistRoleIdList);
        if (CollectionUtils.isNotEmpty(roleFormDBList)) {
            roleCacheDTOList.addAll(roleFormDBList);
        }
        dataPermissionManage.roleListAutoAuthorizeChildren(roleCacheDTOList);
        dataPermissionManage.roleListAutoAuthorizeSelectAll(roleCacheDTOList);
        return roleCacheDTOList;
    }

    @Override
    public List<RoleInfoDTO> getRoleInfoByIdList(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Lists.newArrayList();
        }
        List<SysRoleDO> sysRoleList = sysRoleDao.selectById(roleIdList);
        if (CollectionUtils.isEmpty(sysRoleList)) {
            return Lists.newArrayList();
        }
        Map<Long, SysRoleDO> roleDOMap = sysRoleList.stream().collect(Collectors.toMap(SysRoleDO::getId, Function.identity()));
        List<String> roleCacheKeyList = roleDOMap.keySet().stream().map(
                roleId -> String.format(RedisConstant.ROLE_CACHE_PREFIX, roleId)).collect(Collectors.toList());
        List<RoleCacheDTO> roleCacheDTOList = this.mGet(roleCacheKeyList, RoleCacheDTO.class);
        List<Long> roleCacheIdList = roleCacheDTOList.stream().map(RoleCacheDTO::getRoleId).collect(Collectors.toList());
        // 过滤出缓存中不存在的roleId
        List<Long> notExistRoleIdList = roleIdList.stream().filter(
                roleId -> !roleCacheIdList.contains(roleId)).collect(Collectors.toList());

        List<RoleCacheDTO> roleFormDBList = getFromDBAndAddCache(notExistRoleIdList);
        if (CollectionUtils.isNotEmpty(roleFormDBList)) {
            roleCacheDTOList.addAll(roleFormDBList);
        }
        dataPermissionManage.roleListAutoAuthorizeChildren(roleCacheDTOList);
        dataPermissionManage.roleListAutoAuthorizeSelectAll(roleCacheDTOList);
        return RoleCacheConvert.convertRoleInfo(sysRoleList, roleCacheDTOList);
    }

    @Override
    public void addAllRoleCache(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            // 取批量查询所有角色
            SysRoleQuery roleQuery = new SysRoleQuery();
            roleQuery.setIsDisable(BusinessConstant.N);
            List<SysRoleDO> select = sysRoleDao.select(roleQuery);
            roleIdList = select.stream().map(SysRoleDO::getId).collect(Collectors.toList());
        }

        List<String> roleCacheKeyList = roleIdList.stream().map(
                roleId -> String.format(RedisConstant.ROLE_CACHE_PREFIX, roleId)).collect(Collectors.toList());
        List<RoleCacheDTO> roleCacheDTOList = mGet(roleCacheKeyList, RoleCacheDTO.class);
        List<Long> roleCacheIdList = roleCacheDTOList.stream().map(RoleCacheDTO::getRoleId).collect(Collectors.toList());
        // 过滤出缓存中不存在的roleId
        List<Long> notExistRoleIdList = roleIdList.stream().filter(roleId -> !roleCacheIdList.contains(roleId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notExistRoleIdList)) {
            Lists.partition(notExistRoleIdList, BusinessConstant.ROLE_CACHE_SIZE).stream().forEach(
                    notExistRoleIdList1 -> getFromDBAndAddCache(notExistRoleIdList1)
            );
        }
    }

    @Override
    public void putRoleCache(Long roleId) {
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        SysRoleDO sysRoleDO = sysRoleDao.getById(roleId);
        BusinessLogicException.checkTrue(Objects.isNull(sysRoleDO), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        PermissionDTO permissionByRoleId = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(roleId);
        RoleCacheDTO roleCacheDTO = RoleCacheConvert.permissionRoleCacheConvert(roleId,
                permissionByRoleId.getMenuPermissionDTOList(), permissionByRoleId.getDataPermissionDTOList(), sysRoleDO);
        addRoleCache(roleCacheDTO);
    }

    private List<RoleCacheDTO> getFromDBAndAddCache(List<Long> notExistRoleIdList) {
        if (CollectionUtils.isEmpty(notExistRoleIdList)) {
            return Collections.emptyList();
        }
        log.info("RoleCacheManageImpl | getFromDBAndAddCache notExistRoleIdList is {}", notExistRoleIdList);
        if (notExistRoleIdList.size() > BusinessConstant.ROLE_CACHE_SIZE) {
            log.error("RoleCacheManageImpl | getFromDBAndAddCache notExistRoleIdList is {} 从db读取 ", notExistRoleIdList);
        }
        List<SysRoleDO> sysRoleDOList = sysRoleDao.selectById(notExistRoleIdList);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(sysRoleDOList), PermissionErrorCodeEnums.ROLE_IS_NULL);
        Map<Long, SysRoleDO> roleDOMap = sysRoleDOList.stream().collect(Collectors.toMap(SysRoleDO::getId, sysRoleDO -> sysRoleDO));
        List<RoleCacheDTO> roleCacheDTOList = Lists.newArrayList();
        notExistRoleIdList.stream().forEach(roleId -> {
            PermissionDTO permissionByRoleId = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(roleId);
            RoleCacheDTO roleCacheDTO = RoleCacheConvert.permissionRoleCacheConvert(roleId,
                    permissionByRoleId.getMenuPermissionDTOList(), permissionByRoleId.getDataPermissionDTOList(), roleDOMap.get(roleId));
            addRoleCache(roleCacheDTO);
            roleCacheDTOList.add(roleCacheDTO);
        });
        return roleCacheDTOList;
    }
}

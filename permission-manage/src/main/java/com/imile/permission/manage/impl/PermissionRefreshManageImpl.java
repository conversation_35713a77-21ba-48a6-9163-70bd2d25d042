package com.imile.permission.manage.impl;

import com.imile.permission.manage.EsWithCacheManage;
import com.imile.permission.manage.PermissionRefreshManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/3/20
 */
@Slf4j
@Service
public class PermissionRefreshManageImpl implements PermissionRefreshManage {

    @Autowired
    private EsWithCacheManage esWithCacheManage;

    @Override
    public void pushEsWithCache(String userCode) {
        esWithCacheManage.addUserAuthorizationByDB(userCode);
    }
}

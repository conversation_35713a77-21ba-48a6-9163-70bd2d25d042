package com.imile.permission.manage.impl;

import com.google.common.collect.Lists;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.convert.UserAuthorizationConvert;
import com.imile.permission.domain.dataPermission.dto.DataCodeExtensionTagDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.permission.dto.PermissionDTO;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.integration.hermes.OrgSystemResourceIntegration;
import com.imile.permission.integration.jcasbin.EnforcerIntegration;
import com.imile.permission.integration.resource.ResSystemResourceIntegration;
import com.imile.permission.jcasbin.domain.casbin.Group;
import com.imile.permission.jcasbin.domain.casbin.Policy;
import com.imile.permission.manage.EnforcerManage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/10
 */
@Service
public class EnforcerManageImpl implements EnforcerManage {

    @Autowired
    public EnforcerIntegration enforcer;

    @Autowired
    public OrgSystemResourceIntegration orgSystemResourceIntegration;

    @Autowired
    ResSystemResourceIntegration resSystemResourceIntegration;

    @Override
    public void reLoad() {
        enforcer.reLoad();
    }

    @Override
    public List<List<String>> getPolicy() {
        return enforcer.getPolicy();
    }

    @Override
    public void clearPolicy() {
        enforcer.clearPolicy();
    }

    @Override
    public void loadPolicy() {
        enforcer.loadPolicy();
    }

    @Override
    public void savePolicy() {
        enforcer.savePolicy();
    }

    @Override
    public void enableAutoSave(Boolean autoSave) {
        enforcer.enableAutoSave(autoSave);
    }

    @Override
    public void deleteRole(String role) {
        enforcer.deleteRole(role);
    }

    @Override
    public boolean removeFilteredPolicy(int fieldIndex, String... fieldValues) {
        return enforcer.removeFilteredPolicy(fieldIndex, fieldValues);
    }

    @Override
    public boolean addPolicies(List<List<String>> rules) {
        return enforcer.addPolicies(rules);
    }

    @Override
    public List<List<String>> getFilteredGroupingPolicy(int fieldIndex, String... fieldValues) {
        return enforcer.getFilteredGroupingPolicy(fieldIndex, fieldValues);
    }

    @Override
    public List<List<String>> getFilteredPolicy(int fieldIndex, String... fieldValues) {
        return enforcer.getFilteredPolicy(fieldIndex, fieldValues);
    }

    @Override
    public List<List<String>> getPermissionsForUser(String user, String... domain) {
        return enforcer.getPermissionsForUser(user, domain);
    }

    @Override
    public boolean removeFilteredGroupingPolicy(int fieldIndex, String... fieldValues) {
        return enforcer.removeFilteredGroupingPolicy(fieldIndex, fieldValues);
    }

    @Override
    public boolean patchRemoveFilteredGroupingPolicy(int fieldIndex, List<String[]> fieldValues) {
        return enforcer.patchRemoveFilteredGroupingPolicy(fieldIndex, fieldValues);
    }

    @Override
    public boolean addGroupingPolicies(List<List<String>> rules) {
        return enforcer.addGroupingPolicies(rules);
    }

    @Override
    public boolean patchAddGroupingPolicy(List<List<String>> rules) {
        return enforcer.patchAddGroupingPolicy(rules);
    }

    @Override
    public List<List<String>> getImplicitPermissionsForUser(String user, String... domain) {
        return enforcer.getImplicitPermissionsForUser(user, domain);
    }

    @Override
    public List<String> getImplicitRolesForUser(String name, String... domain) {
        return enforcer.getImplicitRolesForUser(name, domain);
    }

    @Override
    public List<String> getRolesForUser(String name) {
        return enforcer.getRolesForUser(name);
    }


    @Override
    public void deleteByRole(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        enforcer.deleteRole(BusinessConstant.CASBIN_R_COLON + id);
    }

    @Override
    public void deleteRolePermission(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        enforcer.removeFilteredPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_R_COLON + id);
    }

    @Override
    public void deleteRoleMenuPermission(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        enforcer.removeFilteredPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_R_COLON + id);
    }

    @Override
    public void saveRolePermission(Long id, List<MenuPermissionDTO> menuPermissionList, List<DataPermissionDTO> dataPermissionDTOList) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        processMenuPermissionDTO(menuPermissionList);
        List<List<String>> ruleList = new ArrayList<>();
        processRoleMenuPermission(id, menuPermissionList, ruleList);
        processRoleDataPermission(id, dataPermissionDTOList, ruleList);
        if (CollectionUtils.isNotEmpty(ruleList)) {
            enforcer.addPolicies(ruleList);
        }
    }

    @Override
    public void deleteDataPermission(String typeCode, String dataCode, Collection<String> dataCodeList) {
        List<String[]> list = new ArrayList<>();
        if (StringUtils.isNotBlank(dataCode)) {
            list.add(new String[]{BusinessConstant.CASBIN_TC_COLON + typeCode, dataCode});
        }
        if (CollectionUtils.isNotEmpty(dataCodeList)) {
            for (String dc : dataCodeList) {
                list.add(new String[]{BusinessConstant.CASBIN_TC_COLON + typeCode, dc});
            }
        }
        if (StringUtils.isBlank(dataCode) && CollectionUtils.isEmpty(dataCodeList)) {
            list.add(new String[]{BusinessConstant.CASBIN_TC_COLON + typeCode});
        }
        if (CollectionUtils.isNotEmpty(list)) {
            enforcer.patchRemoveFilteredPolicy(BusinessConstant.ONE, list);
        }
    }

    @Override
    public List<Long> getWorkCenterByRoleId(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        List<Long> result = new ArrayList<>();
        roleIdList.forEach(
                roleId -> {
                    List<List<String>> groupList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_R_COLON + roleId);
                    if (CollectionUtils.isNotEmpty(groupList)) {
                        groupList.forEach(
                                group -> {
                                    result.add(Long.valueOf(group.get(BusinessConstant.ONE).split(BusinessConstant.COLON)[BusinessConstant.ONE]));
                                }
                        );
                    }

                }
        );
        return result;
    }

    @Override
    public void saveRoleMenuPermission(Long id, List<MenuPermissionDTO> menuPermissionList) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(menuPermissionList), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        processMenuPermissionDTO(menuPermissionList);
        List<List<String>> ruleList = new ArrayList<>();
        processRoleMenuPermission(id, menuPermissionList, ruleList);
        if (CollectionUtils.isNotEmpty(ruleList)) {
            enforcer.addPolicies(ruleList);
        }
    }

    @Override
    public List<List<String>> buildRoleMenuRules(Long id, List<MenuPermissionDTO> menuPermissionList) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(menuPermissionList), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        // 将全选中的半选处理为半选
        processMenuPermissionDTO(menuPermissionList);
        List<List<String>> ruleList = new ArrayList<>();
        processRoleMenuPermission(id, menuPermissionList, ruleList);
        if (CollectionUtils.isEmpty(ruleList)) {
            return Lists.newArrayList();
        }
        return ruleList;
    }


    @Override
    public void saveRoleDataPermission(Long id, List<DataPermissionDTO> dataPermissionDTOList) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(dataPermissionDTOList), PermissionErrorCodeEnums.DATA_CODE_NOT_NULL);
        List<List<String>> ruleList = new ArrayList<>();
        processRoleDataPermission(id, dataPermissionDTOList, ruleList);
        if (CollectionUtils.isNotEmpty(ruleList)) {
            enforcer.addPolicies(ruleList);
        }
    }


    @Override
    public void saveSourceNodeMenuPermission(String id, List<MenuPermissionDTO> menuPermissionList) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.WORK_CENTER_NODE_ID_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(menuPermissionList), PermissionErrorCodeEnums.MENU_PERMISSION_NOT_NULL);
        processMenuPermissionDTO(menuPermissionList);
        List<List<String>> ruleList = new ArrayList<>();
        processSourceNodeMenuPermission(id, menuPermissionList, ruleList);
        if (CollectionUtils.isNotEmpty(ruleList)) {
            enforcer.addPolicies(ruleList);
        }
    }


    @Override
    public void saveSourceNodeDataPermission(String id, List<DataPermissionDTO> dataPermissionDTOList) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.WORK_CENTER_NODE_ID_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(dataPermissionDTOList), PermissionErrorCodeEnums.DATA_CODE_NOT_NULL);
        List<List<String>> ruleList = new ArrayList<>();
        processSourceNodeDataPermission(id, dataPermissionDTOList, ruleList);
        if (CollectionUtils.isNotEmpty(ruleList)) {
            enforcer.addPolicies(ruleList);
        }
    }


    @Override
    public PermissionDTO getPermissionByRoleId(Long id) {
        List<List<String>> rolePermission = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_R_COLON + id
        );

        if (CollectionUtils.isEmpty(rolePermission)) {
            return emptyPermissionDTO();
        }

        PermissionDTO permissionDTO = getPermissionDTO(rolePermission);

        return permissionDTO;
    }


    @Override
    public PermissionDTO getPermissionBySourceNodeId(String sourceNodeId) {
        List<List<String>> sourceNodePermissionList = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_WCSN_COLON + sourceNodeId
        );

        if (CollectionUtils.isEmpty(sourceNodePermissionList)) {
            return emptyPermissionDTO();
        }

        PermissionDTO permissionDTO = getPermissionDTO(sourceNodePermissionList);
        return permissionDTO;
    }


    @Override
    public void bindingRoleToPost(List<Long> finalRoleIdList, Long postId) {
        // 判断解绑岗位
        if (CollectionUtils.isEmpty(finalRoleIdList)) {
            List<List<String>> postRoleList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_P_COLON + postId);
            postRoleList = postRoleList.stream().filter(e -> e.get(BusinessConstant.ONE).contains(BusinessConstant.CASBIN_R_COLON)).collect(Collectors.toList());
            removeGroupingPolicy(postRoleList);
            return;
        }
        // 查询以前绑定到角色
        List<List<String>> postRoleList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_P_COLON + postId);
        //比较新角色,删除旧到关联角色
        List<Long> newRoleIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(postRoleList)) {
            newRoleIdList.addAll(finalRoleIdList);
        } else {
            List<Long> oldRoleIdList = postRoleList.stream()
                    .map(
                            e -> e.get(BusinessConstant.ONE).split(BusinessConstant.COLON)[BusinessConstant.ONE]
                    )
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            // 新增角色
            newRoleIdList.addAll(CollectionUtils.removeAll(finalRoleIdList, oldRoleIdList));
            // 删除角色
            Collection<Long> removeRoleList = CollectionUtils.removeAll(oldRoleIdList, finalRoleIdList);
            if (CollectionUtils.isNotEmpty(removeRoleList)) {
                for (Long removeId : removeRoleList) {
                    enforcer.removeFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_P_COLON + postId, BusinessConstant.CASBIN_R_COLON + removeId);
                }
            }
        }
        //绑定新到关联角色
        if (CollectionUtils.isNotEmpty(newRoleIdList)) {
            addGroup(newRoleIdList, BusinessConstant.CASBIN_P_COLON + postId, BusinessConstant.CASBIN_R_COLON);

        }
    }

    @Override
    public List<Long> getPostBindingRole(Long postId) {
        BusinessLogicException.checkTrue(Objects.isNull(postId), PermissionErrorCodeEnums.POST_ID_IS_NULL);
        List<List<String>> postRoleList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_P_COLON + postId);
        if (CollectionUtils.isEmpty(postRoleList)) {
            return Collections.emptyList();
        }
        List<String> bindingList = new ArrayList<>();
        processV1TypeRecord(postRoleList, bindingList, BusinessConstant.CASBIN_R);
        return bindingList.stream()
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getPostBindingRole(List<Long> postIdList) {
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(postIdList), PermissionErrorCodeEnums.POST_ID_IS_NULL);

        List<String[]> postIdListArr = postIdList.stream()
                .map(postId -> new String[]{BusinessConstant.CASBIN_P_COLON + postId})
                .collect(Collectors.toList());
        Map<String, List<List<String>>> patchFilteredGroupingPolicy = enforcer.getPatchFilteredGroupingPolicy(BusinessConstant.ZERO, postIdListArr);
        if (MapUtils.isEmpty(patchFilteredGroupingPolicy)) {
            return Collections.emptyList();
        }
        List<String> roleList = new ArrayList<>();
        patchFilteredGroupingPolicy.values().forEach(
                postRoleList -> processV1TypeRecord(postRoleList, roleList, BusinessConstant.CASBIN_R)
        );
        return roleList.stream()
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<Long>> getPostBindingRoleList(List<Long> postIdList) {
        if (CollectionUtils.isEmpty(postIdList)) {
            return Collections.emptyMap();
        }
        List<String[]> postIdArr = postIdList.stream()
                .map(
                        postId -> new String[]{BusinessConstant.CASBIN_P_COLON + postId}
                )
                .collect(Collectors.toList());
        Map<String, List<List<String>>> postRoleListMap = enforcer.getPatchFilteredGroupingPolicy(BusinessConstant.ZERO, postIdArr);
        Map<Long, List<Long>> result = new HashMap<>();
        for (Long postId : postIdList) {
            List<List<String>> postRoleList = postRoleListMap.get(BusinessConstant.CASBIN_P_COLON + postId);
            List<String> bindingList = new ArrayList<>();
            processV1TypeRecord(postRoleList, bindingList, BusinessConstant.CASBIN_R);
            result.put(postId, bindingList.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        return result;
    }


    @Override
    public List<Long> getMenuByRoleId(Long roleId) {
        List<List<String>> roleAllPermission = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_R_COLON + roleId
        );

        return getFullAndPartiallyMenuId(roleAllPermission);
    }

    @Override
    public List<Long> getMenuIdByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return Collections.emptyList();
        }
        List<List<String>> userPermission = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_U_COLON + userCode
        );

        return getFullAndPartiallyMenuId(userPermission);
    }

    @Override
    public List<Long> getAllMenuIdByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return Collections.emptyList();
        }
        List<Long> userMenuIdList = getMenuIdByUserCode(userCode);
        List<Long> roleIdList = getBindingRoleByUserCode(userCode);
        List<Long> roleMenuIdList = getMenuIdByRoleIdList(roleIdList);
        userMenuIdList.addAll(roleMenuIdList);
        return userMenuIdList;
    }

    @Override
    public List<Long> getImplicitMenuIdByUserCode(String userCode) {
        List<List<String>> userPermission = enforcer.getImplicitPermissionsForUser(
                BusinessConstant.CASBIN_U_COLON + userCode
        );
        return getFullAndPartiallyMenuId(userPermission);
    }

    @Override
    public Map<Long, List<Long>> getImplicitFullPartiallyMenuByUserCode(String userCode) {
        List<List<String>> userPermission = enforcer.getImplicitPermissionsForUser(
                BusinessConstant.CASBIN_U_COLON + userCode
        );
        return getMenu(userPermission);
    }

    @Override
    public PermissionDTO getImplicitPermissionByRoleId(Long roleId) {
        List<List<String>> userPermission = enforcer.getImplicitPermissionsForUser(
                BusinessConstant.CASBIN_R_COLON + roleId
        );
        return getPermissionDTO(userPermission);
    }

    @Override
    public Map<Long, List<Long>> getAllFullPartiallyMenuByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return Collections.emptyMap();
        }

        List<List<String>> result = new ArrayList<>();

        // 用户权限
        List<List<String>> userMenuList = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_U_COLON + userCode
        );

        if (CollectionUtils.isNotEmpty(userMenuList)) {
            result.addAll(userMenuList);
        }

        // 角色权限
        List<Long> roleIdList = getBindingRoleByUserCode(userCode);
        List<List<String>> roleMenuList = roleIdList.stream()
                .map(roleId -> enforcer.getPermissionsForUser(BusinessConstant.CASBIN_R_COLON + roleId))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(roleMenuList)) {
            result.addAll(roleMenuList);
        }

        Map<Long, List<Long>> menu = getMenu(result);
        return menu;
    }

    @Override
    public List<Long> getMenuIdByRoleIdList(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        return roleIdList.stream()
                .map(roleId -> getMenuByRoleId(roleId))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getRoleAssociationPost(Long roleId) {
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        // 查询角色关联的岗位
        List<List<String>> postRoleList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ONE, BusinessConstant.CASBIN_R_COLON + roleId);
        List<String> roleAssociationList = new ArrayList<>();
        processV0TypeRecord(postRoleList, roleAssociationList, BusinessConstant.CASBIN_P);
        return roleAssociationList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<Long>> getRoleAssociationPostMap(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyMap();
        }

        List<String[]> roleArr = roleIdList.stream()
                .map(roleId -> new String[]{BusinessConstant.CASBIN_R_COLON + roleId})
                .collect(Collectors.toList());
        Map<String, List<List<String>>> patchFilteredGroupingPolicy = enforcer.getPatchFilteredGroupingPolicy(BusinessConstant.ONE, roleArr);

        Map<Long, List<Long>> result = new HashMap<>();
        for (Long roleId : roleIdList) {
            List<List<String>> postRoleList = patchFilteredGroupingPolicy.get(BusinessConstant.CASBIN_R_COLON + roleId);
            List<String> bindingList = new ArrayList<>();
            processV0TypeRecord(postRoleList, bindingList, BusinessConstant.CASBIN_P);
            result.put(roleId, bindingList.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(Long::valueOf)
                    .distinct()
                    .collect(Collectors.toList()));
        }

        return result;
    }

    @Override
    public List<Long> getWorkCenterAssociationRole(Long workCenterId) {
        List<String> workCenterAssociationList = processWorkCenterAssociation(workCenterId);
        return workCenterAssociationList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }


    @Override
    public Map<String, List<String>> getAllTypeDataPermissionByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return Collections.emptyMap();
        }
        Map<String, List<String>> map = new HashMap<>();
        List<Long> roleIdList = getBindingRoleByUserCode(userCode);

        List<String> keyList = new ArrayList<>();

        keyList.add(BusinessConstant.CASBIN_U_COLON + userCode);
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            keyList.addAll(
                    roleIdList.stream()
                            .map(roleId -> BusinessConstant.CASBIN_R_COLON + roleId)
                            .collect(Collectors.toList())
            );
        }

        List<List<String>> permissionList = enforcer.getPermissionsForKey(keyList);

        processTypeData(getTypeData(permissionList), map);

        return map;
    }


    @Override
    public List<String> getImplicitGroupByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return Collections.emptyList();
        }
        return enforcer.getImplicitRolesForUser(BusinessConstant.CASBIN_U_COLON + userCode);
    }

    @Override
    public PermissionDTO getPermissionDTO(String userCode, List<Long> roleIdList, List<String> workCenterNodeIdList) {
        return enforcer.getPermissionDTO(userCode, roleIdList, workCenterNodeIdList);
    }


    @Override
    public void deleteByWorkCenterId(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.WORK_CENTER_ID_NOT_NULL);
        List<String> list = new ArrayList<>();
        List<String> wcList = enforcer.getRolesForUser(BusinessConstant.CASBIN_WC_COLON + id);
        if (CollectionUtils.isNotEmpty(wcList)) {
            list.addAll(wcList);
        }
        list.add(BusinessConstant.CASBIN_WC_COLON + id);
        enforcer.patchDeleteRole(list);
    }

    @Override
    public Map<Long, List<Long>> getFullPartiallyMenuByRoleId(Long roleId) {
        if (Objects.isNull(roleId)) {
            return Collections.emptyMap();
        }
        List<List<String>> rolePermission = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_R_COLON + roleId
        );
        return getMenu(rolePermission);
    }


    @Override
    public List<Long> getMenuIdList(String userCode, List<Long> roleIdList, List<String> workCenterNodeIdList) {
        List<List<String>> userPermission = getPolicy(userCode, roleIdList, workCenterNodeIdList);
        return getFullAndPartiallyMenuId(userPermission);
    }

    @Override
    public Map<Long, List<Long>> getMenu(String userCode, List<Long> roleIdList, List<String> workCenterNodeIdList) {
        List<List<String>> userPermission = getPolicy(userCode, roleIdList, workCenterNodeIdList);
        return getMenu(userPermission);
    }

    @Override
    public Boolean addDataPermission(String typeCode, String parentId, String currentId) {
        List<List<String>> parentRule = enforcer.getFilteredPolicy(BusinessConstant.ONE, BusinessConstant.CASBIN_TC_COLON + typeCode, BusinessConstant.CASBIN_DC_COLON + parentId);
        if (CollectionUtils.isNotEmpty(parentRule)) {
            BusinessLogicException.checkTrue(parentRule.size() > 100, PermissionErrorCodeEnums.DATA_CHANGES_ARE_TOO_LARGE);
            List<List<String>> updateList = parentRule.stream()
                    .map(
                            e -> Arrays.asList(
                                    e.get(BusinessConstant.ZERO),
                                    e.get(BusinessConstant.ONE),
                                    BusinessConstant.CASBIN_DC_COLON + currentId,
                                    BusinessConstant.POUND,
                                    BusinessConstant.POUND,
                                    BusinessConstant.POUND
                            )
                    )
                    .collect(Collectors.toList());
            enforcer.patchAddPolicy(updateList);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean changeDataPermission(String typeCode, String parentId, String currentId, List<String> subIdList) {
        List<String> idList = Lists.newArrayList(currentId);
        idList.addAll(subIdList);
        // 删除拥有原权限的数据
        if (CollectionUtils.isNotEmpty(idList)) {
            List<String[]> deleteArr = idList.stream()
                    .map(e -> new String[]{BusinessConstant.CASBIN_TC_COLON + typeCode, BusinessConstant.CASBIN_DC_COLON + e})
                    .collect(Collectors.toList());
            enforcer.patchRemoveFilteredPolicy(BusinessConstant.ONE, deleteArr);
        }
        // 拥有父权限的权限列表
        List<List<String>> parentRule = enforcer.getFilteredPolicy(BusinessConstant.ONE, BusinessConstant.CASBIN_TC_COLON + typeCode, BusinessConstant.CASBIN_DC_COLON + parentId);
        if (CollectionUtils.isNotEmpty(parentRule)) {
            BusinessLogicException.checkTrue(parentRule.size() * idList.size() > 100, PermissionErrorCodeEnums.DATA_CHANGES_ARE_TOO_LARGE);
            BusinessLogicException.checkTrue(parentRule.size() > 5, PermissionErrorCodeEnums.DATA_CHANGES_ARE_TOO_LARGE);
            // 添加新的子权限
            parentRule.forEach(
                    e -> {
                        List<List<String>> updateList = idList.stream()
                                .map(
                                        id -> Arrays.asList(
                                                e.get(BusinessConstant.ZERO),
                                                e.get(BusinessConstant.ONE),
                                                BusinessConstant.CASBIN_DC_COLON + id,
                                                BusinessConstant.POUND,
                                                BusinessConstant.POUND,
                                                BusinessConstant.POUND
                                        )
                                ).collect(Collectors.toList());
                        enforcer.patchAddPolicy(updateList);
                    }
            );
        }
        return Boolean.TRUE;
    }

    @Override
    public List<Long> getRoleIdByMenuId(Long menuId) {
        BusinessLogicException.checkTrue(Objects.isNull(menuId), PermissionErrorCodeEnums.MENU_PERMISSION_NOT_NULL);
        List<List<String>> policyList = enforcer.getFilteredPolicy(BusinessConstant.THREE, BusinessConstant.CASBIN_M_COLON + menuId);
        List<String> roleIdList = new ArrayList<>();
        processV0TypeRecord(policyList, roleIdList, BusinessConstant.CASBIN_R);
        return roleIdList.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<String>> getUserCodeByRoleId(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyMap();
        }
        Map<String, List<String>> map = new HashMap<>();

        List<String[]> roleIdListArr = roleIdList.stream()
                .map(roleId -> new String[]{BusinessConstant.CASBIN_R_COLON + roleId})
                .collect(Collectors.toList());

        Map<String, List<List<String>>> patchFilteredGroupingPolicy = enforcer.getPatchFilteredGroupingPolicy(BusinessConstant.ONE, roleIdListArr);
        if (MapUtils.isEmpty(patchFilteredGroupingPolicy)) {
            return Collections.emptyMap();
        }

        for (Map.Entry<String, List<List<String>>> entry : patchFilteredGroupingPolicy.entrySet()) {
            String key = entry.getKey();
            List<List<String>> value = entry.getValue();
            value.stream()
                    .map(
                            e -> e.get(BusinessConstant.ZERO)
                    )
                    .filter(
                            e -> e.startsWith(BusinessConstant.CASBIN_U_COLON)
                    )
                    .map(
                            e -> e.split(BusinessConstant.COLON)[BusinessConstant.ONE]
                    )
                    .distinct()
                    .forEach(
                            e -> {
                                List<String> role = map.getOrDefault(e, new ArrayList<>());
                                role.add(key.split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                                map.put(e, role);
                            }
                    );
        }
        return map;
    }

    @Override
    public Boolean addRoleForUser(String userCode, List<Long> roleIdList) {
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            roleIdList.forEach(
                    e -> {
                        enforcer.addGroupingPolicy(
                                Group.builder()
                                        .v0(BusinessConstant.CASBIN_U_COLON + userCode)
                                        .v1(BusinessConstant.CASBIN_R_COLON + e)
                                        .build()
                                        .getArr()
                        );
                    }
            );
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean checkUserRole(String userCode, Long roleId) {
        List<List<String>> groupingPolicy = enforcer.getFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_U_COLON + userCode, BusinessConstant.CASBIN_R_COLON + roleId);
        return CollectionUtils.isNotEmpty(groupingPolicy) ? true : false;
    }


    @Override
    public List<String> getRoleDataCodeByTypeCode(Long roleId, String typeCode) {
        List<List<String>> roleDataPermissionList = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_R_COLON + roleId, BusinessConstant.CASBIN_TC_COLON + typeCode
        );

        return roleDataPermissionList.stream()
                .map(
                        dataPermission -> dataPermission.get(BusinessConstant.ONE).split(BusinessConstant.COLON)[BusinessConstant.ONE]
                )
                .collect(Collectors.toList());
    }


    @Override
    public void saveUserPermission(String userCode, List<MenuPermissionDTO> menuPermissionList, List<DataPermissionDTO> dataPermissionDTOList) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        processMenuPermissionDTO(menuPermissionList);
        List<List<String>> ruleList = new ArrayList<>();
        processUserMenuPermission(userCode, menuPermissionList, ruleList);
        processUserNodeDataPermission(userCode, dataPermissionDTOList, ruleList);
        if (CollectionUtils.isNotEmpty(ruleList)) {
            enforcer.addPolicies(ruleList);
        }
    }


    @Override
    public void deleteUserPermission(String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        enforcer.removeFilteredPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_U_COLON + userCode);
    }

    @Override
    public List<Long> getBindingRoleByUserCode(String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        List<List<String>> groupList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_U_COLON + userCode);
        List<String> result = new ArrayList<>();
        processV1TypeRecord(groupList, result, BusinessConstant.CASBIN_R);
        return result.stream().map(Long::valueOf).collect(Collectors.toList());
    }


    @Override
    public List<Long> getBindingWorkCenterByUserCode(String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        List<List<String>> groupList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ZERO, BusinessConstant.CASBIN_U_COLON + userCode);
        List<String> result = new ArrayList<>();
        processV1TypeRecord(groupList, result, BusinessConstant.CASBIN_WC);
        return result.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<Long>> getBindingRoleByUserCodeList(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return Collections.emptyMap();
        }
        Map<String, List<Long>> resultMap = new HashMap<>();
        List<String[]> userCodeArr = userCodeList.stream()
                .map(userCode -> new String[]{BusinessConstant.CASBIN_U_COLON + userCode})
                .collect(Collectors.toList());
        Map<String, List<List<String>>> patchFilteredGroupingPolicy = enforcer.getPatchFilteredGroupingPolicy(BusinessConstant.ZERO, userCodeArr);
        if (MapUtils.isNotEmpty(patchFilteredGroupingPolicy)) {
            patchFilteredGroupingPolicy.forEach(
                    (k, v) -> {
                        List<String> result = new ArrayList<>();
                        processV1TypeRecord(v, result, BusinessConstant.CASBIN_R);
                        String replaceKey = k.replace(BusinessConstant.CASBIN_U_COLON, "");
                        resultMap.put(replaceKey, result.stream().map(Long::valueOf).collect(Collectors.toList()));
                    }
            );
        }
        return resultMap;
    }

    @Override
    public Map<String, List<Long>> getBindingWorkCenterByUserCodeList(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return Collections.emptyMap();
        }
        Map<String, List<Long>> resultMap = new HashMap<>();
        List<String[]> userCodeArr = userCodeList.stream()
                .map(userCode -> new String[]{BusinessConstant.CASBIN_U_COLON + userCode})
                .collect(Collectors.toList());
        Map<String, List<List<String>>> patchFilteredGroupingPolicy = enforcer.getPatchFilteredGroupingPolicy(BusinessConstant.ZERO, userCodeArr);
        if (MapUtils.isNotEmpty(patchFilteredGroupingPolicy)) {
            patchFilteredGroupingPolicy.forEach(
                    (k, v) -> {
                        List<String> result = new ArrayList<>();
                        processV1TypeRecord(v, result, BusinessConstant.CASBIN_WC);
                        String replaceKey = k.replace(BusinessConstant.CASBIN_U_COLON, "");
                        resultMap.put(replaceKey, result.stream().map(Long::valueOf).collect(Collectors.toList()));
                    }
            );
        }
        return resultMap;
    }

    @Override
    public void processRoleAndWorkCenter(List<String> userCodeList, Map<String, List<Long>> userCodeToRoleMap, Map<String, List<Long>> userCodeToWorkCenterMap) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return;
        }
        List<String[]> userCodeArr = userCodeList.stream()
                .map(userCode -> new String[]{BusinessConstant.CASBIN_U_COLON + userCode})
                .collect(Collectors.toList());
        Map<String, List<List<String>>> patchFilteredGroupingPolicy = enforcer.getPatchFilteredGroupingPolicy(BusinessConstant.ZERO, userCodeArr);

        if (MapUtils.isNotEmpty(patchFilteredGroupingPolicy)) {
            patchFilteredGroupingPolicy.forEach(
                    (k, v) -> {
                        List<String> rResult = new ArrayList<>();
                        String replaceKey = k.replace(BusinessConstant.CASBIN_U_COLON, "");
                        processV1TypeRecord(v, rResult, BusinessConstant.CASBIN_R);
                        userCodeToRoleMap.put(replaceKey, rResult.stream().map(Long::valueOf).collect(Collectors.toList()));
                        List<String> wcResult = new ArrayList<>();
                        processV1TypeRecord(v, wcResult, BusinessConstant.CASBIN_WC);
                        userCodeToWorkCenterMap.put(replaceKey, wcResult.stream().map(Long::valueOf).collect(Collectors.toList()));
                    }
            );
        }
    }

    @Override
    public PermissionDTO getPermissionByUserCode(String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        List<List<String>> userPermissionList = enforcer.getPermissionsForUser(BusinessConstant.CASBIN_U_COLON + userCode);
        if (CollectionUtils.isEmpty(userPermissionList)) {
            return emptyPermissionDTO();
        }
        PermissionDTO permissionDTO = getPermissionDTO(userPermissionList);
        return permissionDTO;
    }

    @Override
    @Deprecated
    public PermissionDTO getImplicitPermissionByUserCode(String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        List<List<String>> userPermissionList = enforcer.getImplicitPermissionsForUser(BusinessConstant.CASBIN_U_COLON + userCode);
        if (CollectionUtils.isEmpty(userPermissionList)) {
            return emptyPermissionDTO();
        }
        PermissionDTO permissionDTO = getPermissionDTO(userPermissionList);
        return permissionDTO;
    }

    @Override
    public Map<Long, List<Long>> getMenu(List<List<String>> userPermission) {
        return Optional.ofNullable(userPermission)
                .orElse((List<List<String>>) Collections.EMPTY_LIST)
                .stream()
                .filter(e -> e.size() > BusinessConstant.THREE)
                .filter(e -> {
                    String v2 = e.get(BusinessConstant.THREE);
                    return StringUtils.isNotBlank(v2) && v2.contains(BusinessConstant.CASBIN_M_COLON);
                })
                .collect(Collectors.groupingBy(e -> {
                    String v1 = e.get(BusinessConstant.TWO);
                    return Long.valueOf(v1.split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                }, Collectors.mapping(
                        e -> {
                            String v2 = e.get(BusinessConstant.THREE);
                            return Long.valueOf(v2.split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                        },
                        Collectors.toList()
                )));
    }


    public void processMenuPermissionDTO(List<MenuPermissionDTO> menuPermissionList) {
        if (CollectionUtils.isEmpty(menuPermissionList)) {
            return;
        }
        List<Long> allMenuIdList = menuPermissionList.stream().filter(e -> CollectionUtils.isNotEmpty(e.getMenuIdList())).map(MenuPermissionDTO::getMenuIdList).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> resourceIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allMenuIdList)) {
            resourceIdList.addAll(resSystemResourceIntegration.convertPartially(allMenuIdList));
        }
        menuPermissionList.forEach(
                menuPermissionDTO -> {
                    List<Long> menuIdList = Optional.ofNullable(menuPermissionDTO.getMenuIdList())
                            .orElse(Collections.EMPTY_LIST);
                    List<Long> partiallyMenuIdList = Optional.ofNullable(menuPermissionDTO.getPartiallyMenuIdList())
                            .orElse(new ArrayList<>());

                    if (CollectionUtils.isEmpty(menuIdList) && CollectionUtils.isEmpty(partiallyMenuIdList)) {
                        return;
                    }
                    // full 中的半选 ID
                    List<Long> partiallyId = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(menuIdList)) {
                        partiallyId.addAll(
                                CollectionUtils.intersection(resourceIdList, menuIdList)
                        );
                    }

                    // 设置全选 ID
                    Collection<Long> fullId = CollectionUtils.removeAll(menuIdList, partiallyId);
                    menuPermissionDTO.setMenuIdList(new ArrayList<>(fullId));

                    // 设置半选 ID
                    partiallyMenuIdList.addAll(partiallyId);
                    menuPermissionDTO.setPartiallyMenuIdList(partiallyMenuIdList);


                }

        );
    }

    public void processRoleDataPermission(Long id, List<DataPermissionDTO> dataPermissionDTOList, List<List<String>> ruleList) {
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            buildDataRule(dataPermissionDTOList, ruleList, BusinessConstant.CASBIN_R_COLON + id);
        }
    }

    public void buildDataRule(List<DataPermissionDTO> dataPermissionDTOList, List<List<String>> ruleList, String v0) {
        dataPermissionDTOList.forEach(
                dataPermissionDTO -> {
                    String typeCode = dataPermissionDTO.getTypeCode();
                    BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
                    List<String> dataCodeList = dataPermissionDTO.getDataCodeList();
                    if (CollectionUtils.isNotEmpty(dataCodeList)) {
                        dataCodeList.stream()
                                .filter(StringUtils::isNotBlank)
                                .forEach(
                                        dataCode -> {
                                            ruleList.add(
                                                    Policy.builder()
                                                            // WCSN:id,TC:typeCode,DC:dataCode,POUND,POUND,POUND
                                                            // R:id,TC:typeCode,DC:dataCode,POUND,POUND,POUND
                                                            // U:id,TC:typeCode,DC:dataCode,POUND,POUND,POUND
                                                            .v0(v0)
                                                            .v1(BusinessConstant.CASBIN_TC_COLON + dataPermissionDTO.getTypeCode())
                                                            .v2(BusinessConstant.CASBIN_DC_COLON + dataCode)
                                                            .v3(BusinessConstant.POUND)
                                                            .v4(BusinessConstant.POUND)
                                                            .v5(BusinessConstant.POUND)
                                                            .build()
                                                            .getArr()
                                            );
                                        }
                                );
                    }
                }
        );
    }

    public void processRoleMenuPermission(Long id, List<MenuPermissionDTO> menuPermissionList, List<List<String>> ruleList) {
        if (CollectionUtils.isNotEmpty(menuPermissionList)) {
            buildMenuRule(menuPermissionList, ruleList, BusinessConstant.CASBIN_R_COLON + id);
        }
    }

    public void processSourceNodeMenuPermission(String id, List<MenuPermissionDTO> menuPermissionList, List<List<String>> ruleList) {
        if (CollectionUtils.isNotEmpty(menuPermissionList)) {
            buildMenuRule(menuPermissionList, ruleList, BusinessConstant.CASBIN_WCSN_COLON + id);
        }
    }

    public void processSourceNodeDataPermission(String id, List<DataPermissionDTO> dataPermissionDTOList, List<List<String>> ruleList) {
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            buildDataRule(dataPermissionDTOList, ruleList, BusinessConstant.CASBIN_WCSN_COLON + id);
        }
    }

    public void buildMenuRule(List<MenuPermissionDTO> menuPermissionList, List<List<String>> ruleList, String v0) {
        menuPermissionList.forEach(
                menuPermissionDTO -> {
                    List<Long> menuIdList = menuPermissionDTO.getMenuIdList();
                    List<Long> partiallyMenuIdList = Optional.ofNullable(menuPermissionDTO.getPartiallyMenuIdList())
                            .orElse(new ArrayList<>());
                    String systemPlatform = Optional.ofNullable(menuPermissionDTO.getSystemPlatform())
                            .orElse(BusinessConstant.DEFAULT_SP);
                    Long rootMenu = Optional.ofNullable(menuPermissionDTO.getMenuId())
                            .orElse(BusinessConstant.DEFAULT_RM);
                    if (CollectionUtils.isNotEmpty(menuIdList)) {
                        Iterator<Long> iterator = menuIdList.iterator();
                        while (iterator.hasNext()) {
                            Long next = iterator.next();
                            if (next < 0) {
                                partiallyMenuIdList.add(next);
                                iterator.remove();
                            }

                        }

                        ruleList.addAll(
                                buildMenuRule(
                                        menuIdList,
                                        v0,
                                        BusinessConstant.CASBIN_SP_COLON + systemPlatform,
                                        BusinessConstant.CASBIN_RM_COLON + rootMenu,
                                        BusinessConstant.CASBIN_M_COLON,
                                        BusinessConstant.FULL
                                )
                        );
                    }

                    if (CollectionUtils.isNotEmpty(partiallyMenuIdList)) {
                        ruleList.addAll(
                                buildMenuRule(
                                        partiallyMenuIdList,
                                        v0,
                                        BusinessConstant.CASBIN_SP_COLON + systemPlatform,
                                        BusinessConstant.CASBIN_RM_COLON + rootMenu,
                                        BusinessConstant.CASBIN_M_COLON,
                                        BusinessConstant.PARTIALLY
                                )
                        );
                    }

                }
        );
    }

    public List<List<String>> buildMenuRule(Collection<Long> menuIdList, String v0, String v1, String v2, String v3Prefix, String v4) {
        if (CollectionUtils.isEmpty(menuIdList)) {
            return Collections.emptyList();
        }
        return menuIdList.stream().map(
                e -> Policy.builder()
                        // R:id,SP:name,MR:id,M:id,FULL|PARTIALLY,POUND
                        // WCSN:id,SP:name,MR:id,M:id,FULL|PARTIALLY,POUND
                        // U:id,SP:name,MR:id,M:id,FULL|PARTIALLY,POUND
                        .v0(v0)
                        .v1(v1)
                        .v2(v2)
                        .v3(v3Prefix + e)
                        .v4(v4)
                        .v5(BusinessConstant.POUND)
                        .build()
                        .getArr()
        ).collect(Collectors.toList());
    }

    public PermissionDTO emptyPermissionDTO() {
        PermissionDTO permissionDTO = new PermissionDTO();
        permissionDTO.setDataPermissionDTOList(Collections.EMPTY_LIST);
        permissionDTO.setMenuPermissionDTOList(Collections.EMPTY_LIST);
        return permissionDTO;
    }

    public PermissionDTO getPermissionDTO(List<List<String>> permissionList) {
        Map<String, List<List<String>>> map = permissionList.stream()
                .collect(
                        Collectors.groupingBy(
                                e -> e.get(BusinessConstant.ONE).split(BusinessConstant.COLON)[BusinessConstant.ZERO]
                        )
                );
        PermissionDTO permissionDTO = new PermissionDTO();
        Set<String> roleSet = new HashSet<>();
        // 设置默认值
        permissionDTO.setMenuPermissionDTOList(Collections.emptyList());
        permissionDTO.setDataPermissionDTOList(Collections.emptyList());
        // R:id,SP:name,MR:id,M:id,POUND,POUND
        // WCSN:id,SP:name,MR:id,M:id,POUND,POUND
        // U:id,SP:name,MR:id,M:id,POUND,POUND
        // SP:name,MR:id 分组
        if (map.containsKey(BusinessConstant.CASBIN_SP)) {
            List<List<String>> menuPermissionList = map.get(BusinessConstant.CASBIN_SP);
            List<MenuPermissionDTO> menuPermissionDTOList = new ArrayList<>();
            Map<String, List<List<String>>> spToRuleMap = menuPermissionList.stream()
                    .collect(Collectors.groupingBy(e -> e.get(BusinessConstant.ONE)));
            spToRuleMap.forEach(
                    (k, v) -> {
                        MenuPermissionDTO menuPermissionDTO = new MenuPermissionDTO();
                        menuPermissionDTO.setSystemPlatform(v.get(BusinessConstant.ZERO).get(BusinessConstant.ONE).split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                        menuPermissionDTO.setMenuId(Long.valueOf(v.get(BusinessConstant.ZERO).get(BusinessConstant.TWO).split(BusinessConstant.COLON)[BusinessConstant.ONE]));
                        List<Long> menuIdList = new ArrayList<>();
                        List<Long> partiallyMenuIdList = new ArrayList<>();
                        v.forEach(
                                e -> {
                                    if (e.get(BusinessConstant.ZERO).startsWith(BusinessConstant.CASBIN_R_COLON)) {
                                        roleSet.add(e.get(BusinessConstant.ZERO).split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                                    }
                                    if (e.get(BusinessConstant.FOUR).equals(BusinessConstant.FULL)) {
                                        menuIdList.add(Long.valueOf(e.get(BusinessConstant.THREE).split(BusinessConstant.COLON)[BusinessConstant.ONE]));
                                    } else if (e.get(BusinessConstant.FOUR).equals(BusinessConstant.PARTIALLY)) {
                                        partiallyMenuIdList.add(Long.valueOf(e.get(BusinessConstant.THREE).split(BusinessConstant.COLON)[BusinessConstant.ONE]));
                                    }
                                }
                        );

                        menuPermissionDTO.setMenuIdList(menuIdList);
                        menuPermissionDTO.setPartiallyMenuIdList(partiallyMenuIdList);
                        menuPermissionDTOList.add(menuPermissionDTO);
                    }
            );
            permissionDTO.setMenuPermissionDTOList(menuPermissionDTOList);
        }

        // R:id,TC:typeCode,DC:dataCode,POUND,POUND,POUND
        // WCSN:id,TC:typeCode,DC:dataCode,POUND,POUND,POUND
        // U:id,TC:typeCode,DC:dataCode,POUND,POUND,POUND
        // TypeCode、DataCode 分组
        if (map.containsKey(BusinessConstant.CASBIN_TC)) {
            List<List<String>> dataPermissionList = map.get(BusinessConstant.CASBIN_TC);
            List<DataPermissionDTO> dataPermissionDTOList = new ArrayList<>();

            Map<String, List<List<String>>> tcToRuleMap = dataPermissionList.stream()
                    .collect(Collectors.groupingBy(e -> e.get(BusinessConstant.ONE)));
            tcToRuleMap.forEach(
                    (k, v) -> {
                        DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                        dataPermissionDTO.setTypeCode(k.split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                        List<String> dataCodeList = Lists.newArrayList();
                        List<DataCodeExtensionTagDTO> dataTagList = Lists.newArrayList();
                        for (List<String> item : v) {
                            if (item.get(BusinessConstant.ZERO).startsWith(BusinessConstant.CASBIN_R_COLON)) {
                                roleSet.add(item.get(BusinessConstant.ZERO).split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                            }
                            String dataCode = item.get(BusinessConstant.TWO).split(BusinessConstant.COLON)[BusinessConstant.ONE];
                            DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                            tag.setDataCode(dataCode);
                            List<String> extList = Lists.newArrayList();
                            tag.setExtensionTagCodeList(extList);
                            if(StringUtils.isNotBlank(item.get(3)) && !BusinessConstant.POUND.equals(item.get(3))){
                                extList.addAll(Arrays.asList(item.get(3).split(",")));
                                if (extList.contains(BusinessConstant.NOT)) {
                                    tag.setExtensionTagCodeList(Lists.newArrayList(BusinessConstant.NOT));
                                    dataTagList.add(tag);
                                    continue;
                                }
                            }
                            dataTagList.add(tag);
                            dataCodeList.add(dataCode);
                        }
                        dataPermissionDTO.setDataCodeList(dataCodeList);
                        dataPermissionDTO.setDataTagList(dataTagList);
                        dataPermissionDTOList.add(dataPermissionDTO);
                    }
            );
            permissionDTO.setDataPermissionDTOList(dataPermissionDTOList);
        }
        permissionDTO.setRoleList(roleSet.stream().map(Long::valueOf).collect(Collectors.toList()));
        // 对菜单和数据权限去重
        UserAuthorizationConvert.duplicateRemovalForPermissionDTO(permissionDTO);
        return permissionDTO;
    }

    public List<String> processWorkCenterAssociation(Long workCenterId) {
        BusinessLogicException.checkTrue(Objects.isNull(workCenterId), PermissionErrorCodeEnums.WORK_CENTER_ID_NOT_NULL);
        List<List<String>> roleWorkCenterList = enforcer.getFilteredGroupingPolicy(BusinessConstant.ONE, BusinessConstant.CASBIN_WC_COLON + workCenterId);
        List<String> workCenterAssociationList = new ArrayList<>();
        processV0TypeRecord(roleWorkCenterList, workCenterAssociationList, BusinessConstant.CASBIN_R);
        return workCenterAssociationList;
    }

    public void processV0TypeRecord(List<List<String>> groupList, List<String> result, String casbinType) {
        if (CollectionUtils.isNotEmpty(groupList)) {
            processTypeRecord(groupList, BusinessConstant.ZERO, casbinType, result);
        }
    }

    @Override
    public Map<Long, List<Long>> getMenuMapByRoleId(Long roleId) {
        List<List<String>> userPermission = enforcer.getPermissionsForKey(Arrays.asList(BusinessConstant.CASBIN_R_COLON + roleId));
        return getMenu(userPermission);
    }


    public void processV1TypeRecord(List<List<String>> groupList, List<String> result, String casbinType) {
        if (CollectionUtils.isNotEmpty(groupList)) {
            processTypeRecord(groupList, BusinessConstant.ONE, casbinType, result);
        }
    }

    public void processV3TypeRecord(List<List<String>> groupList, List<String> result, String casbinType) {
        if (CollectionUtils.isNotEmpty(groupList)) {
            processTypeRecord(groupList, BusinessConstant.THREE, casbinType, result);

        }
    }

    public void processTypeRecord(List<List<String>> groupList, Integer index, String casbinType, List<String> result) {
        groupList.stream()
                .filter(
                        e -> e.get(index).split(BusinessConstant.COLON)[BusinessConstant.ZERO]
                                .equals(casbinType)
                )
                .forEach(
                        group -> {
                            result.add(group.get(index).split(BusinessConstant.COLON)[BusinessConstant.ONE]);
                        }
                );
    }

    @Override
    public void addGroup(Collection<?> newIdList, String v0, String casbinType) {
        enforcer.addGroupingPolicies(
                newIdList.stream()
                        .map(
                                e -> Group.builder()
                                        .v0(v0)
                                        .v1(casbinType + e)
                                        .build()
                                        .getArr()
                        )
                        .collect(Collectors.toList())
        );
    }

    @Override
    public void patchRemoveFilteredPolicy(Integer zero, List<String[]> idArr) {
        if (CollectionUtils.isEmpty(idArr)) {
            return;
        }
        enforcer.patchRemoveFilteredPolicy(com.imile.permission.jcasbin.common.constant.BusinessConstant.ZERO, idArr);
    }

    @Override
    public void patchDeleteRole(List<String> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        enforcer.patchDeleteRole(collect);
    }

    @Override
    public void addGroupingPolicy(List<String> arr) {
        enforcer.addGroupingPolicy(
                arr
        );
    }

    @Override
    public void removeGroupingPolicy(List<List<String>> postRoleList) {
        if (CollectionUtils.isNotEmpty(postRoleList)) {
            List<String[]> array = postRoleList.stream()
                    .map(removeId -> removeId.stream().toArray(String[]::new))
                    .collect(Collectors.toList());
            enforcer.patchRemoveFilteredGroupingPolicy(BusinessConstant.ZERO, array);
        }
    }

    public List<Long> getFullAndPartiallyMenuId(List<List<String>> userPermission) {
        return Optional.ofNullable(userPermission)
                .orElse((List<List<String>>) Collections.EMPTY_LIST)
                .stream()
                .filter(e -> e.size() > BusinessConstant.THREE)
                .map(e -> e.get(BusinessConstant.THREE))
                .filter(e -> StringUtils.isNotBlank(e) && e.contains(BusinessConstant.CASBIN_M_COLON))
                .map(e -> e.split(BusinessConstant.COLON)[BusinessConstant.ONE])
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    public List<List<String>> getTypeDataPermissionByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return Collections.emptyList();
        }
        List<List<String>> permissionList = enforcer.getPermissionsForUser(
                BusinessConstant.CASBIN_U_COLON + userCode
        );
        return getTypeData(permissionList);
    }

    public List<List<String>> getTypeData(List<List<String>> userPermission) {
        return Optional.ofNullable(userPermission)
                .orElse((List<List<String>>) Collections.EMPTY_LIST)
                .stream()
                .filter(e -> e.get(BusinessConstant.ONE).startsWith(BusinessConstant.CASBIN_TC_COLON))
                .collect(Collectors.toList());
    }

    public List<List<String>> getTypeDataPermissionByRoleIdList(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }


        List<List<String>> permissionList = enforcer.getPermissionsForKey(
                roleIdList.stream()
                        .map(roleId -> BusinessConstant.CASBIN_R_COLON + roleId)
                        .collect(Collectors.toList())
        );

        return getTypeData(permissionList);

    }

    public void processTypeData(List<List<String>> typeDataList, Map<String, List<String>> map) {
        if (CollectionUtils.isEmpty(typeDataList)) {
            return;
        }
        typeDataList.forEach(
                e -> {
                    String typeCode = e.get(BusinessConstant.ONE).split(BusinessConstant.CASBIN_TC_COLON)[BusinessConstant.ONE];
                    List<String> dataCodeList = map.getOrDefault(typeCode, new ArrayList<>());
                    String dataCode = e.get(BusinessConstant.TWO).split(BusinessConstant.CASBIN_DC_COLON)[BusinessConstant.ONE];
                    if (!dataCodeList.contains(dataCode)) {
                        dataCodeList.add(dataCode);
                    }
                    map.put(typeCode, dataCodeList);
                }
        );
    }

    public List<List<String>> getPolicy(String userCode, List<Long> roleIdList, List<String> workCenterNodeIdList) {
        List<String> list = new ArrayList<>();
        list.add(BusinessConstant.CASBIN_U_COLON + userCode);
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            roleIdList.forEach(
                    roleId -> {
                        list.add(BusinessConstant.CASBIN_R_COLON + roleId);
                    }
            );
        }
        if (CollectionUtils.isNotEmpty(workCenterNodeIdList)) {
            workCenterNodeIdList.forEach(
                    workCenterNodeId -> {
                        list.add(BusinessConstant.CASBIN_WCSN_COLON + workCenterNodeId);
                    }
            );
        }

        List<List<String>> userPermission = enforcer.getPermissionsForKey(list);

        return userPermission;
    }

    public void processUserNodeDataPermission(String userCode, List<DataPermissionDTO> dataPermissionDTOList, List<List<String>> ruleList) {
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            buildDataRule(dataPermissionDTOList, ruleList, BusinessConstant.CASBIN_U_COLON + userCode);
        }
    }

    public void processUserMenuPermission(String userCode, List<MenuPermissionDTO> menuPermissionDTOList, List<List<String>> ruleList) {
        if (CollectionUtils.isNotEmpty(menuPermissionDTOList)) {
            buildMenuRule(menuPermissionDTOList, ruleList, BusinessConstant.CASBIN_U_COLON + userCode);
        }
    }
}

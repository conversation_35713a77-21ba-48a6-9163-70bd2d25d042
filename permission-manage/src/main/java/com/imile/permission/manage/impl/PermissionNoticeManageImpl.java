package com.imile.permission.manage.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.hrms.api.organization.dto.OrgUserInfoDetailApiDTO;
import com.imile.hrms.api.wechat.query.WeChatTextParam;
import com.imile.idwork.IdWorkerUtil;
import com.imile.permission.config.SpringUtils;
import com.imile.permission.convert.PermissionNoticeConvert;
import com.imile.permission.dao.PermissionRecycleNoticeRelationDAO;
import com.imile.permission.domain.entity.PermissionRecycleNoticeRelationDO;
import com.imile.permission.domain.permission.dto.PermissionRecycleNoticeDTO;
import com.imile.permission.domain.permission.dto.UserPermissionExpireListDTO;
import com.imile.permission.domain.permission.dto.UserPermissionRecycleNoticeDTO;
import com.imile.permission.domain.user.dto.UserInfoDetailDTO;
import com.imile.permission.enums.MailTemplateEnums;
import com.imile.permission.enums.PermissionRecycleTypeEnum;
import com.imile.permission.integration.hrms.WechatSendTextIntegration;
import com.imile.permission.integration.hrms.client.BizOrganizationInfoApiClient;
import com.imile.permission.manage.MailTemplateSendManage;
import com.imile.permission.manage.PermissionNoticeManage;
import com.imile.permission.thread.MyThreadPoolExecutor;
import com.imile.permission.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/30
 */
@Slf4j
@Service
public class PermissionNoticeManageImpl implements PermissionNoticeManage {

    @Autowired
    private WechatSendTextIntegration wechatSendTextIntegration;
    @Value("${permission.notice.expired.collection.en}")
    private String permissionNoticeExpiredCollectionEn;
    @Value("${permission.notice.expired.collection.cn}")
    private String permissionNoticeExpiredCollectionCn;
    @Value("${permission.notice.expired.collection.batch.en}")
    private String permissionBatchNoticeExpiredCollectionEn;
    @Value("${permission.notice.expired.collection.batch.cn}")
    private String permissionBatchNoticeExpiredCollectionCn;
    @Value("${permission.notice.due.collection.en}")
    private String permissionNoticeDueCollectionEn;
    @Value("${permission.notice.due.collection.cn}")
    private String permissionNoticeDueCollectionCn;
    @Value("${permission.notice.due.menu.link}")
    private String menuLink;
    @Value("${permission.notice.due.role.link}")
    private String roleLink;
    @Value("${permission.notice.due.work-center.link}")
    private String workCenterLink;

    @Autowired
    private MailTemplateSendManage mailTemplateSendManage;

    @Autowired
    private BizOrganizationInfoApiClient bizOrganizationInfoApiClient;

    @Autowired
    private PermissionRecycleNoticeRelationDAO  permissionRecycleNoticeRelationDAO;



    @Override
    public void permissionRecycleNotice(UserPermissionRecycleNoticeDTO userPermissionRecycleNoticeDTO) {
        UserInfoDetailDTO userInfo = bizOrganizationInfoApiClient.getUserInfoDetailDTO(userPermissionRecycleNoticeDTO.getUserCode());

        sendRecycleNoticeForUser(userInfo, userPermissionRecycleNoticeDTO);
    }

    @Override
    public void batchPermissionRecycleNotice(Collection<UserPermissionExpireListDTO> noticeList) {

        Map<String, UserPermissionExpireListDTO> userPermissionMap = noticeList.stream().collect(Collectors.toMap(UserPermissionExpireListDTO::getUserCode, Function.identity(), (v1,v2) -> v1));
        List<OrgUserInfoDetailApiDTO> userInfoList = bizOrganizationInfoApiClient.userInfoDetailList(Lists.newArrayList(userPermissionMap.keySet()));
        noticeList = null;
        for (OrgUserInfoDetailApiDTO userInfo : userInfoList) {
            sendBatchRecycleNoticeForUser(userInfo,userPermissionMap);
        }
    }

    @Override
    public void permissionDueNotice(Collection<UserPermissionExpireListDTO>  noticeList) {
        Map<String, UserPermissionExpireListDTO> userPermissionMap = noticeList.stream().collect(Collectors.toMap(UserPermissionExpireListDTO::getUserCode, Function.identity(),(v1,v2) -> v1));
        List<OrgUserInfoDetailApiDTO> userInfoList = bizOrganizationInfoApiClient.userInfoDetailList(Lists.newArrayList(userPermissionMap.keySet()));
        noticeList = null;
        for (OrgUserInfoDetailApiDTO userInfo : userInfoList) {
            sendDueNoticeForUser(userInfo,userPermissionMap);
        }
    }

    private void sendRecycleNoticeForUser(UserInfoDetailDTO userInfo, UserPermissionRecycleNoticeDTO recycleInfo) {
        String expirationDate = recycleInfo.getExpirationDate().atOffset(ZoneOffset.ofHours(8)).format(DateUtils.getZoneDateFormat());
        String permission = getPermissionStr(recycleInfo);
        PermissionRecycleNoticeRelationDO noticeEntity = new PermissionRecycleNoticeRelationDO();
        noticeEntity.setId(IdWorkerUtil.getId());
        noticeEntity.setSendDate(LocalDateTime.now());
        noticeEntity.setUserCode(userInfo.getUserCode());
        noticeEntity.setSystemCode(recycleInfo.getSystemCode());
        noticeEntity.setPermissionType(recycleInfo.getPermissionType());
        noticeEntity.setPermissionIds(recycleInfo.getPermissionIds());
        noticeEntity.setExpirationDate(recycleInfo.getExpirationDate());
        String link;
        if(Objects.equals(recycleInfo.getPermissionType(), PermissionRecycleTypeEnum.DIRECT.getCode())){
            link =  menuLink + "?noticeId=" +  noticeEntity.getId();
        } else if(Objects.equals(recycleInfo.getPermissionType(), PermissionRecycleTypeEnum.ROLE.getCode())){
            link = roleLink;
        } else {
            link = workCenterLink;
        }
        String wechatMessage = String.format(permissionNoticeExpiredCollectionEn, userInfo.getUserName(),
                permission, expirationDate,link);
        // 异步发送邮件
        log.info("PermissionNoticeManageImpl | permissionRecycleNotice | 权限回收通知 {}", wechatMessage);
        MyThreadPoolExecutor.permissionRecycleNotice.execute(() -> {
            WeChatTextParam weChatTextParam = PermissionNoticeConvert.convertRoTextParam(recycleInfo, wechatMessage);
            try {
                wechatSendTextIntegration.sendWechatText(weChatTextParam);
            } catch (Exception e) {
                log.info("PermissionNoticeManageImpl | permissionRecycleNotice | 企微发送失败，msg: {}",e.getMessage());
            }
            Map<String, String> mailParams = Maps.newHashMap();
            mailParams.put("userName", userInfo.getUserName());
            mailParams.put("permissionName", permission);
            mailParams.put("expirationDate", expirationDate);
            mailParams.put("field3", link);

            mailTemplateSendManage.sendEmail(MailTemplateEnums.PERMISSION_WITHDRAWAL_NOTICE.getCode(),
                    null, MailTemplateEnums.PERMISSION_WITHDRAWAL_NOTICE.getTitle(), userInfo.getEmail(), null, mailParams);
            permissionRecycleNoticeRelationDAO.save(noticeEntity);
        });
    }

    private String getPermissionStr(UserPermissionRecycleNoticeDTO recycleInfo) {
        if(Objects.equals(recycleInfo.getPermissionType(), PermissionRecycleTypeEnum.DIRECT.getCode())){
            return "menu permission in " + recycleInfo.getSystemCode() +" system";
        }
        PermissionRecycleTypeEnum type = PermissionRecycleTypeEnum.getByCode(recycleInfo.getPermissionType());
        if(Objects.nonNull(type)){
            return type.getNameEn() + " permission \"" +recycleInfo.getPermissionName() + "\" in " + recycleInfo.getSystemCode() +" system";
        }
        return "permission \"" + recycleInfo.getPermissionName() + "\" in " + recycleInfo.getSystemCode() +" system";
    }

    private void sendBatchRecycleNoticeForUser(OrgUserInfoDetailApiDTO userInfo, Map<String, UserPermissionExpireListDTO> userPermissionMap) {
        UserPermissionExpireListDTO userNotice = userPermissionMap.get(userInfo.getUserCode());
        if(!userNotice.getExpiredPermissionList().isEmpty() || userNotice.isExistDirectExpired()){
            List<PermissionRecycleNoticeDTO> expiredRole = userNotice.getExpiredRoleJoinStringEn(roleLink);
            List<PermissionRecycleNoticeDTO> expiredMenu = userNotice.getExpiredMenuJoinStringEn(menuLink);
            List<PermissionRecycleNoticeDTO> expiredWorkCenter = userNotice.getExpiredWorkCenterJoinStringEn(workCenterLink);
            sendExpireMessageByList(userInfo, expiredRole);
            sendExpireMessageByList(userInfo, expiredMenu);
            sendExpireMessageByList(userInfo, expiredWorkCenter);
        }
    }

    private void sendExpireMessageByList(OrgUserInfoDetailApiDTO userInfo, List<PermissionRecycleNoticeDTO> noticeList) {
        for (PermissionRecycleNoticeDTO notice : noticeList) {
            String mailMessage = notice.getMessage();
            String wechatMessage = String.format(permissionBatchNoticeExpiredCollectionEn, userInfo.getUserName(), mailMessage, notice.getLink());
            log.info("PermissionNoticeManageImpl | batchPermissionRecycleNotice | 权限批量回收通知 {}", wechatMessage);
            // 异步发送邮件
            PermissionRecycleNoticeRelationDO noticeEntity = new PermissionRecycleNoticeRelationDO();
            noticeEntity.setId(notice.getNoticeId());
            noticeEntity.setSendDate(LocalDateTime.now());
            noticeEntity.setUserCode(userInfo.getUserCode());
            noticeEntity.setSystemCode(notice.getSystemCode());
            noticeEntity.setPermissionType(notice.getPermissionType());
            noticeEntity.setPermissionIds(notice.getPermissionIds());
            noticeEntity.setExpirationDate(notice.getExpirationDate());
            MyThreadPoolExecutor.permissionRecycleNotice.execute(new SendNoticeTask(userInfo, wechatMessage, mailMessage, MailTemplateEnums.PERMISSION_BATCH_WITHDRAWAL_NOTICE,noticeEntity, notice.getLink()));
        }
    }


    private void sendDueNoticeForUser(OrgUserInfoDetailApiDTO userInfo, Map<String, UserPermissionExpireListDTO> userPermissionMap) {
        UserPermissionExpireListDTO userNotice = userPermissionMap.get(userInfo.getUserCode());
        if(!userNotice.getExpireIn30PermissionList().isEmpty() || userNotice.isExistDirectExpireIn30()){
            List<PermissionRecycleNoticeDTO> expiredRole = userNotice.getExpireIn30RoleJoinStringEn(roleLink);
            List<PermissionRecycleNoticeDTO> expiredMenu = userNotice.getExpireIn30MenuJoinStringEn(menuLink);
            List<PermissionRecycleNoticeDTO> expiredWorkCenter = userNotice.getExpireIn30WorkCenterJoinStringEn(workCenterLink);
            sendDueMessageByList(userInfo, expiredRole);
            sendDueMessageByList(userInfo, expiredMenu);
            sendDueMessageByList(userInfo, expiredWorkCenter);
        }
        if(!userNotice.getExpireIn7PermissionList().isEmpty() || userNotice.isExistDirectExpireIn7()){
            List<PermissionRecycleNoticeDTO> expiredRole = userNotice.getExpireIn7RoleJoinStringEn(roleLink);
            List<PermissionRecycleNoticeDTO> expiredMenu = userNotice.getExpireIn7MenuJoinStringEn(menuLink);
            List<PermissionRecycleNoticeDTO> expiredWorkCenter = userNotice.getExpireIn7WorkCenterJoinStringEn(workCenterLink);
            sendDueMessageByList(userInfo, expiredRole);
            sendDueMessageByList(userInfo, expiredMenu);
            sendDueMessageByList(userInfo, expiredWorkCenter);
        }
        if(!userNotice.getExpireIn1PermissionList().isEmpty() || userNotice.isExistDirectExpireIn1()){
            List<PermissionRecycleNoticeDTO> expiredRole = userNotice.getExpireIn1RoleJoinStringEn(roleLink);
            List<PermissionRecycleNoticeDTO> expiredMenu = userNotice.getExpireIn1MenuJoinStringEn(menuLink);
            List<PermissionRecycleNoticeDTO> expiredWorkCenter = userNotice.getExpireIn1WorkCenterJoinStringEn(workCenterLink);
            sendDueMessageByList(userInfo, expiredRole);
            sendDueMessageByList(userInfo, expiredMenu);
            sendDueMessageByList(userInfo, expiredWorkCenter);
        }
    }

    private void sendDueMessageByList(OrgUserInfoDetailApiDTO userInfo, List<PermissionRecycleNoticeDTO> noticeList) {
        for (PermissionRecycleNoticeDTO notice : noticeList) {
            String mailMessage = notice.getMessage();
            String wechatMessage = String.format(permissionNoticeDueCollectionEn, userInfo.getUserName(), mailMessage, notice.getLink());
            log.info("PermissionNoticeManageImpl | permissionDueNotice | 权限到期提醒 {}", wechatMessage);
            PermissionRecycleNoticeRelationDO noticeEntity = new PermissionRecycleNoticeRelationDO();
            noticeEntity.setId(notice.getNoticeId());
            noticeEntity.setSendDate(LocalDateTime.now());
            noticeEntity.setUserCode(userInfo.getUserCode());
            noticeEntity.setSystemCode(notice.getSystemCode());
            noticeEntity.setPermissionType(notice.getPermissionType());
            noticeEntity.setPermissionIds(notice.getPermissionIds());
            noticeEntity.setExpirationDate(notice.getExpirationDate());
            // 异步发送邮件
            MyThreadPoolExecutor.PERMISSION_DUE_NOTICE.execute(new SendNoticeTask(userInfo, wechatMessage,mailMessage, MailTemplateEnums.PERMISSION_DUE_NOTICE,noticeEntity,notice.getLink()));
        }
    }


    private static class SendNoticeTask implements Runnable{

        private final OrgUserInfoDetailApiDTO userInfo;
        private final String wechatMessage;
        private final String mailMessage;
        private final WechatSendTextIntegration wechatSendTextIntegration;
        private final MailTemplateSendManage mailTemplateSendManage;
        private final MailTemplateEnums mailTemplateEnum;
        private final PermissionRecycleNoticeRelationDO notice;
        private final PermissionRecycleNoticeRelationDAO permissionRecycleNoticeRelationDAO;
        private final String link;

        public SendNoticeTask(OrgUserInfoDetailApiDTO userInfo, String wechatMessage, String mailMessage, MailTemplateEnums mailTemplateEnum, PermissionRecycleNoticeRelationDO notice, String link) {
            this.userInfo = userInfo;
            this.wechatMessage = wechatMessage;
            this.mailMessage = mailMessage;
            this.mailTemplateEnum = mailTemplateEnum;
            this.wechatSendTextIntegration = SpringUtils.getBean(WechatSendTextIntegration.class);
            this.mailTemplateSendManage = SpringUtils.getBean(MailTemplateSendManage.class);
            this.notice = notice;
            this.permissionRecycleNoticeRelationDAO = SpringUtils.getBean(PermissionRecycleNoticeRelationDAO.class);
            this.link = link;
        }

        @Override
        public void run() {
            WeChatTextParam weChatTextParam = new WeChatTextParam();
            weChatTextParam.setUserCodes(Collections.singletonList(userInfo.getUserCode()));
            weChatTextParam.setContent(wechatMessage);
            weChatTextParam.setContentEn(wechatMessage);
            try {
                wechatSendTextIntegration.sendWechatText(weChatTextParam);
            } catch (Exception e) {
                log.info("PermissionNoticeManageImpl | permissionRecycleNotice | 企微发送失败，msg: {}", e.getMessage());
            }

            Map<String,String> paramMap = Maps.newHashMap();
            paramMap.put("field1", userInfo.getUserName());
            paramMap.put("field2", mailMessage);
            paramMap.put("field3", link);

            mailTemplateSendManage.sendEmail(mailTemplateEnum.getCode(),
                    null,mailTemplateEnum.getTitle(), userInfo.getEmail(), null, paramMap);
            permissionRecycleNoticeRelationDAO.save(notice);
        }

    }
}

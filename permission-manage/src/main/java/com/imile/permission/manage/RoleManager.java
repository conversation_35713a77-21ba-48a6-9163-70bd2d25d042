package com.imile.permission.manage;

import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.role.dto.DefaultRoleIdDTO;
import com.imile.permission.domain.role.dto.SysRoleDTO;
import com.imile.permission.domain.role.param.RoleUpdateParam;
import com.imile.permission.domain.role.param.RoleUpdatePermissionRuleParam;
import com.imile.permission.domain.role.vo.RoleListVO;
import com.imile.permission.domain.user.dto.UserInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
public interface RoleManager {

    @Deprecated
    void updateRolePermission(SysRoleDO sysRole, RoleUpdateParam roleUpdateParam);

    void updateRolePermissionRule(SysRoleDO sysRole, RoleUpdatePermissionRuleParam roleUpdatePermissionRuleParam);

    /**
     * 根据角色类型查询
     * @param roleType
     * @param authScene
     * @return
     */
    List<SysRoleDTO> listRoleByType(Integer roleType, Integer authScene);

    /**
     * 获取员工拥有的默认角色
     * 包括岗位和角色授权，能匹配上的都算
     * @param userCode
     * @return
     */
    List<SysRoleDTO> getUserDefaultRole(String userCode);

    /**
     * 获取默认的角色id
     * @param userInfoDTO
     * @return
     */
    DefaultRoleIdDTO getUserDefaultRoleId(UserInfoDTO userInfoDTO);

    List<RoleListVO> getUserDefaultSystemRole(String userCode, String systemCode);

    List<RoleListVO> getUserBusinessSystemRole(String userCode, String systemCode);
}

package com.imile.permission.manage.impl;

import com.alibaba.fastjson.JSON;
import com.imile.permission.dao.UserDynamicDataPermissionDAO;
import com.imile.permission.domain.dataPermission.dto.TypeCodeDataCodeDTO;
import com.imile.permission.domain.entity.UserDynamicDataPermissionDO;
import com.imile.permission.manage.UserDynamicDataPermissionManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/2
 */
@Slf4j
@Service
public class UserDynamicDataPermissionManageImpl implements UserDynamicDataPermissionManage {
    @Autowired
    private UserDynamicDataPermissionDAO userDynamicDataPermissionDAO;

    @Override
    public void deleteByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return;
        }
        userDynamicDataPermissionDAO.deleteByUserCode(userCode);
    }

    @Override
    public void deleteByUserCodeAndTypeCode(String userCode, String typeCode) {
        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(typeCode)) {
            return;
        }
        userDynamicDataPermissionDAO.deleteByUserCodeAndTypeCode(userCode, typeCode);
    }

    @Override
    public void saveUserDynamicDataPermission(List<UserDynamicDataPermissionDO> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        userDynamicDataPermissionDAO.saveBatch(result);
    }

    @Override
    public void deleteByUserCodeAndTypeCodeAndDataCode(String userCode, String typeCode, String dataCode) {
        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(typeCode) || StringUtils.isBlank(dataCode)) {
            return;
        }
        userDynamicDataPermissionDAO.deleteByUserCodeAndTypeCodeAndDataCode(userCode, typeCode, dataCode);
    }

    @Override
    public List<String> selectDataContentByUserCodeAndTypeCodeAndDataCode(String userCode, String typeCode, String dataCode) {
        return userDynamicDataPermissionDAO.selectDataContentByUserCodeAndTypeCodeAndDataCode(userCode, typeCode, dataCode)
                .stream()
                .map(UserDynamicDataPermissionDO::getDataContent)
                .filter(StringUtils::isNotBlank)
                .map(
                        e -> JSON.parseArray(e, String.class)
                )
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteByUserCodeAndTypeCodeDataCodeDTO(String userCode, List<TypeCodeDataCodeDTO> typeCodeDataCodeDTOS) {
        if (CollectionUtils.isEmpty(typeCodeDataCodeDTOS)) {
            return;
        }
        userDynamicDataPermissionDAO.deleteByUserCodeAndTypeCodeDataCodeDTO(userCode, typeCodeDataCodeDTOS);
    }
}

package com.imile.permission.manage.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.genesis.api.model.result.user.UserBaseInfoDTO;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.context.RequestInfoHolder;
import com.imile.permission.convert.SysRoleConvert;
import com.imile.permission.dao.SysRoleDao;
import com.imile.permission.domain.dataPermission.dto.DataCodeExtensionTagDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.dto.DimensionDataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.MultiDynamicDataConfigValueDTO;
import com.imile.permission.domain.dataPermission.vo.SpecificRuleConfigVO;
import com.imile.permission.domain.entity.PermissionAuthorizationRuleDO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.permission.dto.PermissionDTO;
import com.imile.permission.domain.role.dto.DefaultRoleIdDTO;
import com.imile.permission.domain.role.dto.SysRoleDTO;
import com.imile.permission.domain.role.param.RoleUpdateParam;
import com.imile.permission.domain.role.param.RoleUpdatePermissionRuleParam;
import com.imile.permission.domain.role.query.SysRoleQuery;
import com.imile.permission.domain.role.vo.RoleListVO;
import com.imile.permission.domain.user.dto.UserInfoDTO;
import com.imile.permission.enums.RoleApplyTypeEnum;
import com.imile.permission.enums.RoleAuthSceneEnum;
import com.imile.permission.enums.RoleTypeEnum;
import com.imile.permission.integration.hermes.OrgSystemResourceIntegration;
import com.imile.permission.integration.hrms.HrmsPostIntegration;
import com.imile.permission.integration.resource.ResSystemResourceIntegration;
import com.imile.permission.manage.DataPermissionManage;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.manage.RoleManager;
import com.imile.permission.manage.RolePostManage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@Service
public class RoleManagerImpl implements RoleManager {

    @Autowired
    private RolePostManage rolePostManage;
    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;
    @Autowired
    private DataPermissionManage dataPermissionManage;
    @Autowired
    private SysRoleDao sysRoleDao;
    @Autowired
    private HrmsPostIntegration hrmsPostIntegration;

    @Autowired
    private ResSystemResourceIntegration resSystemResourceIntegration;

    @Autowired
    private OrgSystemResourceIntegration orgSystemResourceIntegration;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRolePermission(SysRoleDO sysRole, RoleUpdateParam roleUpdateParam) {

        rolePostManage.updateDO(sysRole);
        // 删除 casbin 角色权限
        refactoringPermissionCasbinRuleManage.deleteRolePermission(sysRole.getId());
        List<DataPermissionDTO> result = new ArrayList<>();
        // 主数据权限
        List<DataPermissionDTO> dataPermissionDTOList = roleUpdateParam.getDataPermissionDTOList();
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            result.addAll(dataPermissionDTOList);
        }
        rolePostManage.savePermission(sysRole.getId(), roleUpdateParam.getMenuPermissionDTOList(), result);
    }

    @Override
    public void updateRolePermissionRule(SysRoleDO sysRole, RoleUpdatePermissionRuleParam roleUpdatePermissionRuleParam) {
        PermissionDTO oldPermission = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(sysRole.getId());

        List<DataPermissionRuleDTO> baseDataPermissionDTO = Optional.ofNullable(roleUpdatePermissionRuleParam.getBaseDataPermissionDTO())
                .orElse(Lists.newArrayList());

        List<DataPermissionRuleDTO> mainDataPermissionDTO = Optional.ofNullable(roleUpdatePermissionRuleParam.getMainDataPermissionDTO())
                .orElse(Lists.newArrayList());

        List<DataPermissionRuleDTO> dynamicDataPermissionDTO = Optional.ofNullable(roleUpdatePermissionRuleParam.getDynamicDataPermissionDTO())
                .orElse(Lists.newArrayList());

        List<DataPermissionRuleDTO> singleDynamicDataConfigValueDTO = Optional.ofNullable(roleUpdatePermissionRuleParam.getSingleDynamicDataConfigValueDTO()).orElse(Lists.newArrayList());
        List<MultiDynamicDataConfigValueDTO> multiDynamicDataConfigValueDTO = Optional.ofNullable(roleUpdatePermissionRuleParam.getMultiDynamicDataConfigValueDTO()).orElse(Lists.newArrayList());

        // 全选
        DataPermissionDTO selectAllDataPermissionDTO = new DataPermissionDTO();
        selectAllDataPermissionDTO.setTypeCode(BusinessConstant.SELECT_ALL_TYPE_CODE);
        List<String> selectAllCodeList = Lists.newArrayList();
        selectAllDataPermissionDTO.setDataCodeList(selectAllCodeList);

        List<DataPermissionDTO> dataPermissionDTOList = new ArrayList<>();
        List<PermissionAuthorizationRuleDO> ruleList = new ArrayList<>();
        baseDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            List<String> dataCodeList = e.getDataCodeList();
                            if (CollectionUtils.isNotEmpty(dataCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode(e.getTypeCode());
                                dataPermissionDTO.setTypeName(e.getTypeName());
                                dataPermissionDTO.setDataCodeList(dataCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }
                        }
                );


        mainDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            String typeCode = e.getTypeCode();
                            String typeName = e.getTypeName();
                            List<String> dataCodeList = e.getDataCodeList();
                            List<DataCodeExtensionTagDTO> tagList = e.getDataExtensionTagList();

                            // 全选
                            if (e.getSelectAll() != null && e.getSelectAll() == 1) {
                                selectAllCodeList.add(e.getTypeCode());
                            }

                            if (CollectionUtils.isEmpty(tagList)) {
                                tagList = Lists.newArrayList();
                                e.setDataExtensionTagList(tagList);
                            }
                            List<String> functionCodeList = e.getFunctionCodeList();
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();
                            List<String> extDataCodes = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(tagList)) {
                                extDataCodes.addAll(tagList.stream().map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                            }
                            if (CollectionUtils.isNotEmpty(dataCodeList)) {
                                dataCodeList.removeAll(extDataCodes);
                                for (String code : dataCodeList) {
                                    DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                                    tag.setDataCode(code);
                                    tag.setExtensionTagCodeList(Lists.newArrayList());
                                    tagList.add(tag);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(tagList) || Objects.equals(e.getSelectAll(), 1)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode(typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataTagList(tagList);
                                dataPermissionDTO.setSelectAll(e.getSelectAll());
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(functionCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode("$" + typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataCodeList(functionCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                specificRuleConfigList.stream().forEach(
                                        SpecificRuleConfigVO::check
                                );
                                PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = new PermissionAuthorizationRuleDO();
                                permissionAuthorizationRuleDO.setAuthorizationType(BusinessConstant.ROLE);
                                permissionAuthorizationRuleDO.setAuthorizationRoleId(sysRole.getId());
                                permissionAuthorizationRuleDO.setRelationType(BusinessConstant.MAINDATA);
                                permissionAuthorizationRuleDO.setRelationTypeCode(typeCode);
                                permissionAuthorizationRuleDO.setAuthorizationRuleJson(JSON.toJSONString(specificRuleConfigList));
                                ruleList.add(permissionAuthorizationRuleDO);
                            }
                        }
                );

        singleDynamicDataConfigValueDTO.stream()
                .forEach(
                        e -> {
                            String typeCode = e.getTypeCode();
                            String typeName = e.getTypeName();
                            List<String> dataCodeList = e.getDataCodeList();
                            List<DataCodeExtensionTagDTO> tagList = e.getDataExtensionTagList();
                            if (CollectionUtils.isEmpty(tagList)) {
                                tagList = Lists.newArrayList();
                                e.setDataExtensionTagList(tagList);
                            }
                            List<String> functionCodeList = e.getFunctionCodeList();
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();

                            // 全选
                            if (e.getSelectAll() != null && e.getSelectAll() == 1) {
                                selectAllCodeList.add(e.getTypeCode());
                            }

                            List<String> extDataCodes = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(tagList)) {
                                extDataCodes.addAll(tagList.stream().map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                            }
                            if (CollectionUtils.isNotEmpty(dataCodeList)) {
                                dataCodeList.removeAll(extDataCodes);
                                for (String code : dataCodeList) {
                                    DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                                    tag.setDataCode(code);
                                    tag.setExtensionTagCodeList(Lists.newArrayList());
                                    tagList.add(tag);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(tagList) || Objects.equals(e.getSelectAll(), 1)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode(typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataTagList(tagList);
                                dataPermissionDTO.setSelectAll(e.getSelectAll());
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(functionCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode("$" + typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataCodeList(functionCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                specificRuleConfigList.stream().forEach(
                                        SpecificRuleConfigVO::check
                                );
                                PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = new PermissionAuthorizationRuleDO();
                                permissionAuthorizationRuleDO.setAuthorizationType(BusinessConstant.ROLE);
                                permissionAuthorizationRuleDO.setAuthorizationRoleId(sysRole.getId());
                                permissionAuthorizationRuleDO.setRelationType(BusinessConstant.SINGLEDIMENSION);
                                permissionAuthorizationRuleDO.setRelationTypeCode(typeCode);
                                permissionAuthorizationRuleDO.setAuthorizationRuleJson(JSON.toJSONString(specificRuleConfigList));
                                ruleList.add(permissionAuthorizationRuleDO);
                            }
                        }
                );

        for (MultiDynamicDataConfigValueDTO dto : multiDynamicDataConfigValueDTO) {
            String typeCodeDynamic = dto.getTypeCode();
            String typeNameDynamic = dto.getTypeName();
            List<DimensionDataPermissionRuleDTO> dimensionConfigList = Optional.ofNullable(dto.getDimensionConfigList()).orElse(Lists.newArrayList());
            for (DimensionDataPermissionRuleDTO e : dimensionConfigList) {
                String typeCode = typeCodeDynamic + ">" + e.getTypeCode();
                String typeName = typeNameDynamic + ">" + e.getTypeName();

                // 全选
                if (e.getSelectAll() != null && e.getSelectAll() == 1) {
                    selectAllCodeList.add(typeCode);
                }

                List<String> dataCodeList = e.getDataCodeList();
                List<DataCodeExtensionTagDTO> paramTagList = e.getDataExtensionTagList();
                if (CollectionUtils.isEmpty(paramTagList)) {
                    paramTagList = Lists.newArrayList();
                    e.setDataExtensionTagList(paramTagList);
                }

                List<String> extDataCodes = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(paramTagList)) {
                    extDataCodes.addAll(paramTagList.stream().map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                }

                if (CollectionUtils.isNotEmpty(dataCodeList)) {
                    dataCodeList.removeAll(extDataCodes);
                    for (String code : dataCodeList) {
                        DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                        tag.setDataCode(code);
                        tag.setExtensionTagCodeList(Lists.newArrayList());
                        paramTagList.add(tag);
                    }
                }

                if (CollectionUtils.isNotEmpty(paramTagList) || Objects.equals(e.getSelectAll(), 1)) {
                    DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                    dataPermissionDTO.setTypeCode(typeCode);
                    dataPermissionDTO.setTypeName(typeName);
                    dataPermissionDTO.setDataTagList(paramTagList);
                    dataPermissionDTO.setSelectAll(e.getSelectAll());
                    dataPermissionDTOList.add(dataPermissionDTO);
                }

            }

        }


        dynamicDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            String typeCode = e.getTypeCode();
                            String typeName = e.getTypeName();
                            List<String> functionCodeList = e.getFunctionCodeList();
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();


                            if (CollectionUtils.isNotEmpty(functionCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode("$" + typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataCodeList(functionCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                specificRuleConfigList.stream().forEach(
                                        SpecificRuleConfigVO::check
                                );
                                PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = new PermissionAuthorizationRuleDO();
                                permissionAuthorizationRuleDO.setAuthorizationType(BusinessConstant.ROLE);
                                permissionAuthorizationRuleDO.setAuthorizationRoleId(sysRole.getId());
                                permissionAuthorizationRuleDO.setRelationType(BusinessConstant.FUNCTIONDATA);
                                permissionAuthorizationRuleDO.setRelationTypeCode(typeCode);
                                permissionAuthorizationRuleDO.setAuthorizationRuleJson(JSON.toJSONString(specificRuleConfigList));
                                ruleList.add(permissionAuthorizationRuleDO);
                            }
                        }
                );

        List<DataPermissionDTO> oldDataPermissionDTOList = Optional.ofNullable(oldPermission.getDataPermissionDTOList())
                .orElse((List<DataPermissionDTO>) Collections.EMPTY_LIST);
        Map<String, DataPermissionDTO> oldDataMap = oldDataPermissionDTOList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity()));
        Map<String, DataPermissionDTO> dataMap = dataPermissionDTOList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity()));

        // 数据比对
        Set<String> oldDataKeySet = oldDataMap.keySet();
        Set<String> dataKeySet = dataMap.keySet();


        Collection<String> updateData = CollectionUtils.intersection(oldDataKeySet, dataKeySet);
        if (CollectionUtils.isNotEmpty(updateData)) {
            updateData.forEach(
                    e -> {
                        DataPermissionDTO oldDataPermissionDTO = oldDataMap.get(e);
                        DataPermissionDTO newDataPermissionDTO = dataMap.get(e);
                        List<String> oldDataCodeList = Optional.ofNullable(oldDataPermissionDTO.getDataCodeList()).orElse(Lists.newArrayList());
                        List<String> newDataCodeList = Optional.ofNullable(newDataPermissionDTO.getDataCodeList()).orElse(Lists.newArrayList());
                        List<DataCodeExtensionTagDTO> dataTagList = newDataPermissionDTO.getDataTagList();
                        if (CollectionUtils.isNotEmpty(dataTagList)) {
                            newDataCodeList.addAll(dataTagList.stream().map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                        }
                        List<DataCodeExtensionTagDTO> oldDataTagList = oldDataPermissionDTO.getDataTagList();
                        if (CollectionUtils.isNotEmpty(oldDataTagList)) {
                            oldDataCodeList.addAll(oldDataTagList.stream().filter(oldTag -> CollectionUtils.isNotEmpty(oldTag.getExtensionTagCodeList()) && oldTag.getExtensionTagCodeList().contains(BusinessConstant.NOT)).map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                        }

                        // 删除部分
                        Collection<String> removeDataCode = CollectionUtils.removeAll(oldDataCodeList, newDataCodeList);
                        if (CollectionUtils.isNotEmpty(removeDataCode)) {
                            DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                            dataPermissionDTO.setTypeCode(e);
                            List<String> removeCodes = Lists.newArrayList(removeDataCode);
                            List<DataCodeExtensionTagDTO> tagList = removeCodes.stream().map(code -> {
                                DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                                tag.setDataCode(code);
                                tag.setExtensionTagCodeList(Lists.newArrayList(BusinessConstant.NOT));
                                return tag;
                            }).collect(Collectors.toList());
                            dataPermissionDTO.setDataTagList(tagList);
                            dataPermissionDTOList.add(dataPermissionDTO);
                        }
                    }
            );
        }

        // 全选处理
        if (CollectionUtils.isNotEmpty(selectAllCodeList)) {
            dataPermissionDTOList.add(selectAllDataPermissionDTO);
        }
        sysRoleDao.updateById(sysRole);
        // 删除 casbin 角色权限
        refactoringPermissionCasbinRuleManage.deleteRolePermission(sysRole.getId());

        List<MenuPermissionDTO> currentMenuPermissionDTOList = roleUpdatePermissionRuleParam.getMenuPermissionDTOList();
        rolePostManage.savePermission(sysRole.getId(), currentMenuPermissionDTOList, dataPermissionDTOList);

        dataPermissionManage.updateRolePermissionAuthorizationRule(sysRole.getId(), ruleList);
        SysRoleDO role = rolePostManage.getById(sysRole.getId());
        // 商家子角色逻辑处理
        if (Objects.isNull(role)) {
            return;
        }
        Integer authScene = role.getAuthScene();
        if (!RoleAuthSceneEnum.CLIENT.getCode().equals(authScene)) {
            return;
        }

        Long parentId = role.getParentId();
        if (Objects.isNull(parentId) || !parentId.equals(0L)) {
            return;
        }

        SysRoleDO childRole = rolePostManage.getChildRole(role.getId());
        if (Objects.isNull(childRole)) {
            return;
        }
        List<MenuPermissionDTO> menuPermissionDTOList = oldPermission.getMenuPermissionDTOList();
        List<MenuPermissionDTO> deleteMenuList = findDeletedMenus(menuPermissionDTOList, currentMenuPermissionDTOList);
        if (CollectionUtils.isEmpty(deleteMenuList)) {
            return;
        }

        for (MenuPermissionDTO menuPermissionDTO : deleteMenuList) {
            List<Long> all = new ArrayList<>();
            List<Long> menuIdList = Optional.ofNullable(menuPermissionDTO.getMenuIdList()).orElse(Lists.newArrayList());
            List<Long> partiallyMenuIdList = Optional.ofNullable(menuPermissionDTO.getPartiallyMenuIdList()).orElse(Lists.newArrayList());
            all.addAll(menuIdList);
            all.addAll(partiallyMenuIdList);
            List<Long> parentMenuIDList = resSystemResourceIntegration.getParentMenuIdList(all);
            menuPermissionDTO.setMenuIdList(parentMenuIDList);
        }

        PermissionDTO childPermission = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(childRole.getId());
        List<MenuPermissionDTO> childMenuList = childPermission.getMenuPermissionDTOList();
        List<MenuPermissionDTO> currentchildMenuList = removeMenus(childMenuList, deleteMenuList);

        for (MenuPermissionDTO menuPermissionDTO : currentchildMenuList) {
            List<Long> all = new ArrayList<>();
            List<Long> menuIdList = Optional.ofNullable(menuPermissionDTO.getMenuIdList()).orElse(Lists.newArrayList());
            List<Long> partiallyMenuIdList = Optional.ofNullable(menuPermissionDTO.getPartiallyMenuIdList()).orElse(Lists.newArrayList());
            all.addAll(menuIdList);
            all.addAll(partiallyMenuIdList);
            List<Long> parentMenuIDList = resSystemResourceIntegration.getParentMenuIdList(all);
            menuPermissionDTO.setMenuIdList(parentMenuIDList);
        }
        // 删除旧子角色菜单
        refactoringPermissionCasbinRuleManage.deleteRolePermission(childRole.getId());
        // 保存新子角色菜单
        rolePostManage.savePermission(childRole.getId(), currentchildMenuList, childPermission.getDataPermissionDTOList());


    }

    private List<MenuPermissionDTO> removeMenus(List<MenuPermissionDTO> childMenuList, List<MenuPermissionDTO> deleteMenuList) {
        if (CollectionUtils.isEmpty(childMenuList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(deleteMenuList)) {
            return childMenuList;
        }

        List<MenuPermissionDTO> menuPermissionDTOList = Lists.newArrayList();

        Map<String, MenuPermissionDTO> map = childMenuList.stream()
                .collect(Collectors.toMap(MenuPermissionDTO::getSystemPlatform, Function.identity(), (v1, v2) -> v1));
        Map<String, MenuPermissionDTO> deleteMap = deleteMenuList.stream()
                .collect(Collectors.toMap(MenuPermissionDTO::getSystemPlatform, Function.identity(), (v1, v2) -> v1));

        map.forEach(
                (k, v) -> {
                    MenuPermissionDTO menuPermissionDTO = deleteMap.get(k);
                    if (Objects.nonNull(menuPermissionDTO)) {
                        Set<Long> menuIdSet = new HashSet<>();
                        menuIdSet.addAll(Optional.ofNullable(v.getMenuIdList()).orElse(Lists.newArrayList()));
                        menuIdSet.addAll(Optional.ofNullable(v.getPartiallyMenuIdList()).orElse(Lists.newArrayList()));

                        menuIdSet.removeAll(Optional.ofNullable(menuPermissionDTO.getMenuIdList()).orElse(Lists.newArrayList()));
                        menuIdSet.removeAll(Optional.ofNullable(menuPermissionDTO.getPartiallyMenuIdList()).orElse(Lists.newArrayList()));

                        MenuPermissionDTO menu = new MenuPermissionDTO();
                        menu.setSystemPlatform(menuPermissionDTO.getSystemPlatform());
                        menu.setMenuId(menuPermissionDTO.getMenuId());
                        menu.setMenuIdList(new ArrayList<>(menuIdSet));
                        menu.setPartiallyMenuIdList(new ArrayList<>());
                        menuPermissionDTOList.add(menu);
                    }
                }
        );

        return menuPermissionDTOList;
    }

    private List<MenuPermissionDTO> findDeletedMenus(List<MenuPermissionDTO> menuPermissionDTOList, List<MenuPermissionDTO> currentMenuPermissionDTOList) {
        if (CollectionUtils.isEmpty(menuPermissionDTOList)) {
            return Lists.newArrayList();
        }

        if (CollectionUtils.isEmpty(currentMenuPermissionDTOList)) {
            return menuPermissionDTOList;
        }
        List<MenuPermissionDTO> deleteMenuList = Lists.newArrayList();

        Map<String, MenuPermissionDTO> map = menuPermissionDTOList.stream()
                .collect(Collectors.toMap(MenuPermissionDTO::getSystemPlatform, Function.identity(), (v1, v2) -> v1));
        Map<String, MenuPermissionDTO> currentMap = currentMenuPermissionDTOList.stream()
                .collect(Collectors.toMap(MenuPermissionDTO::getSystemPlatform, Function.identity(), (v1, v2) -> v1));
        map.forEach(
                (k, v) -> {
                    MenuPermissionDTO menuPermissionDTO = currentMap.get(k);
                    if (Objects.isNull(menuPermissionDTO)) {
                        deleteMenuList.add(v);
                    } else {
                        Set<Long> menuIdSet = new HashSet<>();
                        menuIdSet.addAll(Optional.ofNullable(v.getMenuIdList()).orElse(Lists.newArrayList()));
                        menuIdSet.addAll(Optional.ofNullable(v.getPartiallyMenuIdList()).orElse(Lists.newArrayList()));

                        menuIdSet.removeAll(Optional.ofNullable(menuPermissionDTO.getMenuIdList()).orElse(Lists.newArrayList()));
                        menuIdSet.removeAll(Optional.ofNullable(menuPermissionDTO.getPartiallyMenuIdList()).orElse(Lists.newArrayList()));

                        MenuPermissionDTO removeId = new MenuPermissionDTO();
                        removeId.setSystemPlatform(menuPermissionDTO.getSystemPlatform());
                        removeId.setMenuId(menuPermissionDTO.getMenuId());
                        removeId.setMenuIdList(new ArrayList<>(menuIdSet));
                        removeId.setPartiallyMenuIdList(new ArrayList<>());
                        deleteMenuList.add(removeId);
                    }
                }
        );

        return deleteMenuList;
    }

    @Override
    public List<SysRoleDTO> listRoleByType(Integer roleType, Integer authScene) {
        SysRoleQuery roleQuery = new SysRoleQuery();
        roleQuery.setRoleType(roleType);
        roleQuery.setAuthScene(authScene);
        roleQuery.setIsDisable(BusinessConstant.N);
        List<SysRoleDO> select = sysRoleDao.select(roleQuery);
        return SysRoleConvert.convertSysRoleDTO(select);
    }

    @Override
    public List<SysRoleDTO> getUserDefaultRole(String userCode) {
        // 查询员工默认角色
        List<SysRoleDTO> sysRoleDTOList = listRoleByType(RoleTypeEnum.DEFAULT_ROLE.getCode(), RoleAuthSceneEnum.EMPLOYEE.getCode());
        if (CollectionUtils.isEmpty(sysRoleDTOList)) {
            return Lists.newArrayList();
        }
        UserBaseInfoDTO userBaseInfoCache = hrmsPostIntegration.getUserBaseInfoCache(userCode);
        if (Objects.isNull(userBaseInfoCache)) {
            return Lists.newArrayList();
        }
        // 先根据用工类型过滤一波默认角色
        List<SysRoleDTO> userDefaultRoleList = sysRoleDTOList.stream().filter(sysRoleDTO ->
                sysRoleDTO.getEmploymentTypeList().contains(userBaseInfoCache.getEmployeeType())).collect(Collectors.toList());
        // 如果是适用于岗位，再根据常驻国过滤一波角色
        List<SysRoleDTO> postDefaultRoleList = userDefaultRoleList.stream().filter(
                        sysRoleDTO -> RoleApplyTypeEnum.POST.getCode().equals(sysRoleDTO.getApplyType())).filter(
                        sysRoleDTO -> Optional.ofNullable(sysRoleDTO.getRoleCountryList()).orElse(Collections.emptyList()).contains(userBaseInfoCache.getLocationCountry())
                                || Optional.ofNullable(sysRoleDTO.getRoleCountryList()).orElse(Collections.emptyList()).contains(BusinessConstant.DEFAULT_ROLE_COUNTRY))
                .collect(Collectors.toList());
        // 适用于所有员工的默认角色
        List<SysRoleDTO> employeeDefaultRoleList = userDefaultRoleList.stream().filter(
                sysRoleDTO -> RoleApplyTypeEnum.EMPLOYEE.getCode().equals(sysRoleDTO.getApplyType())).collect(Collectors.toList());

        // 剩下的角色，就是生效的员工默认角色
        List<SysRoleDTO> userHasDefaultRoleList = Lists.newArrayList();
        userHasDefaultRoleList.addAll(postDefaultRoleList);
        userHasDefaultRoleList.addAll(employeeDefaultRoleList);
        List<Long> sensitiveIdList = resSystemResourceIntegration.getSensitiveIdList(null);
        if (CollectionUtils.isNotEmpty(sensitiveIdList)) {
            List<Long> sensitiveRoleIds = refactoringPermissionCasbinRuleManage.getRoleIdListByMenuIdList(sensitiveIdList);
            if (CollectionUtils.isNotEmpty(sensitiveRoleIds)) {
                for (SysRoleDTO role : userHasDefaultRoleList) {
                    if (sensitiveRoleIds.contains(role.getId())) {
                        role.setSensitiveLevel(1);
                    } else {
                        role.setSensitiveLevel(0);
                    }
                }
            }
        }
        return userHasDefaultRoleList;
    }

    @Override
    public List<RoleListVO> getUserDefaultSystemRole(String userCode, String systemCode) {
        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(systemCode)) {
            return Collections.emptyList();
        }
        List<SysRoleDTO> sysRoleDTOList = listRoleByType(RoleTypeEnum.DEFAULT_ROLE.getCode(), RoleAuthSceneEnum.EMPLOYEE.getCode());
        if (CollectionUtils.isEmpty(sysRoleDTOList)) {
            return Collections.emptyList();
        }
        List<RoleListVO> userDefaultRoleList = sysRoleDTOList.stream()
                .filter(sysRoleDTO -> CollectionUtils.isNotEmpty(sysRoleDTO.getSystemCodeList()) && sysRoleDTO.getSystemCodeList().contains(systemCode))
                .map(
                        e -> {
                            RoleListVO roleListVO = new RoleListVO();
                            roleListVO.setRoleId(e.getId());
                            roleListVO.setEditable(Boolean.FALSE);
                            roleListVO.setRoleType(e.getRoleType());
                            if (RequestInfoHolder.isChinese() || StringUtils.isBlank(e.getRoleNameEn())) {
                                roleListVO.setRoleName(e.getRoleName());
                            } else {
                                roleListVO.setRoleName(e.getRoleNameEn());
                            }
                            return roleListVO;
                        }
                )
                .collect(Collectors.toList());
        return userDefaultRoleList;
    }

    @Override
    public List<RoleListVO> getUserBusinessSystemRole(String userCode, String systemCode) {
        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(systemCode)) {
            return Collections.emptyList();
        }
        List<Long> bindingRoleByUserCode = refactoringPermissionCasbinRuleManage.getRoleIdByUserCode(userCode);
        if (CollectionUtils.isEmpty(bindingRoleByUserCode)) {
            return Collections.emptyList();
        }
        String systemCodeArrStr = String.format("[\"%s\"]", systemCode);
        List<SysRoleDO> list = rolePostManage.listById(bindingRoleByUserCode);
        List<RoleListVO> userBusinessRoleList = list.stream()
                .filter(role -> StringUtils.isNotEmpty(role.getMultipleSystem()) && role.getMultipleSystem().equals(systemCodeArrStr))
                .filter(e -> Objects.nonNull(e.getRoleType()) && e.getRoleType().equals(RoleTypeEnum.BUSINESS_ROLE.getCode()))
                .map(
                        e -> {
                            RoleListVO roleListVO = new RoleListVO();
                            roleListVO.setRoleId(e.getId());
                            roleListVO.setRoleType(e.getRoleType());
                            if (RequestInfoHolder.isChinese() || StringUtils.isBlank(e.getRoleNameEn())) {
                                roleListVO.setRoleName(e.getRoleName());
                            } else {
                                roleListVO.setRoleName(e.getRoleNameEn());
                            }
                            roleListVO.setEditable(Boolean.TRUE);
                            return roleListVO;
                        }
                )
                .collect(Collectors.toList());
        return userBusinessRoleList;
    }

    public DefaultRoleIdDTO getUserDefaultRoleId(UserInfoDTO userInfoDTO) {
        if (Objects.isNull(userInfoDTO)) {
            return DefaultRoleIdDTO.empty();
        }
        List<SysRoleDTO> sysRoleDTOList = listRoleByType(RoleTypeEnum.DEFAULT_ROLE.getCode(), RoleAuthSceneEnum.EMPLOYEE.getCode());
        if (CollectionUtils.isEmpty(sysRoleDTOList)) {
            return DefaultRoleIdDTO.empty();
        }
        String employeeType = Optional.ofNullable(userInfoDTO.getEmployeeType()).orElse("");
        String locationCountry = Optional.ofNullable(userInfoDTO.getLocationCountry()).orElse("");

        // 先根据用工类型过滤一波默认角色
        List<SysRoleDTO> userDefaultRoleList = sysRoleDTOList.stream().filter(sysRoleDTO ->
                StringUtils.isNotBlank(employeeType) && sysRoleDTO.getEmploymentTypeList().contains(employeeType)).collect(Collectors.toList());

        // 员工默认角色
        List<Long> empDefaultRoleList = userDefaultRoleList.stream().filter(
                        sysRoleDTO -> RoleAuthSceneEnum.EMPLOYEE.getCode().equals(sysRoleDTO.getAuthScene()) && RoleApplyTypeEnum.EMPLOYEE.getCode().equals(sysRoleDTO.getApplyType())).map(SysRoleDTO::getId)
                .collect(Collectors.toList());

        // 岗位默认角色，根据国家过滤
        List<Long> postDefaultRoleList = userDefaultRoleList.stream().filter(
                        sysRoleDTO -> RoleApplyTypeEnum.POST.getCode().equals(sysRoleDTO.getApplyType())).filter(
                        sysRoleDTO -> Optional.ofNullable(sysRoleDTO.getRoleCountryList()).orElse(Collections.emptyList()).contains(locationCountry)
                                || Optional.ofNullable(sysRoleDTO.getRoleCountryList()).orElse(Collections.emptyList()).contains(BusinessConstant.DEFAULT_ROLE_COUNTRY))
                .map(SysRoleDTO::getId)
                .collect(Collectors.toList());

        DefaultRoleIdDTO defaultRoleIdDTO = new DefaultRoleIdDTO();
        defaultRoleIdDTO.setEmpRoleIdList(empDefaultRoleList);
        defaultRoleIdDTO.setPostRoleIdList(postDefaultRoleList);
        return defaultRoleIdDTO;
    }
}

package com.imile.permission.manage;

import com.imile.common.page.PaginationResult;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.permission.dto.UserAllPermissionDTO;
import com.imile.permission.domain.role.dto.RoleCacheDTO;
import com.imile.permission.domain.user.param.UserQueryParam;
import com.imile.permission.domain.user.query.UserRoleQuery;
import com.imile.permission.domain.user.vo.PermissionRuleSystemIsolationVO;
import com.imile.permission.domain.user.vo.UserAuthorizationPermissionVO;
import com.imile.permission.domain.user.vo.UserAuthorizationSystemIsolationVO;
import com.imile.permission.domain.user.vo.UserListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/11
 */
public interface UserAuthorizationManage {
    /**
     * 查询分页
     *
     * @param userQueryParam userQueryParam
     * @return PaginationResult<UserListVO>
     */
    PaginationResult<UserListVO> findUserPage(UserQueryParam userQueryParam);

    /**
     * 获取用户直接授权的权限
     *
     * @param userCode userCode
     * @return UserAuthorizationPermissionVO
     */
    UserAuthorizationPermissionVO getUserPermission(String userCode);

    /**
     * 获取用户全部权限（角色、岗位、授权）
     *
     * @param userCode userCode
     * @return UserAuthorizationPermissionVO
     */
    UserAuthorizationPermissionVO getUserImplicitPermissionIgnoreSpecialRoles(String userCode);


    /**
     * 获取用户全部权限（角色、岗位、授权）
     *
     * @param userCode userCode
     * @return UserAuthorizationPermissionVO
     */
    UserAuthorizationPermissionVO getUserImplicitPermission(String userCode);



    /**
     * 获取用户绑定的角色
     *
     * @param userCode userCode
     * @return List<SysRoleDO>
     */
    List<SysRoleDO> getBindingRoleForUser(String userCode);



    /**
     * 绑定数据权限给 用户
     *
     * @param userCode           userCode
     * @param dataPermissionList dataPermissionList
     */
    void bindingDataPermissionForUser(String userCode, List<DataPermissionDTO> dataPermissionList);

    /**
     * 绑定菜单权限给 用户
     *
     * @param userCode           userCode
     * @param menuPermissionList menuPermissionList
     */
    void bindingMenuPermissionForUser(String userCode, List<MenuPermissionDTO> menuPermissionList);

    void savePermission(String userCode, List<MenuPermissionDTO> menuPermissionDTOList, List<DataPermissionDTO> dataPermissionDTOList);

    UserAuthorizationPermissionVO getUserAndRolePermission(UserRoleQuery userRoleQuery);

    UserAuthorizationSystemIsolationVO getPermissionAllSystemIsolation(String userCode);

    PermissionRuleSystemIsolationVO getAllPermissionAndDynamicData(String userCode);

    void recordUserMenu(UserAllPermissionDTO userAllPermissionDTO);

    void recordRoleMenu(List<RoleCacheDTO> roleCacheAndDBByIdList);
}

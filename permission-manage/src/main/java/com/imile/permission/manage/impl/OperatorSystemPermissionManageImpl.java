package com.imile.permission.manage.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemRoleListApiDTO;
import com.imile.permission.convert.RoleCacheConvert;
import com.imile.permission.dao.SysRoleDao;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.entity.RefactoringPermissionCasbinRuleDO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.operatorSystem.OperatorSystemRoleListDTO;
import com.imile.permission.domain.operatorSystem.OperatorSystemRoleQueryDTO;
import com.imile.permission.domain.operatorSystem.OperatorSystemRoleUserDTO;
import com.imile.permission.domain.permission.dto.PermissionDTO;
import com.imile.permission.domain.relation.dto.PostRoleDTO;
import com.imile.permission.domain.role.dto.RoleCacheDTO;
import com.imile.permission.helper.PermissionPageHelper;
import com.imile.permission.manage.OperatorSystemPermissionManage;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.manage.RoleCacheManage;
import com.imile.permission.util.CasbinParseUtil;
import com.imile.permission.util.OrikaUtil;
import com.imile.permission.util.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Service
public class OperatorSystemPermissionManageImpl implements OperatorSystemPermissionManage {

    @Autowired
    private SysRoleDao sysRoleDao;

    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;

    @Autowired
    private RoleCacheManage roleCacheManage;

    @Override
    public void saveRole(SysRoleDO roleDO) {
        if (Objects.isNull(roleDO)) {
            return;
        }
        sysRoleDao.save(roleDO);

    }

    @Override
    public void saveRoleMenu(Long id, List<MenuPermissionDTO> menuPermissionDTOS) {
        if (Objects.isNull(id) || CollectionUtils.isEmpty(menuPermissionDTOS)) {
            return;
        }

        refactoringPermissionCasbinRuleManage.saveRolePermission(id, menuPermissionDTOS, Lists.newArrayList());
        RoleCacheDTO roleCacheDTO = RoleCacheConvert.permissionRoleCacheConvert(id, menuPermissionDTOS, Lists.newArrayList(), null);
        roleCacheManage.addRoleCache(roleCacheDTO);
    }

    @Override
    public void updateRoleById(SysRoleDO roleDO) {
        if (Objects.isNull(roleDO)) {
            return;
        }
        sysRoleDao.updateById(roleDO);
    }

    @Override
    public void updateRoleMenu(Long id, List<MenuPermissionDTO> menuPermissionDTOS) {
        if (Objects.isNull(id)) {
            return;
        }

        refactoringPermissionCasbinRuleManage.deleteRoleMenu(id);
        refactoringPermissionCasbinRuleManage.saveRoleMenu(id, menuPermissionDTOS);
        roleCacheManage.putRoleCache(id);

    }

    @Override
    public PaginationResult<OperatorSystemRoleListDTO> getRolesPaginated(OperatorSystemRoleQueryDTO operatorSystemRoleQuery) {
        PageInfo<SysRoleDO> pageInfo = PermissionPageHelper.startPage(operatorSystemRoleQuery)
                .doSelectPageInfo(() -> sysRoleDao.getRolesPaginated(operatorSystemRoleQuery));
        List<SysRoleDO> list = pageInfo.getList();

        return PageUtil.getPageResult(OrikaUtil.mapAsList(list, OperatorSystemRoleListDTO.class), operatorSystemRoleQuery, pageInfo);
    }

    @Override
    public SysRoleDO getRoleById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return sysRoleDao.getById(id);
    }

    @Override
    public List<MenuPermissionDTO> getMenuPermissionByRoleId(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        PermissionDTO permissionByRoleId = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(id);
        return permissionByRoleId.getMenuPermissionDTOList();
    }

    @Override
    public PaginationResult<String> getUserCodesByRole(OperatorSystemRoleUserDTO role) {
        PageInfo<RefactoringPermissionCasbinRuleDO> pageInfo = PermissionPageHelper.startPage(role)
                .doSelectPageInfo(() -> refactoringPermissionCasbinRuleManage.getUserCodesByRole(role));
        List<RefactoringPermissionCasbinRuleDO> list = pageInfo.getList();
        List<String> userCodeList = list.stream()
                .map(e -> CasbinParseUtil.getRealValue(e.getV0()))
                .collect(Collectors.toList());
        return PageUtil.getPageResult(userCodeList, role, pageInfo);
    }

    @Override
    public List<OperatorSystemRoleListDTO> getRoleByUser(String userCode, Long orgId) {
        if (Objects.isNull(userCode) || Objects.isNull(orgId)) {
            return Collections.emptyList();
        }
        List<Long> roleIdList = refactoringPermissionCasbinRuleManage.getRoleIdByUserCode(userCode);
        List<SysRoleDO> list = sysRoleDao.listByRoleIdAndOrgId(roleIdList, orgId);
        return OrikaUtil.mapAsList(list, OperatorSystemRoleListDTO.class);
    }

    @Override
    public List<OperatorSystemRoleListDTO> getRoleByMenuId(List<Long> menuIdList, Long orgId) {
        if (Objects.isNull(menuIdList) || Objects.isNull(orgId)) {
            return Collections.emptyList();
        }
        List<Long> roleIdList = refactoringPermissionCasbinRuleManage.getRoleIdListByMenuIdList(menuIdList);
        List<SysRoleDO> list = sysRoleDao.listByRoleIdAndOrgId(roleIdList, orgId);
        return OrikaUtil.mapAsList(list, OperatorSystemRoleListDTO.class);
    }

    @Override
    public List<Long> listRoleIdByMenuId(List<Long> menuIdList) {
        if (CollectionUtils.isEmpty(menuIdList)) {
            return Collections.emptyList();
        }
        List<Long> roleIdList = refactoringPermissionCasbinRuleManage.getRoleIdListByMenuIdList(menuIdList);
        return roleIdList;
    }
}

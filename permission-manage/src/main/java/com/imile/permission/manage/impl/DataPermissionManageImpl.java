package com.imile.permission.manage.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.permission.api.dto.RocketMessageApiDTO;
import com.imile.permission.casbin.BlacklistValidator;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.context.RequestInfoHolder;
import com.imile.permission.convert.BasicDataConvert;
import com.imile.permission.convert.DataEqualRightsConvert;
import com.imile.permission.dao.BusinessBasicDataPermissionConfigDAO;
import com.imile.permission.dao.DataEqualRightsDAO;
import com.imile.permission.dao.DataExtensionTagDAO;
import com.imile.permission.dao.DynamicDataDimensionConfigDAO;
import com.imile.permission.dao.PermissionAuthorizationRuleDAO;
import com.imile.permission.dao.PermissionRuleDAO;
import com.imile.permission.dao.PermissionSuperAccountDAO;
import com.imile.permission.dao.PublishedBusinessBasicDataPermissionConfigDAO;
import com.imile.permission.dao.RuleDynamicDataPermissionDAO;
import com.imile.permission.dao.SysDataPermissionDAO;
import com.imile.permission.dao.SysDynamicDataPermissionDAO;
import com.imile.permission.dao.UserDynamicDataPermissionDAO;
import com.imile.permission.domain.dataPermission.dto.DataCodeExtensionTagDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.dto.DimensionSystemTypeCodeDTO;
import com.imile.permission.domain.dataPermission.dto.FlowBaseDataAddDTO;
import com.imile.permission.domain.dataPermission.dto.FlowFunctionDataAddDTO;
import com.imile.permission.domain.dataPermission.dto.FlowMainDataAddDTO;
import com.imile.permission.domain.dataPermission.dto.SystemTypeCodeDTO;
import com.imile.permission.domain.dataPermission.dto.SystemTypeDTO;
import com.imile.permission.domain.dataPermission.dto.UserDynamicDataPermissionMergeTypeDTO;
import com.imile.permission.domain.dataPermission.param.BusinessBasicDataParam;
import com.imile.permission.domain.dataPermission.param.BusinessBasicDataPermissionConfigAddParam;
import com.imile.permission.domain.dataPermission.param.DataCodeInfo;
import com.imile.permission.domain.dataPermission.param.DataPermissionDataAddParam;
import com.imile.permission.domain.dataPermission.param.DataPermissionDeleteParam;
import com.imile.permission.domain.dataPermission.param.DataPermissionTypeAddParam;
import com.imile.permission.domain.dataPermission.param.DataPermissionTypeDataAddParam;
import com.imile.permission.domain.dataPermission.param.DataPermissionUpdateBaseInfoParam;
import com.imile.permission.domain.dataPermission.param.DimensionData;
import com.imile.permission.domain.dataPermission.param.DimensionDataExt;
import com.imile.permission.domain.dataPermission.param.EditMultiDimensionParam;
import com.imile.permission.domain.dataPermission.param.MappingConfigParam;
import com.imile.permission.domain.dataPermission.param.MappingFieldInfoParam;
import com.imile.permission.domain.dataPermission.param.MultiDimensionParam;
import com.imile.permission.domain.dataPermission.param.MultiDimensionTypeCodeParam;
import com.imile.permission.domain.dataPermission.param.MultiDimensionUpdateParam;
import com.imile.permission.domain.dataPermission.param.MultiDimensionVO;
import com.imile.permission.domain.dataPermission.param.SimpleDataItemParam;
import com.imile.permission.domain.dataPermission.param.SingleDimensionParam;
import com.imile.permission.domain.dataPermission.param.SingleDimensionVO;
import com.imile.permission.domain.dataPermission.query.BasicDataQuery;
import com.imile.permission.domain.dataPermission.query.DataEqualRightsQuery;
import com.imile.permission.domain.dataPermission.query.DataPermissionQuery;
import com.imile.permission.domain.dataPermission.query.PublishMainDataQuery;
import com.imile.permission.domain.dataPermission.vo.BusinessBasicDataListVO;
import com.imile.permission.domain.dataPermission.vo.BusinessBasicDataPermissionConfigVO;
import com.imile.permission.domain.dataPermission.vo.BusinessBasicDataTreeVO;
import com.imile.permission.domain.dataPermission.vo.CustomDynamicDataConfigNestedVO;
import com.imile.permission.domain.dataPermission.vo.CustomDynamicDataConfigVO;
import com.imile.permission.domain.dataPermission.vo.DimensionConfigDTO;
import com.imile.permission.domain.dataPermission.vo.DimensionSystemDataPermissionRuleVO;
import com.imile.permission.domain.dataPermission.vo.DynamicDataCodeVO;
import com.imile.permission.domain.dataPermission.vo.ExtensionTagVO;
import com.imile.permission.domain.dataPermission.vo.IdNameVO;
import com.imile.permission.domain.dataPermission.vo.LabelValueVO;
import com.imile.permission.domain.dataPermission.vo.MainDataFieldMappingTreeVO;
import com.imile.permission.domain.dataPermission.vo.PermissionBaseDataVO;
import com.imile.permission.domain.dataPermission.vo.PermissionBaseTypeVO;
import com.imile.permission.domain.dataPermission.vo.PermissionTypeDataVO;
import com.imile.permission.domain.dataPermission.vo.PublishMainDataVO;
import com.imile.permission.domain.dataPermission.vo.RuleDataVO;
import com.imile.permission.domain.dataPermission.vo.SpecificRuleConfigVO;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionRuleVO;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionVO;
import com.imile.permission.domain.entity.BusinessBasicDataPermissionConfigDO;
import com.imile.permission.domain.entity.DataEqualRightsDO;
import com.imile.permission.domain.entity.DataExtensionTagDO;
import com.imile.permission.domain.entity.DynamicDataDimensionConfigDO;
import com.imile.permission.domain.entity.InterfaceFieldMappingDO;
import com.imile.permission.domain.entity.PermissionAuthorizationRuleDO;
import com.imile.permission.domain.entity.PermissionRuleDO;
import com.imile.permission.domain.entity.PermissionSuperAccountDO;
import com.imile.permission.domain.entity.PublishedBusinessBasicDataPermissionConfigDO;
import com.imile.permission.domain.entity.RuleDynamicDataPermissionDO;
import com.imile.permission.domain.entity.SysDataPermissionDO;
import com.imile.permission.domain.entity.SysDynamicDataPermissionDO;
import com.imile.permission.domain.entity.UserDynamicDataPermissionDO;
import com.imile.permission.domain.role.dto.RoleCacheDTO;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.enums.UseCaseTypeEnum;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.helper.AutoAuthorizeUtil;
import com.imile.permission.jcasbin.domain.dto.RefactoringPermissionCasbinRuleDTO;
import com.imile.permission.manage.DataPermissionManage;
import com.imile.permission.manage.EnforcerManage;
import com.imile.permission.manage.InterfaceFieldMappingManage;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.manage.RoleCacheManage;
import com.imile.permission.util.CasbinParseUtil;
import com.imile.permission.util.OrikaUtil;
import com.imile.permission.util.RegexUtil;
import com.imile.permission.util.RestTemplateUtil;
import com.imile.permission.util.UserInfoUtil;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.imile.permission.constants.RedisConstant.MAIN_DATA_TYPE_CODE_PREFIX;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Slf4j
@Service
public class DataPermissionManageImpl implements DataPermissionManage {

    @Autowired
    private SysDataPermissionDAO sysDataPermissionDAO;

    @Autowired
    private SysDynamicDataPermissionDAO sysDynamicDataPermissionDAO;

    @Autowired
    private PermissionAuthorizationRuleDAO permissionAuthorizationRuleDAO;

    @Autowired
    private EnforcerManage enforcerManage;

    @Autowired
    private PermissionSuperAccountDAO permissionSuperAccountDAO;

    @Autowired
    private BusinessBasicDataPermissionConfigDAO businessBasicDataPermissionConfigDAO;

    @Autowired
    private PublishedBusinessBasicDataPermissionConfigDAO publishedBusinessBasicDataPermissionConfigDAO;

    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;

    @Autowired
    private UserDynamicDataPermissionDAO userDynamicDataPermissionDAO;
    @Autowired
    private BlacklistValidator blacklistValidator;

    @Autowired
    private PermissionRuleDAO permissionRuleDAO;

    @Autowired
    private RuleDynamicDataPermissionDAO ruleDynamicDataPermissionDAO;
    @Autowired
    private DataEqualRightsDAO dataEqualRightsDAO;

    @Autowired
    private DataExtensionTagDAO dataExtensionTagDAO;

    @Autowired
    private RoleCacheManage roleCacheManage;

    @Autowired
    private InterfaceFieldMappingManage interfaceFieldMappingManage;

    @Autowired
    private DynamicDataDimensionConfigDAO dynamicDataDimensionConfigDAO;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Value("${rocketmq.producer.auto-authorize.user.topic}")
    private String userAutoAuthorizeTopic;


    @Value("${rocketmq.producer.auto-authorize.role.topic}")
    private String roleAutoAuthorizeTopic;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${select-all.real-auth.typeCode}")
    private List<String> realAuthSelectAllTypeCodes;


    @Override
    public List<PermissionBaseTypeVO> typeList() {
        return sysDataPermissionDAO.typeList(null);
    }

    @Override
    public List<PermissionBaseDataVO> dataList(String typeCode) {
        return OrikaUtil.mapAsList(sysDataPermissionDAO.selectByTypeCode(typeCode, BusinessConstant.N), PermissionBaseDataVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(DataPermissionTypeDataAddParam dataPermissionTypeDataAddParam) {
        String typeCode = dataPermissionTypeDataAddParam.getTypeCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(BusinessConstant.SELECT_ALL_TYPE_CODE.equals(typeCode), PermissionErrorCodeEnums.TYPE_CODE_RESERVED);
        String typeName = dataPermissionTypeDataAddParam.getTypeName();
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeName), PermissionErrorCodeEnums.TYPE_NAME_NOT_NULL);
        // 拿到数据详情信息
        List<SimpleDataItemParam> dataItemList = dataPermissionTypeDataAddParam.getDataItemList();
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(dataItemList), PermissionErrorCodeEnums.DATA_CODE_NOT_NULL);
        // 拿到数据详情的code
        List<String> dataCodeList = dataItemList.stream().map(SimpleDataItemParam::getDataCode).collect(Collectors.toList());
        BusinessLogicException.checkTrue(
                dataCodeList.stream().distinct().count() != dataCodeList.stream().count(),
                PermissionErrorCodeEnums.DATA_CODE_DUPLICATION
        );

        Boolean status = checkTypeCode(typeCode);
        BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.TYPE_CODE_EXIST);

        SysDataPermissionDO dataPermission = OrikaUtil.map(dataPermissionTypeDataAddParam, SysDataPermissionDO.class);
        dataPermission.setIsType(BusinessConstant.Y);
        dataPermission.setSingleSystem(dataPermissionTypeDataAddParam.getSystemCode());
        dataPermission.setDataCode(null);
        String useCaseDescription = dataPermissionTypeDataAddParam.getUseCaseDescription();
        dataPermission.setUseCaseDescription(useCaseDescription);
        sysDataPermissionDAO.save(dataPermission);

        Map<String, String> map = new HashMap<>();

        List<SysDataPermissionDO> recordList = dataItemList.stream()
                .filter(o -> StringUtils.isNotBlank(o.getDataCode()))
                .map(
                        dataItem -> {
                            SysDataPermissionDO sysDataPermission = new SysDataPermissionDO();
                            sysDataPermission.setDataCode(dataItem.getDataCode());
                            sysDataPermission.setTypeCode(typeCode);
                            sysDataPermission.setTypeName(typeName);
                            sysDataPermission.setSingleSystem(dataPermissionTypeDataAddParam.getSystemCode());
                            sysDataPermission.setIsType(BusinessConstant.N);

                            // 把dataItem中的数据描述转换为json格式
                            map.put("descriptionEN", dataItem.getDescriptionEN());
                            map.put("descriptionCN", dataItem.getDescriptionCN());

                            sysDataPermission.setJsonDescription(JSON.toJSONString(map));
                            return sysDataPermission;
                        }
                )
                .collect(Collectors.toList());

        sysDataPermissionDAO.saveBatch(recordList);
    }


    @Override
    public void addType(DataPermissionTypeAddParam dataPermissionDataAddParam) {
        String typeCode = dataPermissionDataAddParam.getTypeCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(BusinessConstant.SELECT_ALL_TYPE_CODE.equals(typeCode), PermissionErrorCodeEnums.TYPE_CODE_RESERVED);
        String typeName = dataPermissionDataAddParam.getTypeName();
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeName), PermissionErrorCodeEnums.TYPE_NAME_NOT_NULL);
        List<SysDataPermissionDO> sysDataPermissionList = sysDataPermissionDAO.selectByTypeCode(typeCode, BusinessConstant.N);
        BusinessLogicException.checkTrue(CollectionUtils.isNotEmpty(sysDataPermissionList), PermissionErrorCodeEnums.TYPE_CODE_EXIST);
        SysDataPermissionDO dataPermission = OrikaUtil.map(dataPermissionDataAddParam, SysDataPermissionDO.class);
        dataPermission.setIsType(BusinessConstant.Y);
        sysDataPermissionDAO.save(dataPermission);
    }

    @Override
    public void addData(DataPermissionDataAddParam dataPermissionDataAddParam) {
        String typeCode = dataPermissionDataAddParam.getTypeCode();/*  */
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        List<String> dataCodeList = dataPermissionDataAddParam.getDataCode();
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(dataCodeList), PermissionErrorCodeEnums.DATA_CODE_NOT_NULL);
        List<SysDataPermissionDO> sysDataPermissionList = sysDataPermissionDAO.selectByTypeCode(typeCode, BusinessConstant.Y);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(sysDataPermissionList), PermissionErrorCodeEnums.TYPE_CODE_NOT_EXIST);
        BusinessLogicException.checkTrue(
                dataCodeList.stream().distinct().count() != dataCodeList.stream().count(),
                PermissionErrorCodeEnums.DATA_CODE_DUPLICATION
        );

        // 删除数据权限
        List<SysDataPermissionDO> oldDataCodeList = sysDataPermissionDAO.selectByTypeCode(typeCode, BusinessConstant.N);
        Map<String, SysDataPermissionDO> dataCodeToDOMap = oldDataCodeList.stream().collect(Collectors.toMap(SysDataPermissionDO::getDataCode, Function.identity()));
        Set<String> oldDataCodeSet = dataCodeToDOMap.keySet();
        Collection<String> deleteDataCodeList = CollectionUtils.removeAll(oldDataCodeSet, dataCodeList);
        for (String del : deleteDataCodeList) {
            Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeDataCodeExist(typeCode, del);
            BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
        }
        if (CollectionUtils.isNotEmpty(deleteDataCodeList)) {
            sysDataPermissionDAO.delete(typeCode, null, deleteDataCodeList);
            enforcerManage.deleteDataPermission(typeCode, null, deleteDataCodeList);
        }

        // 新增数据权限
        Collection<String> newDataCodeList = CollectionUtils.removeAll(dataCodeList, oldDataCodeSet);
        SysDataPermissionDO dataPermission = sysDataPermissionList.get(BusinessConstant.ZERO);
        List<SysDataPermissionDO> recordList = newDataCodeList.stream()
                .filter(StringUtils::isNotBlank)
                .map(
                        dataCode -> {
                            SysDataPermissionDO sysDataPermission = new SysDataPermissionDO();
                            sysDataPermission.setDataCode(dataCode);
                            sysDataPermission.setTypeCode(typeCode);
                            sysDataPermission.setTypeName(dataPermission.getTypeName());
                            sysDataPermission.setSingleSystem(dataPermissionDataAddParam.getSystemCode());
                            sysDataPermission.setIsType(BusinessConstant.N);
                            return sysDataPermission;
                        }
                )
                .collect(Collectors.toList());
        sysDataPermissionDAO.saveBatch(recordList);
    }

    @Override
    public List<PermissionTypeDataVO> getTree(String key) {
        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();
        String userCode = userInfo.getUserCode();

        List<PermissionSuperAccountDO> superAccountList = permissionSuperAccountDAO.getByUserCode(userCode);
        if (CollectionUtils.isNotEmpty(superAccountList)) {
            return allType(key);
        }

        Map<String, List<String>> dataPermissionDTOMap = enforcerManage.getAllTypeDataPermissionByUserCode(userCode);
        if (MapUtils.isEmpty(dataPermissionDTOMap)) {
            return Collections.emptyList();
        }
        List<PermissionTypeDataVO> result = new ArrayList<>();

        Map<String, SysDataPermissionDO> typeCodoToDoMap = sysDataPermissionDAO.getTypeDataMapByTypeCode(dataPermissionDTOMap.keySet(), key);
        dataPermissionDTOMap.forEach(
                (k, v) -> {
                    PermissionTypeDataVO permissionTypeDataVO = new PermissionTypeDataVO();
                    permissionTypeDataVO.setTypeCode(k);
                    if (typeCodoToDoMap.containsKey(k)) {
                        permissionTypeDataVO.setTypeName(typeCodoToDoMap.get(k).getTypeName());
                        permissionTypeDataVO.setDataCode(v);
                        result.add(permissionTypeDataVO);
                    }
                }
        );
        return result;
    }

    @Override
    public List<PermissionTypeDataVO> allType(String key) {
        List<SysDataPermissionDO> list = sysDataPermissionDAO.selectType(key);
        return buildPermissionDataVO(list);
    }

    private List<PermissionTypeDataVO> buildPermissionDataVO(List<SysDataPermissionDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        Set<String> typeCodeSet = list.stream().map(SysDataPermissionDO::getTypeCode).collect(Collectors.toSet());
        Map<String, SysDataPermissionDO> typeDataMapByTypeCode = sysDataPermissionDAO.getTypeDataMapByTypeCode(typeCodeSet, "");
        Map<String, List<SysDataPermissionDO>> typeCodeToDOList = list.stream()
                .collect(Collectors.groupingBy(SysDataPermissionDO::getTypeCode));
        List<PermissionTypeDataVO> permissionTypeDataList = new ArrayList<>();

        typeCodeToDOList.forEach(
                (k, v) -> {
                    PermissionTypeDataVO permissionTypeDataVO = new PermissionTypeDataVO();
                    permissionTypeDataVO.setTypeCode(v.get(BusinessConstant.ZERO).getTypeCode());
                    permissionTypeDataVO.setTypeName(v.get(BusinessConstant.ZERO).getTypeName());
                    permissionTypeDataVO.setSystemCode(v.get(BusinessConstant.ZERO).getSingleSystem());
                    permissionTypeDataVO.setCreateDate(v.get(0).getCreateDate());
                    List<LabelValueVO> dataCodeList = v.stream().map(
                                    e -> {
                                        LabelValueVO labelValueVO = new LabelValueVO();
                                        labelValueVO.setLabel(e.getDataCode());
                                        labelValueVO.setValue(e.getDataCode());
                                        if (StringUtils.isNotBlank(e.getJsonDescription())) {
                                            Map<String, String> map = JSON.parseObject(e.getJsonDescription(), new TypeReference<Map<String, String>>() {
                                            });
                                            String descCn = map.get("descriptionCN");
                                            String descEn = map.get("descriptionEN");
                                            labelValueVO.setDescription(UserEvnHolder.getLocal().equals(Locale.US) ? descEn : descCn);
                                            labelValueVO.setDescriptionCn(descCn);
                                            labelValueVO.setDescriptionEn(descEn);
                                        }
                                        return labelValueVO;
                                    }
                            )
                            .collect(Collectors.toList());
                    permissionTypeDataVO.setDataCodeList(dataCodeList);
                    SysDataPermissionDO sysDataPermissionDO = typeDataMapByTypeCode.get(permissionTypeDataVO.getTypeCode());
                    if (Objects.nonNull(sysDataPermissionDO)) {
                        permissionTypeDataVO.setUseCaseDescription(sysDataPermissionDO.getUseCaseDescription());
                    }
                    permissionTypeDataList.add(permissionTypeDataVO);
                }
        );

        return permissionTypeDataList;
    }

    @Override
    public List<PermissionTypeDataVO> getDataTree(DataPermissionQuery query) {
        List<SysDataPermissionDO> list = sysDataPermissionDAO.selectDataPermission(query);
        return buildPermissionDataVO(list);
    }

    @Override
    public void delete(DataPermissionDeleteParam dataPermissionDeleteParam) {
        String typeCode = dataPermissionDeleteParam.getTypeCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);

        String dataCode = dataPermissionDeleteParam.getDataCode();
        List<String> dataCodeList = dataPermissionDeleteParam.getDataCodeList();

        List<String> checkDataCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataCodeList)) {
            checkDataCodeList.addAll(dataCodeList);
        }
        if (StringUtils.isNotBlank(dataCode)) {
            checkDataCodeList.add(dataCode);
        }

        if (CollectionUtils.isNotEmpty(checkDataCodeList)) {
            Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeDataCodeListExist(typeCode, checkDataCodeList);
            BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
        } else {
            Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeExist(typeCode);
            BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
        }

        sysDataPermissionDAO.delete(typeCode, dataCode, dataCodeList);
        enforcerManage.deleteDataPermission(dataPermissionDeleteParam.getTypeCode(), dataCode, dataCodeList);
    }

    @Override
    public Long addBusinessBasicDataConfig(BusinessBasicDataPermissionConfigAddParam businessBasicDataPermissionConfigAddParam) {
        BusinessLogicException.checkTrue(Objects.isNull(businessBasicDataPermissionConfigAddParam.getDataStructures()), PermissionErrorCodeEnums.DATA_STRUCTURES_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(businessBasicDataPermissionConfigAddParam.getDataUrl()), PermissionErrorCodeEnums.DATA_URL_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(businessBasicDataPermissionConfigAddParam.getTypeCode()), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(businessBasicDataPermissionConfigAddParam.getTypeName()), PermissionErrorCodeEnums.TYPE_NAME_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(businessBasicDataPermissionConfigAddParam.getUseCaseType()), PermissionErrorCodeEnums.USE_CASE_TYPE_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(businessBasicDataPermissionConfigAddParam.getUseCaseDescription()), PermissionErrorCodeEnums.USE_CASE_DESCRIPTION_NOT_NULL);
        // Q:https/http 正则校验
        BusinessLogicException.checkTrue(!RegexUtil.isValidUrl(businessBasicDataPermissionConfigAddParam.getDataUrl()), PermissionErrorCodeEnums.DATA_URL_FORMAT_ERROR);
        BusinessLogicException.checkTrue(blacklistValidator.isBlacklisted(businessBasicDataPermissionConfigAddParam.getDataUrl()), PermissionErrorCodeEnums.DATA_URL_IS_BLACK_URL);

        String useCaseType = businessBasicDataPermissionConfigAddParam.getUseCaseType();
        Boolean status = checkTypeCode(businessBasicDataPermissionConfigAddParam.getTypeCode());
        BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.TYPE_CODE_EXIST);

        BusinessBasicDataPermissionConfigDO config = OrikaUtil.map(businessBasicDataPermissionConfigAddParam, BusinessBasicDataPermissionConfigDO.class);
        config.setSingleSystem(businessBasicDataPermissionConfigAddParam.getSystemCode());

        // 新增
        if (useCaseType.equals(UseCaseTypeEnum.OWNER.getCode())) {
            // 保存资源发布表
            PublishedBusinessBasicDataPermissionConfigDO publishedBusinessBasicDataPermissionConfigDO = new PublishedBusinessBasicDataPermissionConfigDO();
            publishedBusinessBasicDataPermissionConfigDO.setDataStructures(config.getDataStructures());
            publishedBusinessBasicDataPermissionConfigDO.setDataUrl(config.getDataUrl());
            publishedBusinessBasicDataPermissionConfigDO.setSourceTypeCode(config.getTypeCode());
            publishedBusinessBasicDataPermissionConfigDO.setTypeName(config.getTypeName());
            publishedBusinessBasicDataPermissionConfigDO.setTypeNameEn(config.getTypeNameEn());
            publishedBusinessBasicDataPermissionConfigDO.setSingleSystem(config.getSingleSystem());
            publishedBusinessBasicDataPermissionConfigDO.setLastReferenceVersion(0);
            publishedBusinessBasicDataPermissionConfigDO.setUseCaseDescription(config.getUseCaseDescription());
            publishedBusinessBasicDataPermissionConfigDO.setRequestType(config.getRequestType());
            publishedBusinessBasicDataPermissionConfigDAO.save(publishedBusinessBasicDataPermissionConfigDO);
            // 保存引用表
            config.setSourceTypeCode(config.getTypeCode());
            config.setRefSourceSystem(config.getSingleSystem());
            businessBasicDataPermissionConfigDAO.save(config);

            if (CollectionUtils.isNotEmpty(businessBasicDataPermissionConfigAddParam.getFieldInfos())) {
                MappingConfigParam mappingParam = new MappingConfigParam();
                mappingParam.setSourceTypeCode(publishedBusinessBasicDataPermissionConfigDO.getSourceTypeCode());
                mappingParam.setFieldInfos(businessBasicDataPermissionConfigAddParam.getFieldInfos());
                interfaceFieldMappingManage.editFields(mappingParam);
            }
        }
        // 引用
        else if (useCaseType.equals(UseCaseTypeEnum.REF.getCode())) {
            String refSourceSystem = config.getRefSourceSystem();
            String singleSystem = config.getSingleSystem();
            BusinessLogicException.checkTrue(refSourceSystem.equals(singleSystem), PermissionErrorCodeEnums.SAME_SYSTEM_REFERENCE_ERROR);

            String sourceTypeCode = config.getSourceTypeCode();
            BusinessLogicException.checkTrue(StringUtils.isBlank(sourceTypeCode), PermissionErrorCodeEnums.SOURCE_TYPE_CODE_NOT_NULL);
            PublishedBusinessBasicDataPermissionConfigDO publishedBusinessBasicDataPermissionConfig = publishedBusinessBasicDataPermissionConfigDAO.getByTypeCode(businessBasicDataPermissionConfigAddParam.getSourceTypeCode());
            BusinessLogicException.checkTrue(Objects.isNull(publishedBusinessBasicDataPermissionConfig), PermissionErrorCodeEnums.TYPE_CODE_NOT_EXIST);
            Integer lastReferenceVersion = Optional.ofNullable(publishedBusinessBasicDataPermissionConfig.getLastReferenceVersion()).orElse(0);
            lastReferenceVersion = lastReferenceVersion + 1;
            publishedBusinessBasicDataPermissionConfig.setLastReferenceVersion(lastReferenceVersion);
            publishedBusinessBasicDataPermissionConfigDAO.updateById(publishedBusinessBasicDataPermissionConfig);
            businessBasicDataPermissionConfigDAO.save(config);
        }

        return config.getId();
    }

    @Override
    public void deleteBusinessBasicDataConfig(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);
        BusinessBasicDataPermissionConfigDO config = businessBasicDataPermissionConfigDAO.getById(id);
        BusinessLogicException.checkTrue(Objects.isNull(config), PermissionErrorCodeEnums.BUSINESS_BASIC_DATA_CONFIG_NOT_EXIST);
        String sourceTypeCode = config.getSourceTypeCode();
        String useCaseType = config.getUseCaseType();
        // 创建类型检查是有引用数据
        if (useCaseType.equals(UseCaseTypeEnum.OWNER.getCode())) {
            List<BusinessBasicDataPermissionConfigDO> list = businessBasicDataPermissionConfigDAO.listRefTypeCode(sourceTypeCode);
            BusinessLogicException.checkTrue(CollectionUtils.isNotEmpty(list), PermissionErrorCodeEnums.DATA_BEING_REFERENCED);
            List<DynamicDataDimensionConfigDO> dynamicDataDimensionConfigDOS = dynamicDataDimensionConfigDAO.listByMultiDimensionRefMainDataCodeLike(sourceTypeCode);
            BusinessLogicException.checkTrue(CollectionUtils.isNotEmpty(dynamicDataDimensionConfigDOS), PermissionErrorCodeEnums.DATA_BEING_REFERENCED);
        }

        Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeExist(config.getTypeCode());
        Boolean statusDynamic = refactoringPermissionCasbinRuleManage.checkTypeCodeExist("$" + config.getTypeCode());
        BusinessLogicException.checkTrue(status || statusDynamic, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
        config.setIsDelete(BusinessConstant.Y);
        businessBasicDataPermissionConfigDAO.updateDO(config);
        // 创建类型需要同步删除资源表数据
        if (useCaseType.equals(UseCaseTypeEnum.OWNER.getCode())) {
            publishedBusinessBasicDataPermissionConfigDAO.deleteByTypeCode(config.getTypeCode());
            removeRuleByTypeCode(config.getTypeCode());
            deleteDynamicType(config.getTypeCode());
        }
    }

    @Override
    public List<BusinessBasicDataPermissionConfigVO> listBusinessBasicDataConfig(String key) {
        List<BusinessBasicDataPermissionConfigDO> list = businessBasicDataPermissionConfigDAO.listNotDelete(key);
        return OrikaUtil.mapAsList(list, BusinessBasicDataPermissionConfigVO.class);
    }

    @Override
    public List<BusinessBasicDataPermissionConfigVO> listBasicDataConfig(BasicDataQuery query) {
        List<BusinessBasicDataPermissionConfigDO> list = businessBasicDataPermissionConfigDAO.selectBasicData(query);
        // 将数据权限配置转换为VO
        List<BusinessBasicDataPermissionConfigVO> basicDataVOList = BasicDataConvert.convertToBasicDataVO(list);
        List<String> typcCodeList = basicDataVOList.stream().map(BusinessBasicDataPermissionConfigVO::getTypeCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> sourceTypeCode = basicDataVOList.stream().map(BusinessBasicDataPermissionConfigVO::getSourceTypeCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, BusinessBasicDataPermissionConfigVO> map = basicDataVOList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getTypeCode()))
                .collect(Collectors.toMap(BusinessBasicDataPermissionConfigVO::getTypeCode, Function.identity(), (v1, v2) -> v1));

        if (CollectionUtils.isNotEmpty(sourceTypeCode)) {
            List<SysDynamicDataPermissionDO> dynamicDataCodeList = sysDynamicDataPermissionDAO.listDataByTypeCodeList(sourceTypeCode);

            Map<String, List<DynamicDataCodeVO>> dynamicDataCodeVOMap = dynamicDataCodeList.stream()
                    .map(
                            e -> {
                                DynamicDataCodeVO vo = new DynamicDataCodeVO();
                                vo.setTypeCode(e.getTypeCode());
                                vo.setDataCode(e.getDataCode());
                                vo.setDataUrl(e.getDataUrl());
                                vo.setOptionalParameter(e.getOptionalParameter());
                                vo.setDescription(e.getDescription());
                                return vo;
                            }
                    )
                    .collect(Collectors.groupingBy(DynamicDataCodeVO::getTypeCode));

            List<PermissionRuleDO> ruleDataList = permissionRuleDAO.listByTypeCodeList(sourceTypeCode);
            Map<String, List<RuleDataVO>> ruleDataVOMap = ruleDataList.stream()
                    .map(
                            e -> {
                                RuleDataVO vo = new RuleDataVO();
                                vo.setRuleName(e.getRuleName());
                                vo.setRuleCode(e.getRuleCode());
                                vo.setRuleType(e.getRuleType());
                                vo.setRelationTypeCode(e.getRelationTypeCode());
                                return vo;
                            }
                    )
                    .collect(Collectors.groupingBy(RuleDataVO::getRelationTypeCode));

            Map<String, Set<String>> relatedSystemMap = businessBasicDataPermissionConfigDAO.listByRefSourceTypeCode(typcCodeList).
                    stream()
                    .collect(Collectors.groupingBy(BusinessBasicDataPermissionConfigDO::getSourceTypeCode, Collectors.mapping(BusinessBasicDataPermissionConfigDO::getSingleSystem, Collectors.toSet())));

            List<DataExtensionTagDO> dataExtensionTagDOList = dataExtensionTagDAO.listByTypeCodeList(typcCodeList);
            Map<String, List<ExtensionTagVO>> dataExtensionTagVOListMap = dataExtensionTagDOList.stream()
                    .map(
                            e -> {
                                ExtensionTagVO vo = new ExtensionTagVO();
                                vo.setTypeCode(e.getTypeCode());
                                vo.setExtensionTagCode(e.getExtensionTagCode());
                                vo.setExtensionTagName(e.getExtensionTagName());
                                return vo;
                            }
                    )
                    .collect(Collectors.groupingBy(ExtensionTagVO::getTypeCode));

            for (String tc : typcCodeList) {
                BusinessBasicDataPermissionConfigVO businessBasicDataPermissionConfigVO = map.get(tc);
                if (Objects.nonNull(businessBasicDataPermissionConfigVO)) {
                    businessBasicDataPermissionConfigVO.setDynamicDataCodeList(dynamicDataCodeVOMap.getOrDefault(businessBasicDataPermissionConfigVO.getSourceTypeCode(), Lists.newArrayList()));
                    businessBasicDataPermissionConfigVO.setRuleDataList(ruleDataVOMap.getOrDefault(businessBasicDataPermissionConfigVO.getSourceTypeCode(), Lists.newArrayList()));
                    Set<String> relatedSystemSet = relatedSystemMap.getOrDefault(tc, Collections.emptySet());
                    businessBasicDataPermissionConfigVO.setRelatedSystemList(Lists.newArrayList(relatedSystemSet));
                    businessBasicDataPermissionConfigVO.setExtensionTagList(dataExtensionTagVOListMap.getOrDefault(businessBasicDataPermissionConfigVO.getSourceTypeCode(), Lists.newArrayList()));
                }
            }

        }
        return basicDataVOList;
    }

    @Override
    public List<BusinessBasicDataTreeVO> treeBusinessBasicData(BusinessBasicDataParam businessBasicDataParam) {
        String dataUrl = businessBasicDataParam.getDataUrl();
        boolean blacklisted = blacklistValidator.isBlacklisted(dataUrl);
        if (blacklisted) {
            return Lists.newArrayList();
        }
        String uToken = UserInfoUtil.getUToken();
        List<BusinessBasicDataTreeVO> result = new ArrayList<>();
        // 增加平行节点列表
        List<BusinessBasicDataTreeVO> list = RestTemplateUtil.exeGet(dataUrl, uToken, BusinessBasicDataTreeVO.class);
        if (!isFilterDisabled(businessBasicDataParam)) {
            return list;
        }
        if (CollectionUtils.isNotEmpty(list)) {
            for (BusinessBasicDataTreeVO basicDataTreeVO : list) {
                BusinessBasicDataTreeVO.removeDisable(basicDataTreeVO);
            }
            result.addAll(list);
        }
        return result;
    }


    @Override
    public BusinessBasicDataListVO listBusinessBasicData(BusinessBasicDataParam businessBasicDataParam) {
        String uToken = UserInfoUtil.getUToken();
        List<IdNameVO> result = RestTemplateUtil.exeGet(businessBasicDataParam.getDataUrl(), uToken, IdNameVO.class);
        if (CollectionUtils.isNotEmpty(result)) {
            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
            result = result.stream().filter(e -> {
                if (isFilterDisabled(businessBasicDataParam) && StringUtils.isNotBlank(e.getStatus()) && "DISABLED".equals(e.getStatus())) {
                    return false;
                }
                return Objects.isNull(objectObjectHashMap.putIfAbsent(e.getId(), e.getName()));
            }).collect(Collectors.toList());
        }
        BusinessBasicDataListVO businessBasicDataListVO = new BusinessBasicDataListVO();
        businessBasicDataListVO.setTypeCode(businessBasicDataParam.getTypeCode());
        businessBasicDataListVO.setTypeName(businessBasicDataParam.getTypeName());
        businessBasicDataListVO.setIdNameVO(result);
        return businessBasicDataListVO;
    }

    private static boolean isFilterDisabled(BusinessBasicDataParam businessBasicDataParam) {
        return Objects.nonNull(businessBasicDataParam.getIsFilterDisabled()) && Objects.equals(businessBasicDataParam.getIsFilterDisabled(), BusinessConstant.Y);
    }

    @Override
    public List<PermissionTypeDataVO> getAllTree(String key) {
        return allType(key);
    }

    @Override
    public List<String> getFilterSystemTypeCode(Collection<String> systemCode, Collection<String> typeCode) {
        if (CollectionUtils.isEmpty(systemCode) || CollectionUtils.isEmpty(typeCode)) {
            return Collections.emptyList();
        }
        List<String> paramTypeCodes = Lists.newArrayList();
        for (String code : typeCode) {
            if (code.contains(">")) {
                paramTypeCodes.add(code.split(">")[0]);
            } else {
                paramTypeCodes.add(code);
            }
        }

        List<String> result = new ArrayList<>();
        List<String> mainTypeCodeList = businessBasicDataPermissionConfigDAO.listFilterSystemTypeCode(systemCode, paramTypeCodes);
        List<String> typeCodeList = sysDataPermissionDAO.listFilterSystemTypeCode(systemCode, paramTypeCodes);
        List<DynamicDataDimensionConfigDO> dynamicDimensionTypeCode = dynamicDataDimensionConfigDAO.listFilterSystemTypeCode(systemCode, paramTypeCodes);

        if (CollectionUtils.isNotEmpty(mainTypeCodeList)) {
            result.addAll(mainTypeCodeList);
        }
        if (CollectionUtils.isNotEmpty(typeCodeList)) {
            result.addAll(typeCodeList);
        }
        if(CollectionUtils.isNotEmpty(dynamicDimensionTypeCode)) {
            for (DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO : dynamicDimensionTypeCode) {
                Integer dimension = dynamicDataDimensionConfigDO.getDimension();
                if (Objects.isNull(dimension)) {
                    continue;
                }
                if (dimension.equals(0)) {
                    result.add(dynamicDataDimensionConfigDO.getTypeCode());
                } else if (dimension.equals(1)) {
                    String multiDimensionConfig = dynamicDataDimensionConfigDO.getMultiDimensionConfig();
                    if (StringUtils.isBlank(multiDimensionConfig) || multiDimensionConfig.equals("[]")) {
                        continue;
                    }
                    List<DimensionData> dimensionDataList = JSONArray.parseArray(multiDimensionConfig, DimensionData.class);
                    for (DimensionData dimensionData : dimensionDataList) {
                        String dimensionTypeCode = dimensionData.getDimensionTypeCode();
                        if (StringUtils.isBlank(dimensionTypeCode)) {
                            continue;
                        }
                        String nestedTypeCode = dynamicDataDimensionConfigDO.getTypeCode() + ">" + dimensionTypeCode;
                        if (typeCode.contains(nestedTypeCode)) {
                            result.add(nestedTypeCode);
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<SystemTypeCodeDTO> getBaseDataSystemTypeCode(Collection<String> typeCode) {
        if (CollectionUtils.isEmpty(typeCode)) {
            return Collections.emptyList();
        }
        List<SystemTypeCodeDTO> result = sysDataPermissionDAO.getBaseDataSystemTypeCode(typeCode);
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public List<SystemTypeCodeDTO> getMainDataSystemTypeCode(Collection<String> typeCode) {
        if (CollectionUtils.isEmpty(typeCode)) {
            return Collections.emptyList();
        }
        List<SystemTypeCodeDTO> result = businessBasicDataPermissionConfigDAO.getMainDataSystemTypeCode(typeCode);
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public List<SystemTypeCodeDTO> getDynamicDataSystemTypeCode(Collection<String> typeCode) {
        if (CollectionUtils.isEmpty(typeCode)) {
            return Collections.emptyList();
        }
        List<SystemTypeCodeDTO> result = sysDynamicDataPermissionDAO.getDynamicDataSystemTypeCode(typeCode);
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public void buildSystemDataPermissionDTO(List<SystemTypeCodeDTO> baseList, Map<String, DataPermissionDTO> map, List<SystemDataPermissionVO> baseDataPermissionDTO) {
        Map<String, List<SystemTypeCodeDTO>> systemCodeMap = baseList.stream().collect(Collectors.groupingBy(SystemTypeCodeDTO::getSystemCode));
        for (Map.Entry<String, List<SystemTypeCodeDTO>> entry : systemCodeMap.entrySet()) {
            SystemDataPermissionVO vo = new SystemDataPermissionVO();
            vo.setSystemCode(entry.getKey());
            List<SystemTypeCodeDTO> value = entry.getValue();
            value.forEach(
                    e -> {
                        DataPermissionDTO dataPermissionDTO = map.get(e.getTypeCode());
                        if (Objects.nonNull(dataPermissionDTO)) {
                            vo.getDataPermissionDTOList().add(dataPermissionDTO);
                        }
                    }
            );
            baseDataPermissionDTO.add(vo);
        }
    }


    @Override
    public void processDataPermissionDTO(List<DataPermissionDTO> dataPermissionDTOList, List<SystemDataPermissionVO> baseDataPermissionDTO, List<SystemDataPermissionVO> mainDataPermissionDTO) {
        // 权限数据处理
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            Map<String, DataPermissionDTO> map = dataPermissionDTOList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity()));
            Set<String> typeCode = map.keySet();
            List<SystemTypeCodeDTO> baseList = getBaseDataSystemTypeCode(typeCode);
            List<SystemTypeCodeDTO> mainList = getMainDataSystemTypeCode(typeCode);

            // 基础数据
            if (CollectionUtils.isNotEmpty(baseList)) {
                buildSystemDataPermissionDTO(baseList, map, baseDataPermissionDTO);
            }

            // 主数据
            if (CollectionUtils.isNotEmpty(mainList)) {
                buildSystemDataPermissionDTO(mainList, map, mainDataPermissionDTO);
            }

        }
    }

    @Override
    public void processDataPermissionDTO(List<DataPermissionDTO> dataPermissionDTOList, List<SystemDataPermissionVO> baseDataPermissionDTO, List<SystemDataPermissionVO> mainDataPermissionDTO, List<SystemDataPermissionVO> dynamicDataPermissionDTO) {
        // 权限数据处理
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            Map<String, DataPermissionDTO> map = dataPermissionDTOList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity()));
            Set<String> typeCode = map.keySet();
            typeCode = typeCode.stream()
                    .map(e -> {
                        if (e.startsWith("$")) {
                            return e.replace("$", "");
                        } else {
                            return e;
                        }
                    })
                    .collect(Collectors.toSet());
            List<SystemTypeCodeDTO> baseList = getBaseDataSystemTypeCode(typeCode);
            List<SystemTypeCodeDTO> mainList = getMainDataSystemTypeCode(typeCode);
            List<SystemTypeCodeDTO> dynamicList = getDynamicDataSystemTypeCode(typeCode);

            // 基础数据
            if (CollectionUtils.isNotEmpty(baseList)) {
                buildSystemDataPermissionDTO(baseList, map, baseDataPermissionDTO);
            }

            // 主数据
            if (CollectionUtils.isNotEmpty(mainList)) {
                buildSystemDataPermissionDTO(mainList, map, mainDataPermissionDTO);
            }

            // 动态数据
            if (CollectionUtils.isNotEmpty(dynamicList)) {
                buildSystemDataDynamicPermissionDTO2(dynamicList, map, dynamicDataPermissionDTO);
            }

        }

    }

    private void buildSystemDataDynamicPermissionDTO2(List<SystemTypeCodeDTO> baseList, Map<String, DataPermissionDTO> map, List<SystemDataPermissionVO> baseDataPermissionDTO) {
        Map<String, List<SystemTypeCodeDTO>> systemCodeMap = baseList.stream().collect(Collectors.groupingBy(SystemTypeCodeDTO::getSystemCode));
        for (Map.Entry<String, List<SystemTypeCodeDTO>> entry : systemCodeMap.entrySet()) {
            SystemDataPermissionVO vo = new SystemDataPermissionVO();
            vo.setSystemCode(entry.getKey());
            List<SystemTypeCodeDTO> value = entry.getValue();
            value.forEach(
                    e -> {
                        DataPermissionDTO dataPermissionDTO = map.get("$" + e.getTypeCode());
                        if (Objects.nonNull(dataPermissionDTO)) {
                            dataPermissionDTO.setTypeCode(e.getTypeCode());
                            vo.getDataPermissionDTOList().add(dataPermissionDTO);
                        }
                    }
            );
            baseDataPermissionDTO.add(vo);
        }
    }


    @Override
    public Boolean checkTypeCode(String typeCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        Boolean sysDataPermissionStatus = sysDataPermissionDAO.checkTypeCode(typeCode);
        if (sysDataPermissionStatus) {
            return true;
        }
        Boolean sysDynamicDataPermissionStatus = sysDynamicDataPermissionDAO.checkTypeCode(typeCode);
        if (sysDynamicDataPermissionStatus) {
            return true;
        }
        Boolean businessBasicDataPermissionConfigStatus = businessBasicDataPermissionConfigDAO.checkTypeCode(typeCode);
        if (businessBasicDataPermissionConfigStatus) {
            return true;
        }
        Boolean dynamicDataDimensionConfigStatus = dynamicDataDimensionConfigDAO.checkTypeCode(typeCode);
        if (dynamicDataDimensionConfigStatus) {
            return true;
        }
        return false;
    }

    @Override
    public void batchSaveDynamicData(List<SysDynamicDataPermissionDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        sysDynamicDataPermissionDAO.saveBatch(list);
    }

    @Override
    public List<SysDynamicDataPermissionDO> selectDynamicDataByTypeCode(String typeCode, Integer status) {
        return sysDynamicDataPermissionDAO.selectDynamicDataByTypeCode(typeCode, status);
    }

    @Override
    public void deleteDynamicData(List<Long> removeList) {
        if (CollectionUtils.isEmpty(removeList)) {
            return;
        }
        sysDynamicDataPermissionDAO.deleteBatchIds(removeList);

    }

    @Override
    public List<SysDynamicDataPermissionDO> getDynamicDataTree(DataPermissionQuery query) {
        return sysDynamicDataPermissionDAO.getDynamicDataTree(query);
    }

    @Override
    public List<UserDynamicDataPermissionDO> getByUserCode(String userCode) {
        return userDynamicDataPermissionDAO.getByUserCode(userCode);
    }


    @Override
    public void deleteDynamicType(String typeCode) {
        sysDynamicDataPermissionDAO.deleteDynamicType(typeCode);
    }

    @Override
    public List<UserDynamicDataPermissionMergeTypeDTO> getMergeTypeByUserCode(String userCode) {
        List<UserDynamicDataPermissionDO> list = userDynamicDataPermissionDAO.getByUserCode(userCode);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Map<String, List<UserDynamicDataPermissionDO>> map = list.stream().collect(Collectors.groupingBy(UserDynamicDataPermissionDO::getTypeCode));
        List<UserDynamicDataPermissionMergeTypeDTO> result = new ArrayList<>();

        for (Map.Entry<String, List<UserDynamicDataPermissionDO>> entry : map.entrySet()) {
            UserDynamicDataPermissionMergeTypeDTO dto = new UserDynamicDataPermissionMergeTypeDTO();
            dto.setTypeCode(entry.getKey());
            dto.setUserCode(userCode);
            dto.setSingleSystem(entry.getValue().get(0).getSingleSystem());
            dto.setAssociatedMainDataTypeCode(entry.getValue().get(0).getAssociatedMainDataTypeCode());
            List<String> dataContent = entry.getValue().stream()
                    .filter(e -> StringUtils.isNotBlank(e.getDataContent()))
                    .map(e -> JSON.parseArray(e.getDataContent(), String.class))
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
            dto.setDataContent(dataContent);
            result.add(dto);
        }

        return result;
    }

    @Override
    public List<SysDynamicDataPermissionDO> getDynamicByDataPermissionDTO(List<DataPermissionDTO> dataPermissionDTOList) {
        if (CollectionUtils.isEmpty(dataPermissionDTOList)) {
            return Collections.emptyList();
        }
        return sysDynamicDataPermissionDAO.getDynamicByDataPermissionDTO(dataPermissionDTOList);
    }

    @Override
    public void saveMainData(FlowMainDataAddDTO flowMainDataAddDTO) {

    }

    @Override
    public void saveFunctionData(FlowFunctionDataAddDTO flowFunctionDataAddDTO) {

    }

    @Override
    public void saveBaseData(FlowBaseDataAddDTO flowBaseDataAddDTO) {

    }

    @Override
    public BusinessBasicDataPermissionConfigDO getMainDataTypeCode(String typeCode) {
        return businessBasicDataPermissionConfigDAO.getByTypeCode(typeCode);
    }

    @Override
    public void saveRule(List<PermissionRuleDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        permissionRuleDAO.saveBatch(list);
    }

    @Override
    public void removeRuleByTypeCode(String typeCode) {
        permissionRuleDAO.removeRuleByTypeCode(typeCode);
    }

    @Override
    public List<PermissionRuleDO> listRuleByTypeCodeList(List<String> typeCodeList) {
        if (CollectionUtils.isEmpty(typeCodeList)) {
            return Collections.emptyList();
        }
        return permissionRuleDAO.listByTypeCodeList(typeCodeList);
    }

    @Override
    public SysDynamicDataPermissionDO getDynamicTypeCode(String typeCode) {
        return sysDynamicDataPermissionDAO.getDynamicTypeCode(typeCode);
    }

    @Override
    public void buildUserCodeSystemDataPermissionRuleVO(String userCode, List<DataPermissionDTO> dataPermissionList, List<SystemDataPermissionRuleVO> baseDataPermissionDTO, List<SystemDataPermissionRuleVO> mainDataPermissionDTO, List<SystemDataPermissionRuleVO> dynamicDataPermissionDTO, List<SystemDataPermissionRuleVO> singleDynamicDataConfigValueDTO, List<DimensionSystemDataPermissionRuleVO> multiDynamicDataConfigValueDTO) {
        Set<String> typeCodeSet = new HashSet<>();
        List<PermissionAuthorizationRuleDO> userRuleList = permissionAuthorizationRuleDAO.getUserRuleByUserCode(userCode);
        buildSystemDataPermissionRuleVO(dataPermissionList, baseDataPermissionDTO, mainDataPermissionDTO, dynamicDataPermissionDTO, singleDynamicDataConfigValueDTO, multiDynamicDataConfigValueDTO, userRuleList, typeCodeSet);


    }

    @Override
    public void updateUserDirectRule(String userCode, List<PermissionAuthorizationRuleDO> ruleList) {
        if (StringUtils.isBlank(userCode)) {
            return;
        }
        permissionAuthorizationRuleDAO.deleteUserDirectRule(userCode);
        permissionAuthorizationRuleDAO.saveBatch(ruleList);
    }

    @Override
    public void buildRoleIdSystemDataPermissionRuleVO(Long id, List<DataPermissionDTO> dataPermissionList, List<SystemDataPermissionRuleVO> baseDataPermissionDTO, List<SystemDataPermissionRuleVO> mainDataPermissionDTO, List<SystemDataPermissionRuleVO> dynamicDataPermissionDTO, List<SystemDataPermissionRuleVO> singleDynamicDataConfigValueDTO, List<DimensionSystemDataPermissionRuleVO> multiDynamicDataConfigValueDTO) {
        Set<String> typeCodeSet = new HashSet<>();
        List<PermissionAuthorizationRuleDO> userRuleList = permissionAuthorizationRuleDAO.getRoleRuleByRoleId(id);
        buildSystemDataPermissionRuleVO(dataPermissionList, baseDataPermissionDTO, mainDataPermissionDTO, dynamicDataPermissionDTO, singleDynamicDataConfigValueDTO, multiDynamicDataConfigValueDTO, userRuleList, typeCodeSet);


    }

    private void buildSystemDataPermissionRuleVO(List<DataPermissionDTO> dataPermissionList, List<SystemDataPermissionRuleVO> baseDataPermissionDTO, List<SystemDataPermissionRuleVO> mainDataPermissionDTO, List<SystemDataPermissionRuleVO> dynamicDataPermissionDTO, List<SystemDataPermissionRuleVO> singleDynamicDataConfigValueDTO, List<DimensionSystemDataPermissionRuleVO> multiDynamicDataConfigValueDTO, List<PermissionAuthorizationRuleDO> userRuleList, Set<String> typeCodeSet) {
        Map<String, PermissionAuthorizationRuleDO> ruleMap = userRuleList.stream()
                .collect(Collectors.toMap(
                        PermissionAuthorizationRuleDO::getRelationTypeCode,
                        Function.identity(),
                        (e, e2) -> e
                ));

        if (MapUtils.isNotEmpty(ruleMap)) {
            typeCodeSet.addAll(ruleMap.keySet());
        }

        Map<String, DataPermissionDTO> map = Optional.ofNullable(dataPermissionList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity(), (v1, v2) -> v1));

        if (MapUtils.isNotEmpty(map)) {
            for (String key : map.keySet()) {
                if (key.contains(">")) {
                    String[] split = key.split(">");
                    typeCodeSet.add(split[0]);
                } else {
                    typeCodeSet.add(key);
                }
            }
        }


        if (CollectionUtils.isEmpty(typeCodeSet)) {
            return;
        }
        DataPermissionDTO selectAllDataPermission = map.get(BusinessConstant.SELECT_ALL_TYPE_CODE);
        List<String> selectAllTypeCodes;
        if (selectAllDataPermission != null) {
            selectAllTypeCodes = selectAllDataPermission.getDataCodeList();
        } else {
            selectAllTypeCodes = Lists.newArrayList();
        }

        typeCodeSet.addAll(selectAllTypeCodes);
        List<String> typeCodeList = typeCodeSet.stream()
                .map(e -> e.replace("$", ""))
                .distinct()
                .collect(Collectors.toList());

        // 基础数据部分
        List<SystemTypeCodeDTO> baseList = getBaseDataSystemTypeCode(typeCodeList);
        List<SystemTypeCodeDTO> mainList = getMainDataSystemTypeCode(typeCodeList);
        List<SystemTypeCodeDTO> dynamicList = getDynamicDataSystemTypeCode(typeCodeList);
        List<DimensionSystemTypeCodeDTO> dimensionList = getDimensionSystemTypeCode(typeCodeList);


        if (CollectionUtils.isNotEmpty(baseList)) {
            Map<String, List<SystemTypeCodeDTO>> systemCodeMap = baseList.stream().collect(Collectors.groupingBy(SystemTypeCodeDTO::getSystemCode));
            for (Map.Entry<String, List<SystemTypeCodeDTO>> entry : systemCodeMap.entrySet()) {
                SystemDataPermissionRuleVO vo = new SystemDataPermissionRuleVO();
                vo.setSystemCode(entry.getKey());
                List<SystemTypeCodeDTO> value = entry.getValue();
                value.forEach(
                        e -> {
                            DataPermissionDTO dataPermissionDTO = map.get(e.getTypeCode());
                            if (Objects.nonNull(dataPermissionDTO)) {
                                DataPermissionRuleDTO ruleDTO = new DataPermissionRuleDTO();
                                ruleDTO.setTypeCode(dataPermissionDTO.getTypeCode());
                                ruleDTO.setTypeName(dataPermissionDTO.getTypeName());
                                ruleDTO.setDataCodeList(dataPermissionDTO.getDataCodeList());
                                vo.getDataPermissionDTOList().add(ruleDTO);
                            }
                        }
                );
                baseDataPermissionDTO.add(vo);
            }

        }

        if (CollectionUtils.isNotEmpty(mainList)) {
            Map<String, List<SystemTypeCodeDTO>> systemCodeMap = mainList.stream().collect(Collectors.groupingBy(SystemTypeCodeDTO::getSystemCode));
            for (Map.Entry<String, List<SystemTypeCodeDTO>> entry : systemCodeMap.entrySet()) {
                SystemDataPermissionRuleVO vo = new SystemDataPermissionRuleVO();
                vo.setSystemCode(entry.getKey());
                List<SystemTypeCodeDTO> value = entry.getValue();
                for (SystemTypeCodeDTO e : value) {
                    DataPermissionDTO dataPermissionDTO = map.get(e.getTypeCode());
                    DataPermissionDTO functionDataDTO = map.get("$" + e.getTypeCode());
                    PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = ruleMap.get(e.getTypeCode());
                    if (Objects.nonNull(dataPermissionDTO) || Objects.nonNull(functionDataDTO) || Objects.nonNull(permissionAuthorizationRuleDO)) {
                        DataPermissionRuleDTO ruleDTO = new DataPermissionRuleDTO();
                        ruleDTO.setTypeCode(e.getTypeCode());
                        if (CollectionUtils.isNotEmpty(selectAllTypeCodes) && selectAllTypeCodes.contains(dataPermissionDTO.getTypeCode())) {
                            ruleDTO.setSelectAll(1);
                        }

                        if (Objects.nonNull(dataPermissionDTO)) {
                            ruleDTO.setTypeName(dataPermissionDTO.getTypeName());
                            ruleDTO.setDataCodeList(dataPermissionDTO.getDataCodeList());
                            List<DataCodeExtensionTagDTO> dataTagList = dataPermissionDTO.getDataTagList();
                            if (CollectionUtils.isNotEmpty(dataTagList)) {
                                List<String> unCheckCodes = Lists.newArrayList();
                                List<DataCodeExtensionTagDTO> checkDataCodes = dataTagList.stream().filter(tag -> {
                                    if (CollectionUtils.isNotEmpty(tag.getExtensionTagCodeList()) && tag.getExtensionTagCodeList().contains(BusinessConstant.NOT)) {
                                        unCheckCodes.add(tag.getDataCode());
                                        return false;
                                    }
                                    return true;
                                }).collect(Collectors.toList());
                                ruleDTO.setDataExtensionTagList(checkDataCodes);
                                if (CollectionUtils.isNotEmpty(ruleDTO.getDataCodeList())) {
                                    ruleDTO.getDataCodeList().removeAll(unCheckCodes);
                                }
                            }
                        }
                        if (Objects.nonNull(functionDataDTO)) {
                            ruleDTO.setTypeName(functionDataDTO.getTypeName());
                            ruleDTO.setFunctionCodeList(functionDataDTO.getDataCodeList());
                        }
                        if (Objects.nonNull(permissionAuthorizationRuleDO)) {
                            String authorizationRuleJson = permissionAuthorizationRuleDO.getAuthorizationRuleJson();
                            if (StringUtils.isNotBlank(authorizationRuleJson)) {
                                List<SpecificRuleConfigVO> specificRuleConfigVOS = JSON.parseArray(authorizationRuleJson, SpecificRuleConfigVO.class);
                                ruleDTO.setSpecificRuleConfigList(specificRuleConfigVOS);
                            }
                        }
                        vo.getDataPermissionDTOList().add(ruleDTO);
                    } else if (selectAllTypeCodes.contains(e.getTypeCode())) {
                        DataPermissionRuleDTO dto = new DataPermissionRuleDTO();
                        dto.setTypeCode(e.getTypeCode());
                        dto.setSelectAll(1);
                        dto.setDataExtensionTagList(Lists.newArrayList());
                        dto.setDataCodeList(Lists.newArrayList());
                        vo.getDataPermissionDTOList().add(dto);
                    }
                }
                mainDataPermissionDTO.add(vo);
            }
        }

        if (CollectionUtils.isNotEmpty(dynamicList)) {
            Map<String, List<SystemTypeCodeDTO>> systemCodeMap = dynamicList.stream().collect(Collectors.groupingBy(SystemTypeCodeDTO::getSystemCode));
            for (Map.Entry<String, List<SystemTypeCodeDTO>> entry : systemCodeMap.entrySet()) {
                SystemDataPermissionRuleVO vo = new SystemDataPermissionRuleVO();
                vo.setSystemCode(entry.getKey());
                List<SystemTypeCodeDTO> value = entry.getValue();
                value.forEach(
                        e -> {
                            DataPermissionDTO functionDataDTO = map.get("$" + e.getTypeCode());
                            PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = ruleMap.get(e.getTypeCode());

                            if (Objects.nonNull(functionDataDTO) || Objects.nonNull(permissionAuthorizationRuleDO)) {
                                DataPermissionRuleDTO ruleDTO = new DataPermissionRuleDTO();
                                ruleDTO.setTypeCode(e.getTypeCode());
                                if (Objects.nonNull(functionDataDTO)) {
                                    ruleDTO.setTypeName(functionDataDTO.getTypeName());
                                    ruleDTO.setFunctionCodeList(functionDataDTO.getDataCodeList());
                                }

                                if (Objects.nonNull(permissionAuthorizationRuleDO)) {
                                    String authorizationRuleJson = permissionAuthorizationRuleDO.getAuthorizationRuleJson();
                                    if (StringUtils.isNotBlank(authorizationRuleJson)) {
                                        List<SpecificRuleConfigVO> specificRuleConfigVOS = JSON.parseArray(authorizationRuleJson, SpecificRuleConfigVO.class);
                                        ruleDTO.setSpecificRuleConfigList(specificRuleConfigVOS);
                                    }
                                }
                                vo.getDataPermissionDTOList().add(ruleDTO);
                            }
                        }
                );
                dynamicDataPermissionDTO.add(vo);
            }
        }

        if (CollectionUtils.isNotEmpty(dimensionList)) {
            Map<String, List<DimensionSystemTypeCodeDTO>> systemCodeMap = dimensionList.stream().collect(Collectors.groupingBy(DimensionSystemTypeCodeDTO::getSystemCode));
            for (Map.Entry<String, List<DimensionSystemTypeCodeDTO>> entry : systemCodeMap.entrySet()) {
                SystemDataPermissionRuleVO single = new SystemDataPermissionRuleVO();
                single.setSystemCode(entry.getKey());

                DimensionSystemDataPermissionRuleVO multi = new DimensionSystemDataPermissionRuleVO();
                multi.setSystemCode(entry.getKey());

                List<DimensionSystemTypeCodeDTO> value = entry.getValue();
                for (DimensionSystemTypeCodeDTO e : value) {
                    Integer dimension = e.getDimension();
                    // 单维度
                    if (dimension.equals(0)) {
                        DataPermissionDTO dataPermissionDTO = map.get(e.getTypeCode());
                        DataPermissionDTO functionDataDTO = map.get("$" + e.getTypeCode());
                        PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = ruleMap.get(e.getTypeCode());
                        if (Objects.nonNull(dataPermissionDTO) || Objects.nonNull(functionDataDTO) || Objects.nonNull(permissionAuthorizationRuleDO)) {
                            DataPermissionRuleDTO ruleDTO = new DataPermissionRuleDTO();
                            ruleDTO.setTypeCode(e.getTypeCode());
                            if (CollectionUtils.isNotEmpty(selectAllTypeCodes) && selectAllTypeCodes.contains(dataPermissionDTO.getTypeCode())) {
                                ruleDTO.setSelectAll(1);
                            }

                            if (Objects.nonNull(dataPermissionDTO)) {
                                ruleDTO.setTypeName(dataPermissionDTO.getTypeName());
                                ruleDTO.setDataCodeList(dataPermissionDTO.getDataCodeList());
                                List<DataCodeExtensionTagDTO> dataTagList = dataPermissionDTO.getDataTagList();
                                if (CollectionUtils.isNotEmpty(dataTagList)) {
                                    List<String> unCheckCodes = Lists.newArrayList();
                                    List<DataCodeExtensionTagDTO> checkDataCodes = dataTagList.stream().filter(tag -> {
                                        if (CollectionUtils.isNotEmpty(tag.getExtensionTagCodeList()) && tag.getExtensionTagCodeList().contains(BusinessConstant.NOT)) {
                                            unCheckCodes.add(tag.getDataCode());
                                            return false;
                                        }
                                        return true;
                                    }).collect(Collectors.toList());
                                    ruleDTO.setDataExtensionTagList(checkDataCodes);
                                    if (CollectionUtils.isNotEmpty(ruleDTO.getDataCodeList())) {
                                        ruleDTO.getDataCodeList().removeAll(unCheckCodes);
                                    }
                                }
                            }
                            if (Objects.nonNull(functionDataDTO)) {
                                ruleDTO.setTypeName(functionDataDTO.getTypeName());
                                ruleDTO.setFunctionCodeList(functionDataDTO.getDataCodeList());
                            }
                            if (Objects.nonNull(permissionAuthorizationRuleDO)) {
                                String authorizationRuleJson = permissionAuthorizationRuleDO.getAuthorizationRuleJson();
                                if (StringUtils.isNotBlank(authorizationRuleJson)) {
                                    List<SpecificRuleConfigVO> specificRuleConfigVOS = JSON.parseArray(authorizationRuleJson, SpecificRuleConfigVO.class);
                                    ruleDTO.setSpecificRuleConfigList(specificRuleConfigVOS);
                                }
                            }
                            single.getDataPermissionDTOList().add(ruleDTO);
                        } else if (selectAllTypeCodes.contains(e.getTypeCode())) {
                            DataPermissionRuleDTO ruleDTO = new DataPermissionRuleDTO();
                            ruleDTO.setTypeCode(e.getTypeCode());
                            ruleDTO.setSelectAll(1);
                            ruleDTO.setDataExtensionTagList(Lists.newArrayList());
                            ruleDTO.setDataCodeList(Lists.newArrayList());
                            single.getDataPermissionDTOList().add(ruleDTO);
                        }
                    }
                    // 多维度
                    else if (dimension.equals(1)) {
                        List<String> dimensionTypeCodeList = e.getDimensionTypeCodeList();
                        DimensionConfigDTO dimensionConfigDTO = new DimensionConfigDTO();
                        dimensionConfigDTO.setTypeCode(e.getTypeCode());
                        dimensionConfigDTO.setUseCaseDescription(e.getUseCaseDescription());
                        List<DataPermissionRuleDTO> dimensionConfigList = Lists.newArrayList();
                        for (String dimensionTypeCode : dimensionTypeCodeList) {
                            String nestedTypeCode = e.getTypeCode() + ">" + dimensionTypeCode;
                            DataPermissionDTO dataPermissionDTO = map.get(nestedTypeCode);
                            if (Objects.isNull(dataPermissionDTO)) {
                                if (selectAllTypeCodes.contains(nestedTypeCode)) {
                                    DataPermissionRuleDTO ruleDTO = new DataPermissionRuleDTO();
                                    ruleDTO.setTypeCode(dimensionTypeCode);
                                    ruleDTO.setSelectAll(1);
                                    ruleDTO.setDataExtensionTagList(Lists.newArrayList());
                                    ruleDTO.setDataCodeList(Lists.newArrayList());
                                    dimensionConfigList.add(ruleDTO);
                                }
                                continue;
                            }
                            DataPermissionRuleDTO ruleDTO = new DataPermissionRuleDTO();
                            if (CollectionUtils.isNotEmpty(selectAllTypeCodes) && selectAllTypeCodes.contains(nestedTypeCode)) {
                                ruleDTO.setSelectAll(1);
                            }
                            ruleDTO.setTypeCode(dimensionTypeCode);
                            ruleDTO.setTypeName(dimensionTypeCode);
                            ruleDTO.setDataCodeList(dataPermissionDTO.getDataCodeList());
                            List<DataCodeExtensionTagDTO> dataTagList = dataPermissionDTO.getDataTagList();
                            if (CollectionUtils.isNotEmpty(dataTagList)) {
                                List<String> unCheckCodes = Lists.newArrayList();
                                List<DataCodeExtensionTagDTO> checkDataCodes = dataTagList.stream().filter(tag -> {
                                    if (CollectionUtils.isNotEmpty(tag.getExtensionTagCodeList()) && tag.getExtensionTagCodeList().contains(BusinessConstant.NOT)) {
                                        unCheckCodes.add(tag.getDataCode());
                                        return false;
                                    }
                                    return true;
                                }).collect(Collectors.toList());
                                ruleDTO.setDataExtensionTagList(checkDataCodes);
                                if (CollectionUtils.isNotEmpty(ruleDTO.getDataCodeList())) {
                                    ruleDTO.getDataCodeList().removeAll(unCheckCodes);
                                }
                            }
                            dimensionConfigList.add(ruleDTO);
                        }
                        dimensionConfigDTO.setDimensionConfigList(dimensionConfigList);
                        multi.getDimensionDataPermissionDTOList().add(dimensionConfigDTO);

                    }


                }
                singleDynamicDataConfigValueDTO.add(single);
                multiDynamicDataConfigValueDTO.add(multi);


            }
        }
    }

    @Override
    public List<DimensionSystemTypeCodeDTO> getDimensionSystemTypeCode(List<String> typeCodeList) {
        if (CollectionUtils.isEmpty(typeCodeList)) {
            return Collections.emptyList();
        }
        List<DimensionSystemTypeCodeDTO> result = dynamicDataDimensionConfigDAO.getDimensionSystemTypeCode(typeCodeList);
        return result;
    }

    @Override
    public void savePermissionAuthorizationRule(List<PermissionAuthorizationRuleDO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        permissionAuthorizationRuleDAO.saveBatch(ruleList);
    }

    @Override
    public void updateRolePermissionAuthorizationRule(Long id, List<PermissionAuthorizationRuleDO> ruleList) {
        if (Objects.isNull(id)) {
            return;
        }

        permissionAuthorizationRuleDAO.deleteRoleRule(id);
        permissionAuthorizationRuleDAO.saveBatch(ruleList);
    }

    @Override
    public List<String> getSystemTypeCode(String systemCode, List<String> typeCode) {
        if (StringUtils.isBlank(systemCode) || CollectionUtils.isEmpty(typeCode)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        List<String> baseTypeCode = sysDataPermissionDAO.getSystemTypeCode(systemCode, typeCode);
        List<String> mainTypeCode = businessBasicDataPermissionConfigDAO.getSystemTypeCode(systemCode, typeCode);
        List<String> dynamicTypeCode = sysDynamicDataPermissionDAO.getSystemTypeCode(systemCode, typeCode);
        result.addAll(baseTypeCode);
        result.addAll(mainTypeCode);
        result.addAll(dynamicTypeCode);
        return result;
    }

    @Override
    public List<String> getSystemTypeCodeWithDataEquals(String systemCode, List<String> typeCode) {
        if (StringUtils.isBlank(systemCode) || CollectionUtils.isEmpty(typeCode)) {
            return Collections.emptyList();
        }
        // 将动态函数转换成正常的typeCode
        List<String> typeCodeList = CasbinParseUtil.getDynamicTypeCode(typeCode);
        List<String> result = new ArrayList<>();
        List<String> baseTypeCode = sysDataPermissionDAO.getSystemTypeCode(systemCode, typeCodeList);
        List<String> mainTypeCode = businessBasicDataPermissionConfigDAO.getSystemTypeCode(systemCode, typeCodeList);
        List<String> dynamicTypeCode = sysDynamicDataPermissionDAO.getSystemTypeCode(systemCode, typeCodeList);
        // 获取系统同权的typeCode
        DataEqualRightsQuery dataEqualRightsQuery = DataEqualRightsConvert.convertQuery(Lists.newArrayList(systemCode), null);
        List<DataEqualRightsDO> dataEqualRightsList = dataEqualRightsDAO.listDataEqualRightsDO(dataEqualRightsQuery);
        List<String> dataEqualsTypeCodeList = Optional.ofNullable(dataEqualRightsList).orElse(
                Lists.newArrayList()).stream().map(DataEqualRightsDO::getTypeCode).collect(Collectors.toList());
        result.addAll(baseTypeCode);
        result.addAll(mainTypeCode);
        result.addAll(dynamicTypeCode);
        result.addAll(dataEqualsTypeCodeList);
        return result;
    }

    @Override
    public List<RuleDynamicDataPermissionDO> listDynamicDataByUserAndTypeCode(String userCode, List<String> filterTypeCode) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(filterTypeCode)) {
            return Collections.emptyList();
        }
        return ruleDynamicDataPermissionDAO.listDynamicDataByUserAndTypeCode(userCode, filterTypeCode);
    }

    @Override
    public void deletePermissionAuthorizationRuleByUserCode(String userCode) {
        ruleDynamicDataPermissionDAO.deletePermissionAuthorizationRuleByUserCode(userCode);
    }

    @Override
    public List<PermissionAuthorizationRuleDO> getAuthorizationRuleByUser(String userCode) {
        return permissionAuthorizationRuleDAO.getAuthorizationRuleByUser(userCode);
    }

    @Override
    public List<PermissionAuthorizationRuleDO> getAuthorizationRuleByRole(List<Long> roleIdList) {
        return permissionAuthorizationRuleDAO.getAuthorizationRuleByRole(roleIdList);

    }

    @Override
    public void saveRuleDynamicDataPermission(List<RuleDynamicDataPermissionDO> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        ruleDynamicDataPermissionDAO.saveBatch(result);
    }

    @Override
    public List<PermissionRuleDO> getRuleByTypeCode(String typeCode) {
        return permissionRuleDAO.getRuleByTypeCode(typeCode);
    }

    @Override
    public List<String> getMainDataTypeCodeBySystemCode(String systemCode) {
        return businessBasicDataPermissionConfigDAO.getMainDataTypeCodeBySystemCode(systemCode);
    }

    @Override
    public List<RuleDynamicDataPermissionDO> listDynamicDataByUserCode(String userCode) {
        return ruleDynamicDataPermissionDAO.listDynamicDataByUserCode(userCode);
    }

    @Override
    public Map<String, SystemTypeDTO> getMainDataSystem() {
        List<BusinessBasicDataPermissionConfigDO> list = businessBasicDataPermissionConfigDAO.listMainDataSystem();
        Map<String, SystemTypeDTO> map = list.stream().collect(Collectors.toMap(BusinessBasicDataPermissionConfigDO::getTypeCode, e -> new SystemTypeDTO(e.getSingleSystem(), e.getTypeCode(), e.getTypeName())));
        return map;
    }

    @Override
    public Map<String, SystemTypeDTO> getDataSystem() {
        List<BusinessBasicDataPermissionConfigDO> mainList = businessBasicDataPermissionConfigDAO.listMainDataSystem();
        List<SysDataPermissionDO> basicList = sysDataPermissionDAO.selectBasicConfigList();
        List<DynamicDataDimensionConfigDO> dynamicDataDimensionConfigDOS = dynamicDataDimensionConfigDAO.selectTypeList();

        Map<String, SystemTypeDTO> map = mainList.stream().collect(Collectors.toMap(BusinessBasicDataPermissionConfigDO::getTypeCode, e -> new SystemTypeDTO(e.getSingleSystem(), e.getTypeCode(), UserEvnHolder.getLocal().equals(Locale.US) ? e.getTypeNameEn() : e.getTypeName())));
        for (SysDataPermissionDO basicEntity : basicList) {
            map.put(basicEntity.getTypeCode(), new SystemTypeDTO(basicEntity.getSingleSystem(), basicEntity.getTypeCode(), basicEntity.getTypeName()));
        }

        for (DynamicDataDimensionConfigDO dynamicEntity : dynamicDataDimensionConfigDOS) {
            Integer dimension = dynamicEntity.getDimension();
            if (Objects.isNull(dimension)) {
                continue;
            }
            if (dimension.equals(0)) {
                map.put(dynamicEntity.getTypeCode(), new SystemTypeDTO(dynamicEntity.getSingleSystem(), dynamicEntity.getTypeCode(), UserEvnHolder.getLocal().equals(Locale.US) ? dynamicEntity.getTypeNameEn() : dynamicEntity.getTypeName()));
            } else if (dimension.equals(1)) {
                String multiDimensionConfig = dynamicEntity.getMultiDimensionConfig();
                if (StringUtils.isBlank(multiDimensionConfig) || multiDimensionConfig.equals("[]")) {
                    continue;
                }
                List<DimensionData> dimensionDataList = JSONArray.parseArray(multiDimensionConfig, DimensionData.class);
                for (DimensionData dimensionData : dimensionDataList) {
                    String dimensionTypeCode = dimensionData.getDimensionTypeCode();
                    if (StringUtils.isBlank(dimensionTypeCode)) {
                        continue;
                    }

                    map.put(dynamicEntity.getTypeCode() + ">" + dimensionTypeCode, new SystemTypeDTO(dynamicEntity.getSingleSystem(), dynamicEntity.getTypeCode() + ">" + dimensionTypeCode, UserEvnHolder.getLocal().equals(Locale.US) ? (dynamicEntity.getTypeNameEn() + ">" + dimensionData.getDimensionTypeNameEn()) : (dynamicEntity.getTypeName() + ">" + dimensionData.getDimensionTypeName())));
                }
            }
        }

        return map;
    }

    @Override
    public List<PublishMainDataVO> publishMainDataSearch(PublishMainDataQuery publishMainDataQuery) {
        String filterSystemCode = publishMainDataQuery.getFilterSystemCode();

        List<BusinessBasicDataPermissionConfigDO> systemConfigList = businessBasicDataPermissionConfigDAO.selectBySystemCode(filterSystemCode);
        List<String> existTypeCodeList = systemConfigList.stream().map(BusinessBasicDataPermissionConfigDO::getSourceTypeCode).collect(Collectors.toList());
        publishMainDataQuery.setExistTypeCodeList(existTypeCodeList);
        List<PublishedBusinessBasicDataPermissionConfigDO> list = publishedBusinessBasicDataPermissionConfigDAO.selectQuery(publishMainDataQuery);
        List<String> typeCodeList = list.stream().map(PublishedBusinessBasicDataPermissionConfigDO::getSourceTypeCode).collect(Collectors.toList());
        List<SysDynamicDataPermissionDO> dynamicDataCodeList = sysDynamicDataPermissionDAO.listDataByTypeCodeList(typeCodeList);
        Map<String, List<DynamicDataCodeVO>> dynamicDataCodeVOMap = dynamicDataCodeList.stream()
                .map(
                        e -> {
                            DynamicDataCodeVO vo = new DynamicDataCodeVO();
                            vo.setTypeCode(e.getTypeCode());
                            vo.setDataCode(e.getDataCode());
                            vo.setDataUrl(e.getDataUrl());
                            vo.setOptionalParameter(e.getOptionalParameter());
                            vo.setDescription(e.getDescription());
                            return vo;
                        }
                )
                .collect(Collectors.groupingBy(DynamicDataCodeVO::getTypeCode));

        List<PermissionRuleDO> ruleDataList = permissionRuleDAO.listByTypeCodeList(typeCodeList);
        Map<String, List<RuleDataVO>> ruleDataVOMap = ruleDataList.stream()
                .map(
                        e -> {
                            RuleDataVO vo = new RuleDataVO();
                            vo.setRuleName(e.getRuleName());
                            vo.setRuleCode(e.getRuleCode());
                            vo.setRuleType(e.getRuleType());
                            vo.setRelationTypeCode(e.getRelationTypeCode());
                            return vo;
                        }
                )
                .collect(Collectors.groupingBy(RuleDataVO::getRelationTypeCode));

        return list.stream()
                .map(
                        e -> {
                            PublishMainDataVO vo = new PublishMainDataVO();
                            vo.setId(e.getId());
                            vo.setDataStructures(e.getDataStructures());
                            vo.setDataUrl(e.getDataUrl());
                            vo.setRequestType(e.getRequestType());
                            vo.setTypeCode(e.getSourceTypeCode());
                            if (RequestInfoHolder.isChinese() || StringUtils.isEmpty(e.getTypeNameEn())) {
                                vo.setTypeName(e.getTypeName());
                            } else {
                                vo.setTypeName(e.getTypeNameEn());
                            }
                            vo.setTypeNameEn(e.getTypeNameEn());
                            // 引用标识+1
                            int suggestReferenceVersion = e.getLastReferenceVersion() + 1;
                            vo.setSuggestRefTypeCode(e.getSourceTypeCode() + suggestReferenceVersion);
                            vo.setSingleSystem(e.getSingleSystem());
                            // 函数数据处理、数据维度数据处理
                            vo.setDynamicDataCodeList(dynamicDataCodeVOMap.getOrDefault(e.getSourceTypeCode(), Collections.emptyList()));
                            vo.setRuleDataList(ruleDataVOMap.getOrDefault(e.getSourceTypeCode(), Collections.emptyList()));
                            vo.setUseCaseDescription(e.getUseCaseDescription());
                            return vo;
                        }
                )
                .collect(Collectors.toList());

    }

    @Override
    public BusinessBasicDataPermissionConfigDO getBusinessBasicDataPermissionConfigById(Long id) {
        return businessBasicDataPermissionConfigDAO.getById(id);
    }

    @Override
    public void updateBusinessBasicDataPermissionConfigById(BusinessBasicDataPermissionConfigDO businessBasicDataPermissionConfig) {
        businessBasicDataPermissionConfigDAO.updateById(businessBasicDataPermissionConfig);
    }

    @Override
    public List<String> getTypeCodeBySourceTypeCode(String typeCode) {
        return businessBasicDataPermissionConfigDAO.getTypeCodeBySourceTypeCode(typeCode);
    }

    @Override
    public void updateUseCaseDescriptionBySourceTypeCode(String sourceTypeCode, String useCaseDescription) {
        businessBasicDataPermissionConfigDAO.updateUseCaseDescriptionBySourceTypeCode(sourceTypeCode, useCaseDescription);
        publishedBusinessBasicDataPermissionConfigDAO.updateUseCaseDescriptionBySourceTypeCode(sourceTypeCode, useCaseDescription);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBaseInfo(DataPermissionUpdateBaseInfoParam dataPermissionUpdateBaseInfoParam) {
        String typeCode = dataPermissionUpdateBaseInfoParam.getTypeCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        List<SysDataPermissionDO> sysDataPermissionList = sysDataPermissionDAO.selectByTypeCode(typeCode, BusinessConstant.Y);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(sysDataPermissionList), PermissionErrorCodeEnums.TYPE_CODE_NOT_EXIST);

        List<SimpleDataItemParam> simpleDataItemList = Optional.ofNullable(dataPermissionUpdateBaseInfoParam.getDataItemList()).orElse(Lists.newArrayList());
        List<String> dataCodeList = simpleDataItemList.stream().map(SimpleDataItemParam::getDataCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dataCodeList)) {
            BusinessLogicException.checkTrue(
                    dataCodeList.stream().distinct().count() != dataCodeList.stream().count(),
                    PermissionErrorCodeEnums.DATA_CODE_DUPLICATION
            );
        }

        Map<String, SimpleDataItemParam> newDataMap = simpleDataItemList.stream().collect(Collectors.toMap(SimpleDataItemParam::getDataCode, Function.identity(), (v1, v2) -> v1));
        // 删除数据权限
        List<SysDataPermissionDO> oldDataCodeList = sysDataPermissionDAO.selectByTypeCode(typeCode, BusinessConstant.N);
        List<String> oldDataCodeSet = oldDataCodeList.stream().map(SysDataPermissionDO::getDataCode).collect(Collectors.toList());
        Collection<String> deleteDataCodeList = CollectionUtils.removeAll(oldDataCodeSet, dataCodeList);
        for (String del : deleteDataCodeList) {
            newDataMap.remove(del);
            Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeDataCodeExist(typeCode, del);
            BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
        }
        if (CollectionUtils.isNotEmpty(deleteDataCodeList)) {
            sysDataPermissionDAO.delete(typeCode, null, deleteDataCodeList);
            enforcerManage.deleteDataPermission(typeCode, null, deleteDataCodeList);
        }
        // 新增数据权限
        SysDataPermissionDO dataPermission = sysDataPermissionList.get(BusinessConstant.ZERO);
        Map<String, String> map = Maps.newHashMap();

        Collection<String> newDataCodeList = CollectionUtils.removeAll(dataCodeList, oldDataCodeSet);
        List<SysDataPermissionDO> recordList = newDataCodeList.stream()
                .filter(StringUtils::isNotBlank)
                .map(
                        dataCode -> {
                            SysDataPermissionDO sysDataPermission = new SysDataPermissionDO();
                            sysDataPermission.setDataCode(dataCode);
                            sysDataPermission.setTypeCode(typeCode);
                            sysDataPermission.setTypeName(dataPermission.getTypeName());
                            sysDataPermission.setSingleSystem(dataPermission.getSingleSystem());
                            sysDataPermission.setIsType(BusinessConstant.N);
                            // 把dataItem中的数据描述转换为json格式
                            SimpleDataItemParam dataItem = newDataMap.get(dataCode);
                            map.put("descriptionEN", dataItem.getDescriptionEN());
                            map.put("descriptionCN", dataItem.getDescriptionCN());
                            sysDataPermission.setJsonDescription(JSON.toJSONString(map));
                            newDataMap.remove(dataCode);
                            return sysDataPermission;
                        }
                )
                .collect(Collectors.toList());
        sysDataPermissionDAO.saveBatch(recordList);

        // 下面代码是把有可能修改的数据权限的数据描述更新到数据库
        List<SysDataPermissionDO> updateDataItemList = new ArrayList<>();

        Map<String, SysDataPermissionDO> oldDataItemMap = oldDataCodeList.stream().collect(Collectors.toMap(SysDataPermissionDO::getDataCode, Function.identity(), (v1, v2) -> v1));
        newDataMap.forEach(
                (k, v) -> {
                    SysDataPermissionDO sysDataPermissionDO = oldDataItemMap.get(k);
                    if (Objects.isNull(sysDataPermissionDO)) {
                        return;
                    }
                    SysDataPermissionDO sysDataPermission = new SysDataPermissionDO();
                    sysDataPermission.setId(sysDataPermissionDO.getId());
                    sysDataPermission.setDataCode(k);
                    sysDataPermission.setTypeCode(typeCode);
                    sysDataPermission.setTypeName(dataPermission.getTypeName());
                    sysDataPermission.setSingleSystem(dataPermission.getSingleSystem());
                    sysDataPermission.setIsType(BusinessConstant.N);
                    // 把dataItem中的数据描述转换为json格式
                    map.put("descriptionEN", v.getDescriptionEN());
                    map.put("descriptionCN", v.getDescriptionCN());
                    sysDataPermission.setJsonDescription(JSON.toJSONString(map));
                    updateDataItemList.add(sysDataPermission);
                }
        );
        if (CollectionUtils.isNotEmpty(updateDataItemList)) {
            sysDataPermissionDAO.updateBatchById(updateDataItemList);
        }

        String useCaseDescription = Optional.ofNullable(dataPermissionUpdateBaseInfoParam.getUseCaseDescription()).orElse("");
        dataPermission.setUseCaseDescription(useCaseDescription);
        sysDataPermissionDAO.updateById(dataPermission);
    }

    @Override
    public void updateSysDynamicDataPermissionById(SysDynamicDataPermissionDO typeData) {
        sysDynamicDataPermissionDAO.updateById(typeData);
    }


    @Override
    public void saveDataExtensionTag(List<DataExtensionTagDO> saveDataExtensionTagList) {
        if (CollectionUtils.isEmpty(saveDataExtensionTagList)) {
            return;
        }
        dataExtensionTagDAO.saveBatch(saveDataExtensionTagList);
    }

    @Override
    public List<DataExtensionTagDO> getExtensionTagByTypeCode(String typeCode) {
        return dataExtensionTagDAO.getExtensionTagByTypeCode(typeCode);
    }

    @Override
    public void removeExtensionTagByTypeCode(String typeCode) {
        dataExtensionTagDAO.removeExtensionTagByTypeCode(typeCode);
    }

    @Override
    public void updateDataExtensionTag(List<DataExtensionTagDO> updateList) {
        updateList.forEach(entity -> dataExtensionTagDAO.updateById(entity));
    }

    @Override
    public void userAutoAuthorizeChildren(String userCode, List<DataPermissionDTO> dataPermissionList) {
        if (CollectionUtils.isEmpty(dataPermissionList)) {
            return;
        }
        List<String> typeCodes = Lists.newArrayList();
        Map<String, List<String>> typeAutoCodeMap = Maps.newHashMap();
        Map<String, HashSet<String>> typeAllCodeMap = Maps.newHashMap();
        AutoAuthorizeUtil.buildTmpDataFromDataPermission(dataPermissionList, typeCodes, typeAutoCodeMap, typeAllCodeMap);
        Map<String, DataPermissionDTO> typePermissionMap = dataPermissionList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity(), (v1, v2) -> v1));
        List<RefactoringPermissionCasbinRuleDTO> autoAuthorizeEntityList = Lists.newArrayList();
        for (String typeCode : typeCodes) {
            Map<String, String> childrenStrMap = new HashMap<>();
            if (typeCode.contains(">")) {
                String[] split = typeCode.split(">");
                // 子标签
                childrenStrMap = getMainDataParentChildMapAndAddCache(split[1]);
            } else {
                childrenStrMap = getMainDataParentChildMapAndAddCache(typeCode);
            }
            AutoAuthorizeUtil.buildAutoAuthorizeEntityList(typeCode, typeAutoCodeMap, childrenStrMap, typePermissionMap, typeAllCodeMap, CasbinParseUtil.formatUserCode(userCode), autoAuthorizeEntityList);
        }
        if (CollectionUtils.isNotEmpty(autoAuthorizeEntityList)) {
            publisher.publishEvent(
                    RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                            .topic(userAutoAuthorizeTopic)
                            .tag("NEXT_LEVEL")
                            .key(userCode)
                            .data(autoAuthorizeEntityList)
                            .build()
            );
        }
    }

    @Override
    public void userAutoAuthorizeSelectAll(String userCode, List<DataPermissionDTO> dataPermissionDTOList) {
        if (CollectionUtils.isEmpty(dataPermissionDTOList)) {
            return;
        }
        List<RefactoringPermissionCasbinRuleDTO> autoAuthorizeEntityList = Lists.newArrayList();
        Map<String, DataPermissionDTO> dataMap = Optional.of(dataPermissionDTOList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity(), (v1, v2) -> v1));
        Set<String> typeCodeSet = new HashSet<>(dataMap.keySet());
        DataPermissionDTO selectAllDataPermission = dataMap.get(BusinessConstant.SELECT_ALL_TYPE_CODE);

        if (selectAllDataPermission == null) {
            return;
        }
        List<String> selectAllTypeCodes = selectAllDataPermission.getDataCodeList();
        if (CollectionUtils.isEmpty(selectAllTypeCodes)) {
            return;
        }
        for (String typeCode : selectAllTypeCodes) {
            if (!typeCodeSet.contains(typeCode)) {
                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                dataPermissionDTO.setTypeCode(typeCode);
                dataPermissionDTO.setDataCodeList(Lists.newArrayList());
                dataPermissionDTO.setDataTagList(Lists.newArrayList());
                dataPermissionDTO.setSelectAll(1);
                dataPermissionDTOList.add(dataPermissionDTO);
                dataMap.put(typeCode, dataPermissionDTO);
            }
        }
        typeCodeSet.addAll(selectAllTypeCodes);
        List<String> typeCodeList = typeCodeSet.stream()
                .map(e -> {
                    if (e.contains(">")) {
                        String[] split = e.split(">");
                        e = split[0];
                    }
                    return e.replace("$", "");
                })
                .distinct()
                .collect(Collectors.toList());
        List<SystemTypeCodeDTO> mainList = getMainDataSystemTypeCode(typeCodeList);
        List<DimensionSystemTypeCodeDTO> dimensionList = getDimensionSystemTypeCode(typeCodeList);
        List<String> treeStructuresCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(mainList)) {
            mainList.stream().filter(e -> e.getDataStructures() != null && e.getDataStructures() == 1).map(SystemTypeCodeDTO::getTypeCode).forEach(treeStructuresCodes::add);
        }
        if (CollectionUtils.isNotEmpty(dimensionList)) {
            for (DimensionSystemTypeCodeDTO e : dimensionList) {
                // 单维度树型
                if(e.getDimension() != null && e.getDimension() == 0 && e.getDataStructures() != null && e.getDataStructures() == 1) {
                    treeStructuresCodes.add(e.getTypeCode());
                }
                // 多维度才会有dimensionTypeCodeList
                if (CollectionUtils.isNotEmpty(e.getDimensionTreeTypeCodeList())) {
                    for (String dimensionTypeCode : e.getDimensionTreeTypeCodeList()) {
                        String nestedTypeCode = e.getTypeCode() + ">" + dimensionTypeCode;
                        treeStructuresCodes.add(nestedTypeCode);
                    }
                }
            }
        }
        for (String typeCode : typeCodeSet) {
            if (!selectAllTypeCodes.contains(typeCode) || realAuthSelectAllTypeCodes.contains(typeCode)) {
                continue;
            }
            Map<String, String> allMap;
            if (typeCode.contains(">")) {
                String[] split = typeCode.split(">");
                // 子标签
                allMap = getMainDataParentChildMapAndAddCache(split[1]);
            } else {
                allMap = getMainDataParentChildMapAndAddCache(typeCode);
            }
            boolean isTree = CollectionUtils.isNotEmpty(treeStructuresCodes) && treeStructuresCodes.contains(typeCode);
            AutoAuthorizeUtil.buildSelectAllAutoAuthorizeEntityList(typeCode, dataMap, allMap, CasbinParseUtil.formatUserCode(userCode), autoAuthorizeEntityList, dataPermissionDTOList, isTree);
        }
        if (CollectionUtils.isNotEmpty(autoAuthorizeEntityList)) {
            publisher.publishEvent(
                    RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                            .topic(userAutoAuthorizeTopic)
                            .tag("SELECT_ALL")
                            .key(userCode)
                            .data(autoAuthorizeEntityList)
                            .build()
            );
        }
    }


    @Override
    public void roleAutoAuthorizeChildren(Long roleId, List<DataPermissionDTO> dataPermissionList) {
        if (CollectionUtils.isEmpty(dataPermissionList)) {
            return;
        }
        List<String> typeCodes = Lists.newArrayList();
        Map<String, HashSet<String>> typeAllCodeMap = Maps.newHashMap();
        Map<String, List<String>> typeAutoCodeMap = Maps.newHashMap();
        AutoAuthorizeUtil.buildTmpDataFromDataPermission(dataPermissionList, typeCodes, typeAutoCodeMap, typeAllCodeMap);
        Map<String, DataPermissionDTO> typePermissionMap = dataPermissionList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity(), (v1, v2) -> v1));
        List<RefactoringPermissionCasbinRuleDTO> autoAuthorizeEntityList = Lists.newArrayList();
        for (String typeCode : typeCodes) {
            Map<String, String> childrenStrMap = new HashMap<>();
            if (typeCode.contains(">")) {
                String[] split = typeCode.split(">");
                // 子标签
                childrenStrMap = getMainDataParentChildMapAndAddCache(split[1]);
            } else {
                childrenStrMap = getMainDataParentChildMapAndAddCache(typeCode);
            }
            AutoAuthorizeUtil.buildAutoAuthorizeEntityList(typeCode, typeAutoCodeMap, childrenStrMap, typePermissionMap, typeAllCodeMap, CasbinParseUtil.formatR(roleId), autoAuthorizeEntityList);
        }
        if (CollectionUtils.isNotEmpty(autoAuthorizeEntityList)) {
            publisher.publishEvent(
                    RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                            .topic(roleAutoAuthorizeTopic)
                            .tag("NEXT_LEVEL")
                            .key(String.valueOf(roleId))
                            .data(autoAuthorizeEntityList)
                            .build()
            );
        }

    }

    @Override
    public void roleListAutoAuthorizeChildren(List<RoleCacheDTO> roleCacheDTOList) {
        Map<String, Map<String, String>> tempChildrenStrMap = Maps.newHashMap();
        for (RoleCacheDTO roleCacheDTO : roleCacheDTOList) {
            List<DataPermissionDTO> dataPermissionList = roleCacheDTO.getDataPermissionList();
            if (CollectionUtils.isEmpty(dataPermissionList)) {
                continue;
            }
            List<String> typeCodes = Lists.newArrayList();
            Map<String, HashSet<String>> typeAllCodeMap = Maps.newHashMap();
            Map<String, List<String>> typeAutoCodeMap = Maps.newHashMap();
            AutoAuthorizeUtil.buildTmpDataFromDataPermission(dataPermissionList, typeCodes, typeAutoCodeMap, typeAllCodeMap);
            Map<String, DataPermissionDTO> typePermissionMap = dataPermissionList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity(), (v1, v2) -> v1));
            List<RefactoringPermissionCasbinRuleDTO> autoAuthorizeEntityList = Lists.newArrayList();
            for (String typeCode : typeCodes) {
                Map<String, String> childrenStrMap = null;
                if (typeCode.contains(">")) {
                    String[] split = typeCode.split(">");
                    childrenStrMap = tempChildrenStrMap.get(split[1]);
                    if (childrenStrMap == null) {
                        childrenStrMap = getMainDataParentChildMapAndAddCache(split[1]);
                        tempChildrenStrMap.put(split[1], childrenStrMap);
                    }
                } else {
                    childrenStrMap = tempChildrenStrMap.get(typeCode);
                    if (childrenStrMap == null) {
                        childrenStrMap = getMainDataParentChildMapAndAddCache(typeCode);
                        tempChildrenStrMap.put(typeCode, childrenStrMap);
                    }

                }
                AutoAuthorizeUtil.buildAutoAuthorizeEntityList(typeCode, typeAutoCodeMap, childrenStrMap, typePermissionMap, typeAllCodeMap, CasbinParseUtil.formatR(roleCacheDTO.getRoleId()), autoAuthorizeEntityList);
            }
            if (CollectionUtils.isNotEmpty(autoAuthorizeEntityList)) {
                publisher.publishEvent(
                        RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                                .topic(roleAutoAuthorizeTopic)
                                .tag("NEXT_LEVEL")
                                .key(String.valueOf(roleCacheDTO.getRoleId()))
                                .data(autoAuthorizeEntityList)
                                .build()
                );
            }
        }
    }

    @Override
    public void roleAutoAuthorizeSelectAll(Long roleId, List<DataPermissionDTO> dataPermissionDTOList) {
        if (CollectionUtils.isEmpty(dataPermissionDTOList)) {
            return;
        }
        List<RefactoringPermissionCasbinRuleDTO> autoAuthorizeEntityList = Lists.newArrayList();
        Map<String, DataPermissionDTO> dataMap = Optional.of(dataPermissionDTOList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity(), (v1, v2) -> v1));
        Set<String> typeCodeSet = new HashSet<>(dataMap.keySet());
        DataPermissionDTO selectAllDataPermission = dataMap.get(BusinessConstant.SELECT_ALL_TYPE_CODE);
        List<String> selectAllTypeCodes;
        if (selectAllDataPermission != null) {
            selectAllTypeCodes = selectAllDataPermission.getDataCodeList();
        } else {
            selectAllTypeCodes = Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(selectAllTypeCodes)) {
            return;
        }
        for (String typeCode : selectAllTypeCodes) {
            if (!typeCodeSet.contains(typeCode)) {
                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                dataPermissionDTO.setTypeCode(typeCode);
                dataPermissionDTO.setDataCodeList(Lists.newArrayList());
                dataPermissionDTO.setDataTagList(Lists.newArrayList());
                dataPermissionDTO.setSelectAll(1);
                dataPermissionDTOList.add(dataPermissionDTO);
                dataMap.put(typeCode, dataPermissionDTO);
            }
        }
        typeCodeSet.addAll(selectAllTypeCodes);
        List<String> typeCodeList = typeCodeSet.stream()
                .map(e -> {
                    if (e.contains(">")) {
                        String[] split = e.split(">");
                        e = split[0];
                    }
                    return e.replace("$", "");
                })
                .distinct()
                .collect(Collectors.toList());
        List<SystemTypeCodeDTO> mainList = getMainDataSystemTypeCode(typeCodeList);
        List<DimensionSystemTypeCodeDTO> dimensionList = getDimensionSystemTypeCode(typeCodeList);
        List<String> treeStructuresCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(mainList)) {
            mainList.stream().filter(e -> e.getDataStructures() != null && e.getDataStructures() == 1).map(SystemTypeCodeDTO::getTypeCode).forEach(treeStructuresCodes::add);
        }
        if (CollectionUtils.isEmpty(dimensionList)) {
            for (DimensionSystemTypeCodeDTO e : dimensionList) {
                if (CollectionUtils.isNotEmpty(e.getDimensionTreeTypeCodeList())) {
                    for (String dimensionTypeCode : e.getDimensionTreeTypeCodeList()) {
                        String nestedTypeCode = e.getTypeCode() + ">" + dimensionTypeCode;
                        treeStructuresCodes.add(nestedTypeCode);
                    }
                }
            }
        }
        for (String typeCode : typeCodeSet) {
            if (!selectAllTypeCodes.contains(typeCode) || realAuthSelectAllTypeCodes.contains(typeCode)) {
                continue;
            }
            Map<String, String> allMap;
            if (typeCode.contains(">")) {
                String[] split = typeCode.split(">");
                // 子标签
                allMap = getMainDataParentChildMapAndAddCache(split[1]);
            } else {
                allMap = getMainDataParentChildMapAndAddCache(typeCode);
            }
            boolean isTree = CollectionUtils.isNotEmpty(treeStructuresCodes) && treeStructuresCodes.contains(typeCode);
            AutoAuthorizeUtil.buildSelectAllAutoAuthorizeEntityList(typeCode, dataMap, allMap, CasbinParseUtil.formatR(roleId), autoAuthorizeEntityList, dataPermissionDTOList, isTree);
        }
        if (CollectionUtils.isNotEmpty(autoAuthorizeEntityList)) {
            publisher.publishEvent(
                    RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                            .topic(roleAutoAuthorizeTopic)
                            .tag("SELECT_ALL")
                            .key(String.valueOf(roleId))
                            .data(autoAuthorizeEntityList)
                            .build()
            );
        }
    }

    @Override
    public void roleListAutoAuthorizeSelectAll(List<RoleCacheDTO> roleCacheDTOList) {
        Map<String, Map<String, String>> tempChildrenStrMap = Maps.newHashMap();
        for (RoleCacheDTO roleCacheDTO : roleCacheDTOList) {
            List<DataPermissionDTO> dataPermissionList = roleCacheDTO.getDataPermissionList();
            if (CollectionUtils.isEmpty(dataPermissionList)) {
                continue;
            }
            List<RefactoringPermissionCasbinRuleDTO> autoAuthorizeEntityList = Lists.newArrayList();
            Map<String, DataPermissionDTO> dataMap = Optional.of(dataPermissionList)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .collect(Collectors.toMap(DataPermissionDTO::getTypeCode, Function.identity(), (v1, v2) -> v1));
            Set<String> typeCodeSet = new HashSet<>(dataMap.keySet());
            DataPermissionDTO selectAllDataPermission = dataMap.get(BusinessConstant.SELECT_ALL_TYPE_CODE);
            List<String> selectAllTypeCodes;
            if (selectAllDataPermission != null) {
                selectAllTypeCodes = selectAllDataPermission.getDataCodeList();
            } else {
                selectAllTypeCodes = Lists.newArrayList();
            }
            if (CollectionUtils.isEmpty(selectAllTypeCodes)) {
                return;
            }
            for (String typeCode : selectAllTypeCodes) {
                if (!typeCodeSet.contains(typeCode)) {
                    DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                    dataPermissionDTO.setTypeCode(typeCode);
                    dataPermissionDTO.setDataCodeList(Lists.newArrayList());
                    dataPermissionDTO.setDataTagList(Lists.newArrayList());
                    dataPermissionDTO.setSelectAll(1);
                    dataPermissionList.add(dataPermissionDTO);
                    dataMap.put(typeCode, dataPermissionDTO);
                }
            }
            typeCodeSet.addAll(selectAllTypeCodes);
            List<String> typeCodeList = typeCodeSet.stream()
                    .map(e -> {
                        if (e.contains(">")) {
                            String[] split = e.split(">");
                            e = split[0];
                        }
                        return e.replace("$", "");
                    })
                    .distinct()
                    .collect(Collectors.toList());
            List<SystemTypeCodeDTO> mainList = getMainDataSystemTypeCode(typeCodeList);
            List<DimensionSystemTypeCodeDTO> dimensionList = getDimensionSystemTypeCode(typeCodeList);
            List<String> treeStructuresCodes = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(mainList)) {
                mainList.stream().filter(e -> e.getDataStructures() != null && e.getDataStructures() == 1).map(SystemTypeCodeDTO::getTypeCode).forEach(treeStructuresCodes::add);
            }
            if (CollectionUtils.isEmpty(dimensionList)) {
                for (DimensionSystemTypeCodeDTO e : dimensionList) {
                    if (CollectionUtils.isNotEmpty(e.getDimensionTreeTypeCodeList())) {
                        for (String dimensionTypeCode : e.getDimensionTreeTypeCodeList()) {
                            String nestedTypeCode = e.getTypeCode() + ">" + dimensionTypeCode;
                            treeStructuresCodes.add(nestedTypeCode);
                        }
                    }
                }
            }
            for (String typeCode : typeCodeSet) {
                if (!selectAllTypeCodes.contains(typeCode) || realAuthSelectAllTypeCodes.contains(typeCode)) {
                    continue;
                }
                Map<String, String> allMap = null;
                if (typeCode.contains(">")) {
                    String[] split = typeCode.split(">");
                    allMap = tempChildrenStrMap.get(split[1]);
                    if (allMap == null) {
                        allMap = getMainDataParentChildMapAndAddCache(split[1]);
                        tempChildrenStrMap.put(split[1], allMap);
                    }
                } else {
                    allMap = tempChildrenStrMap.get(typeCode);
                    if (allMap == null) {
                        allMap = getMainDataParentChildMapAndAddCache(typeCode);
                        tempChildrenStrMap.put(typeCode, allMap);
                    }

                }
                boolean isTree = CollectionUtils.isNotEmpty(treeStructuresCodes) && treeStructuresCodes.contains(typeCode);
                AutoAuthorizeUtil.buildSelectAllAutoAuthorizeEntityList(typeCode, dataMap, allMap, CasbinParseUtil.formatR(roleCacheDTO.getRoleId()), autoAuthorizeEntityList, dataPermissionList, isTree);
            }
            if (CollectionUtils.isNotEmpty(autoAuthorizeEntityList)) {
                publisher.publishEvent(
                        RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                                .topic(roleAutoAuthorizeTopic)
                                .tag("SELECT_ALL")
                                .key(String.valueOf(roleCacheDTO.getRoleId()))
                                .data(autoAuthorizeEntityList)
                                .build()
                );
            }
        }
    }

    @Override
    public Map<String, String> getMainDataParentChildMapAndAddCache(String typeCode) {
        Map<Object, Object> rawMap = redisTemplate.opsForHash().entries(MAIN_DATA_TYPE_CODE_PREFIX + typeCode);
        Map<String, String> childrenStrMap;
        if (rawMap.isEmpty()) {
            BusinessBasicDataPermissionConfigDO mainDataConfig = businessBasicDataPermissionConfigDAO.getByTypeCode(typeCode);
            childrenStrMap = Maps.newHashMap();
            if (mainDataConfig != null) {
                List<InterfaceFieldMappingDO> fieldMappings = interfaceFieldMappingManage.selectMappingsBySourceTypeCode(mainDataConfig.getSourceTypeCode());
                if (CollectionUtils.isEmpty(fieldMappings)) {
                    return new HashMap<>();
                }
                JSONArray objectForUrl = interfaceFieldMappingManage.getObjectForUrlReturnBlankOnEx(mainDataConfig.getDataUrl(), mainDataConfig.getRequestType(), null);
                List<MainDataFieldMappingTreeVO> mainDataFieldMappingTreeVOS = interfaceFieldMappingManage.getMainDataFieldMappingTreeVOS(objectForUrl, fieldMappings, mainDataConfig.getDataStructures());
                List<BusinessBasicDataTreeVO> list = getBusinessBasicDataTree(mainDataFieldMappingTreeVOS);
                if (CollectionUtils.isNotEmpty(list)) {
                    for (BusinessBasicDataTreeVO vo : list) {
                        processChildStrMap(vo, childrenStrMap);
                    }
                    hmsetWithExpire(MAIN_DATA_TYPE_CODE_PREFIX + typeCode, childrenStrMap, 5 * 60);
                }
            } else {
                DynamicDataDimensionConfigDO dynamicDataDimensionConfigByTypeCode = getDynamicDataDimensionConfigByTypeCode(typeCode);
                if (dynamicDataDimensionConfigByTypeCode != null) {
                    List<InterfaceFieldMappingDO> fieldMappings = interfaceFieldMappingManage.selectMappingsBySourceTypeCode(typeCode);
                    if (CollectionUtils.isEmpty(fieldMappings)) {
                        return new HashMap<>();
                    }
                    String dataUrl = dynamicDataDimensionConfigByTypeCode.getDataUrl();
                    String requestType = dynamicDataDimensionConfigByTypeCode.getRequestType();
                    Integer dataStructures = dynamicDataDimensionConfigByTypeCode.getDataStructures();
                    // dataUrl = dataUrl.replace("permission-svc:80", "localhost:80/permission");
                    JSONArray objectForUrl = interfaceFieldMappingManage.getObjectForUrlReturnBlankOnEx(dataUrl, requestType, null);
                    List<MainDataFieldMappingTreeVO> mainDataFieldMappingTreeVOS = interfaceFieldMappingManage.getMainDataFieldMappingTreeVOS(objectForUrl, fieldMappings, dataStructures);
                    List<BusinessBasicDataTreeVO> list = getBusinessBasicDataTree(mainDataFieldMappingTreeVOS);
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (BusinessBasicDataTreeVO vo : list) {
                            processChildStrMap(vo, childrenStrMap);
                        }
                        hmsetWithExpire(MAIN_DATA_TYPE_CODE_PREFIX + typeCode, childrenStrMap, 5 * 60);
                    }
                }


            }
        } else {
            childrenStrMap = rawMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            entry -> entry.getKey().toString(),
                            entry -> entry.getValue().toString()
                    ));
        }
        return childrenStrMap;
    }

    private List<BusinessBasicDataTreeVO> getBusinessBasicDataTree(List<MainDataFieldMappingTreeVO> mainDataFieldMappingTreeVOS) {
        if (CollectionUtils.isEmpty(mainDataFieldMappingTreeVOS)) {
            return Collections.emptyList();
        }
        List<BusinessBasicDataTreeVO> list = Lists.newArrayList();
        for (MainDataFieldMappingTreeVO mainDataFieldMappingTreeVO : mainDataFieldMappingTreeVOS) {
            BusinessBasicDataTreeVO businessBasicDataTreeVO = new BusinessBasicDataTreeVO();
            Map<String, Object> fields = mainDataFieldMappingTreeVO.getFields();
            Object idO = fields.get("id");
            Object nameO = fields.get("name");
            Object statusO = fields.get("status");
            Object childrenO = fields.get("children");
            if (idO != null) {
                businessBasicDataTreeVO.setId(String.valueOf(idO));
            }
            if (nameO != null) {
                businessBasicDataTreeVO.setName(String.valueOf(nameO));
            }
            if (statusO != null) {
                businessBasicDataTreeVO.setStatus(String.valueOf(statusO));
            }
            if (childrenO instanceof List) {
                businessBasicDataTreeVO.setChildrenList(getBusinessBasicDataTree((List<MainDataFieldMappingTreeVO>) childrenO));
            }
            list.add(businessBasicDataTreeVO);

        }

        return list;
    }


    @Override
    public void saveSingleDynamicDataDimensionConfig(SingleDimensionParam singleDimensionParam) {
        Integer dataStructures = singleDimensionParam.getDataStructures();
        String dataUrl = singleDimensionParam.getDataUrl();
        String requestType = singleDimensionParam.getRequestType();
        String typeCode = singleDimensionParam.getTypeCode();
        String typeName = singleDimensionParam.getTypeName();
        String typeNameEn = singleDimensionParam.getTypeNameEn();
        String systemCode = singleDimensionParam.getSystemCode();
        String useCaseDescription = singleDimensionParam.getUseCaseDescription();
        List<MappingFieldInfoParam> fieldInfos = singleDimensionParam.getFieldInfos();


        BusinessLogicException.checkTrue(Objects.isNull(dataStructures), PermissionErrorCodeEnums.DATA_STRUCTURES_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(dataUrl), PermissionErrorCodeEnums.DATA_URL_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeName), PermissionErrorCodeEnums.TYPE_NAME_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(useCaseDescription), PermissionErrorCodeEnums.USE_CASE_DESCRIPTION_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(requestType), PermissionErrorCodeEnums.REQUEST_TYPE_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeNameEn), PermissionErrorCodeEnums.TYPE_NAME_EN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(systemCode), PermissionErrorCodeEnums.SYSTEM_CODE_NOT_NULL);
        // Q:https/http 正则校验
        BusinessLogicException.checkTrue(!RegexUtil.isValidUrl(dataUrl), PermissionErrorCodeEnums.DATA_URL_FORMAT_ERROR);
        BusinessLogicException.checkTrue(blacklistValidator.isBlacklisted(dataUrl), PermissionErrorCodeEnums.DATA_URL_IS_BLACK_URL);


        // 注意重复范围需要增加新的动态数据系统
        Boolean status = checkTypeCode(typeCode);
        BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.TYPE_CODE_EXIST);

        DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO = new DynamicDataDimensionConfigDO();

        dynamicDataDimensionConfigDO.setDataStructures(dataStructures);
        dynamicDataDimensionConfigDO.setDataUrl(dataUrl);
        dynamicDataDimensionConfigDO.setRequestType(requestType);
        dynamicDataDimensionConfigDO.setTypeCode(typeCode);
        dynamicDataDimensionConfigDO.setTypeName(typeName);
        dynamicDataDimensionConfigDO.setTypeNameEn(typeNameEn);
        dynamicDataDimensionConfigDO.setSingleSystem(systemCode);
        dynamicDataDimensionConfigDO.setUseCaseDescription(useCaseDescription);

        // 单维度数据
        dynamicDataDimensionConfigDO.setDimension(0);

        dynamicDataDimensionConfigDAO.save(dynamicDataDimensionConfigDO);

        MappingConfigParam mappingParam = new MappingConfigParam();
        mappingParam.setSourceTypeCode(typeCode);
        mappingParam.setFieldInfos(fieldInfos);
        interfaceFieldMappingManage.editFields(mappingParam);

    }

    @Override
    public void saveMultiDynamicDataDimensionConfig(MultiDimensionParam multiDimensionParam) {
        BusinessLogicException.checkTrue(Objects.isNull(multiDimensionParam), PermissionErrorCodeEnums.MULTI_DIMENSION_NOT_NULL);

        String systemCode = multiDimensionParam.getSystemCode();
        String typeCode = multiDimensionParam.getTypeCode();
        String typeName = multiDimensionParam.getTypeName();
        String typeNameEn = multiDimensionParam.getTypeNameEn();
        String useCaseDescription = multiDimensionParam.getUseCaseDescription();
        List<DimensionData> dimensionDataList = multiDimensionParam.getDimensionDataList();

        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(BusinessConstant.SELECT_ALL_TYPE_CODE.equals(typeCode), PermissionErrorCodeEnums.TYPE_CODE_RESERVED);
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeName), PermissionErrorCodeEnums.TYPE_NAME_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(useCaseDescription), PermissionErrorCodeEnums.USE_CASE_DESCRIPTION_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeNameEn), PermissionErrorCodeEnums.TYPE_NAME_EN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(systemCode), PermissionErrorCodeEnums.SYSTEM_CODE_NOT_NULL);

        // 注意重复范围需要增加新的动态数据系统
        Boolean status = checkTypeCode(typeCode);
        BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.TYPE_CODE_EXIST);


        DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO = new DynamicDataDimensionConfigDO();

        dynamicDataDimensionConfigDO.setTypeCode(typeCode);
        dynamicDataDimensionConfigDO.setTypeName(typeName);
        dynamicDataDimensionConfigDO.setTypeNameEn(typeNameEn);
        dynamicDataDimensionConfigDO.setSingleSystem(systemCode);
        dynamicDataDimensionConfigDO.setUseCaseDescription(useCaseDescription);
        Set<String> multiDimensionRefMainDataCodeSet = new HashSet<>();
        checkFilterRefMainData(dimensionDataList, multiDimensionRefMainDataCodeSet);

        if (CollectionUtils.isNotEmpty(dimensionDataList)) {
            // 具体的多维度数据信息
            dynamicDataDimensionConfigDO.setMultiDimensionConfig(JSON.toJSONString(dimensionDataList));
        }

        // 多维度数据
        dynamicDataDimensionConfigDO.setDimension(1);
        if (CollectionUtils.isNotEmpty(multiDimensionRefMainDataCodeSet)) {
            List<PublishedBusinessBasicDataPermissionConfigDO> list = publishedBusinessBasicDataPermissionConfigDAO.listByTypeCode(multiDimensionRefMainDataCodeSet);
            BusinessLogicException.checkTrue(list.size() != multiDimensionRefMainDataCodeSet.size(), PermissionErrorCodeEnums.NOT_EXIST);
            // 使用 :: 分割字符
            dynamicDataDimensionConfigDO.setMultiDimensionRefMainDataCode(
                    multiDimensionRefMainDataCodeSet.stream()
                            .collect(Collectors.joining("::", "::", "::"))
            );
        }

        dynamicDataDimensionConfigDAO.save(dynamicDataDimensionConfigDO);


    }

    private Set<String> checkFilterRefMainData(List<DimensionData> dimensionDataList, Set<String> multiDimensionRefMainDataCodeSet) {

        if (CollectionUtils.isNotEmpty(dimensionDataList)) {
            Set<String> allTypeCodeSet = new HashSet<>();
            for (DimensionData dimensionData : dimensionDataList) {
                String dimensionType = dimensionData.getDimensionType();
                String dimensionTypeCode = dimensionData.getDimensionTypeCode();
                String dimensionTypeName = dimensionData.getDimensionTypeName();
                String dimensionTypeNameEn = dimensionData.getDimensionTypeNameEn();
                String dimensionSupportMultiSelect = dimensionData.getDimensionSupportMultiSelect();
                List<DataCodeInfo> dimensionDataCodeInfoList = dimensionData.getDimensionDataCodeInfoList();
                // String configId = dimensionData.getConfigId();
                String dimensionDataUrl = dimensionData.getDimensionDataUrl();
                Integer dataStructures = dimensionData.getDataStructures();

                // 重复校验
                BusinessLogicException.checkTrue(allTypeCodeSet.contains(dimensionTypeCode), PermissionErrorCodeEnums.DIMENSION_TYPE_CODE_DUPLICATION);
                allTypeCodeSet.add(dimensionTypeCode);

                BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionType), PermissionErrorCodeEnums.DIMENSION_TYPE_NOT_NULL);
                BusinessLogicException.checkTrue(BusinessConstant.SELECT_ALL_TYPE_CODE.equals(dimensionTypeCode), PermissionErrorCodeEnums.TYPE_CODE_RESERVED);
                BusinessLogicException.checkTrue(!BusinessConstant.DIMENSION_TYPE_ENUM_LIST.contains(dimensionType), PermissionErrorCodeEnums.DIMENSION_TYPE_ENUM_ERROR);

                // 非空安全校验
                // 枚举、字典
                if (dimensionType.equals("ENUM") || dimensionType.equals("DICT")) {
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionTypeCode), PermissionErrorCodeEnums.DIMENSION_TYPE_CODE_NOT_NULL);
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionTypeName), PermissionErrorCodeEnums.DIMENSION_TYPE_NAME_NOT_NULL);
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionTypeNameEn), PermissionErrorCodeEnums.DIMENSION_TYPE_NAME_EN_NOT_NULL);
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionSupportMultiSelect), PermissionErrorCodeEnums.DIMENSION_SUPPORT_MULTI_SELECT_NOT_NULL);
                    BusinessLogicException.checkTrue(CollectionUtils.isEmpty(dimensionDataCodeInfoList), PermissionErrorCodeEnums.DIMENSION_DATA_CODE_INFO_LIST_NOT_NULL);
                    Set<String> dataCodeSet = new HashSet<>();
                    Set<String> dataNameSet = new HashSet<>();
                    Set<String> dataNameEnSet = new HashSet<>();
                    for (DataCodeInfo dataCodeInfo : dimensionDataCodeInfoList) {
                        String dataCode = dataCodeInfo.getDataCode();
                        String dataName = dataCodeInfo.getDataName();
                        String dataNameEn = dataCodeInfo.getDataNameEn();
                        BusinessLogicException.checkTrue(StringUtils.isBlank(dataCode), PermissionErrorCodeEnums.DATA_CODE_NOT_NULL);
                        BusinessLogicException.checkTrue(StringUtils.isBlank(dataName), PermissionErrorCodeEnums.DATA_NAME_NOT_NULL);
                        BusinessLogicException.checkTrue(StringUtils.isBlank(dataNameEn), PermissionErrorCodeEnums.DATA_NAME_EN_NOT_NULL);
                        BusinessLogicException.checkTrue(dataCodeSet.contains(dataCode), PermissionErrorCodeEnums.DATA_CODE_DUPLICATION);
                        BusinessLogicException.checkTrue(dataNameSet.contains(dataName), PermissionErrorCodeEnums.DATA_NAME_DUPLICATION);
                        BusinessLogicException.checkTrue(dataNameEnSet.contains(dataNameEn), PermissionErrorCodeEnums.DATA_NAME_EN_DUPLICATION);
                        dataCodeSet.add(dataCode);
                        dataNameSet.add(dataName);
                        dataNameEnSet.add(dataNameEn);
                    }
                }
                // 主数据
                else if (dimensionType.equals("MAIN_DATA")) {
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionTypeCode), PermissionErrorCodeEnums.DIMENSION_TYPE_CODE_NOT_NULL);
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionTypeName), PermissionErrorCodeEnums.DIMENSION_TYPE_NAME_NOT_NULL);
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionTypeNameEn), PermissionErrorCodeEnums.DIMENSION_TYPE_NAME_EN_NOT_NULL);
                    // BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionSupportMultiSelect), PermissionErrorCodeEnums.DIMENSION_SUPPORT_MULTI_SELECT_NOT_NULL);
                    BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionDataUrl), PermissionErrorCodeEnums.DIMENSION_DATA_URL_NOT_NULL);
                    BusinessLogicException.checkTrue(Objects.isNull(dataStructures), PermissionErrorCodeEnums.DATA_STRUCTURES_NOT_NULL);
                    // BusinessLogicException.checkTrue(StringUtils.isBlank(configId), PermissionErrorCodeEnums.CONFIG_ID_NOT_NULL);
                    multiDimensionRefMainDataCodeSet.add(dimensionTypeCode);

                }
            }
        }
        return multiDimensionRefMainDataCodeSet;
    }

    @Override
    public void updateMultiDynamicDataDimensionConfig(Long customDynamicId, MultiDimensionUpdateParam multiDimensionParam) {
        BusinessLogicException.checkTrue(Objects.isNull(multiDimensionParam), PermissionErrorCodeEnums.MULTI_DIMENSION_NOT_NULL);
        DynamicDataDimensionConfigDO dynamicDataDimensionConfig = dynamicDataDimensionConfigDAO.getById(customDynamicId);
        BusinessLogicException.checkTrue(Objects.isNull(dynamicDataDimensionConfig), PermissionErrorCodeEnums.NOT_EXIST);

        String useCaseDescription = Optional.ofNullable(multiDimensionParam.getUseCaseDescription()).orElse("");
        dynamicDataDimensionConfig.setUseCaseDescription(useCaseDescription);

        List<DimensionData> dimensionDataList = Optional.ofNullable(multiDimensionParam.getDimensionDataList()).orElse(Lists.newArrayList());
        updateDimensionData(dynamicDataDimensionConfig, dimensionDataList);


    }

    private void updateDimensionData(DynamicDataDimensionConfigDO dynamicDataDimensionConfig, List<DimensionData> dimensionDataList) {
        if (Objects.isNull(dynamicDataDimensionConfig) || CollectionUtils.isEmpty(dimensionDataList)) {
            return;
        }

        String oldMultiDimensionConfig = Optional.ofNullable(dynamicDataDimensionConfig.getMultiDimensionConfig()).orElse("");


        // 空数据直接进行覆盖
        if (StringUtils.isBlank(oldMultiDimensionConfig) || oldMultiDimensionConfig.equals("[]")) {
            String multiDimensionConfig = null;
            if (CollectionUtils.isEmpty(dimensionDataList)) {
                multiDimensionConfig = "";
            } else {
                multiDimensionConfig = JSON.toJSONString(dimensionDataList);
            }
            dynamicDataDimensionConfig.setMultiDimensionConfig(multiDimensionConfig);
            dynamicDataDimensionConfigDAO.updateById(dynamicDataDimensionConfig);
            return;
        }

        List<DimensionData> oldDimensionDataList = JSONArray.parseArray(oldMultiDimensionConfig, DimensionData.class);
        // 新增数据校验
        Set<String> multiDimensionRefMainDataCodeSet = new HashSet<>();
        checkFilterRefMainData(dimensionDataList, multiDimensionRefMainDataCodeSet);
        // 比对维度数据
        // 旧数据
        Map<String, DimensionData> oldMap = oldDimensionDataList.stream()
                .collect(Collectors.toMap(DimensionData::getDimensionTypeCode, Function.identity(), (v1, v2) -> v1));
        Set<String> oldKey = oldMap.keySet();
        // 新数据
        Map<String, DimensionData> newMap = dimensionDataList.stream()
                .collect(Collectors.toMap(DimensionData::getDimensionTypeCode, Function.identity(), (v1, v2) -> v1));
        Set<String> newKey = newMap.keySet();

        // 完全删除的维度
        Collection<String> removeKey = CollectionUtils.removeAll(oldKey, newKey);

        String typeCode = dynamicDataDimensionConfig.getTypeCode();
        for (String key : removeKey) {
            DimensionData dimensionData = oldMap.get(key);
            String dimensionType = dimensionData.getDimensionType();
            if (dimensionType.equals("ENUM") || dimensionType.equals("DICT")) {
                Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeExist(typeCode + ">" + dimensionData.getDimensionTypeCode());
                BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
            } else if (dimensionType.equals("MAIN_DATA")) {
                Boolean mainDataStatus = refactoringPermissionCasbinRuleManage.checkTypeCodeExist(typeCode + ">" + dimensionData.getDimensionTypeCode());
                BusinessLogicException.checkTrue(mainDataStatus, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
                Boolean mainDataDynamicStatus = refactoringPermissionCasbinRuleManage.checkTypeCodeExist(typeCode + ">" + "$" + dimensionData.getDimensionTypeCode());
                BusinessLogicException.checkTrue(mainDataDynamicStatus, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
            }
        }

        // 变更的维度
        Collection<String> updateKey = CollectionUtils.intersection(oldKey, newKey);
        for (String key : updateKey) {
            DimensionData oldDimensionData = oldMap.get(key);
            DimensionData newDimensionData = newMap.get(key);
            String dimensionType = newDimensionData.getDimensionType();
            // 字典和枚举数据变更处理
            if (dimensionType.equals("ENUM") || dimensionType.equals("DICT")) {
                List<DataCodeInfo> oldDimensionDataCodeInfoList = oldDimensionData.getDimensionDataCodeInfoList();
                List<DataCodeInfo> newDimensionDataCodeInfoList = newDimensionData.getDimensionDataCodeInfoList();

                List<String> oldCode = Optional.ofNullable(oldDimensionDataCodeInfoList).orElse(Lists.newArrayList())
                        .stream().map(DataCodeInfo::getDataCode).collect(Collectors.toList());
                List<String> newCode = Optional.ofNullable(newDimensionDataCodeInfoList).orElse(Lists.newArrayList())
                        .stream().map(DataCodeInfo::getDataCode).collect(Collectors.toList());

                Collection<String> removeCode = CollectionUtils.removeAll(oldCode, newCode);
                if (CollectionUtils.isNotEmpty(removeCode)) {
                    Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeDataCodeListExist(typeCode + ">" + newDimensionData.getDimensionTypeCode(), Lists.newArrayList(removeCode));
                    BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
                }

                Map<String, DataCodeInfo> oldDataCodeMap = oldDimensionDataCodeInfoList.stream()
                        .collect(Collectors.toMap(DataCodeInfo::getDataCode, Function.identity(), (v1, v2) -> v1));

                Map<String, DataCodeInfo> newDataCodeMap = newDimensionDataCodeInfoList.stream()
                        .collect(Collectors.toMap(DataCodeInfo::getDataCode, Function.identity(), (v1, v2) -> v1));
                Collection<String> updateCode = CollectionUtils.intersection(oldCode, newCode);

                for (String dataCode : updateCode) {
                    DataCodeInfo oldDataCodeInfo = oldDataCodeMap.get(dataCode);
                    DataCodeInfo newDataCodeInfo = newDataCodeMap.get(dataCode);
                    String oldDataName = oldDataCodeInfo.getDataName();
                    String newDataName = newDataCodeInfo.getDataName();
                    String oldDataNameEn = oldDataCodeInfo.getDataNameEn();
                    String newDataNameEn = newDataCodeInfo.getDataNameEn();
                    BusinessLogicException.checkTrue(!oldDataName.equals(newDataName), PermissionErrorCodeEnums.DATA_NAME_NOT_UPDATE);
                    BusinessLogicException.checkTrue(!oldDataNameEn.equals(newDataNameEn), PermissionErrorCodeEnums.DATA_NAME_EN_NOT_UPDATE);
                }
            }
        }

        // 多维度数据
        if (CollectionUtils.isNotEmpty(multiDimensionRefMainDataCodeSet)) {
            // 使用 :: 分割字符
            dynamicDataDimensionConfig.setMultiDimensionRefMainDataCode(
                    multiDimensionRefMainDataCodeSet.stream()
                            .collect(Collectors.joining("::", "::", "::"))
            );
        }

        String multiDimensionConfig = null;
        if (CollectionUtils.isEmpty(dimensionDataList)) {
            multiDimensionConfig = "";
        } else {
            multiDimensionConfig = JSON.toJSONString(dimensionDataList);
        }
        dynamicDataDimensionConfig.setMultiDimensionConfig(multiDimensionConfig);
        dynamicDataDimensionConfigDAO.updateById(dynamicDataDimensionConfig);
    }

    @Override
    public DynamicDataDimensionConfigDO getDynamicDataDimensionConfigById(Long customDynamicId) {
        if (Objects.isNull(customDynamicId)) {
            return null;
        }
        DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO = dynamicDataDimensionConfigDAO.getById(customDynamicId);
        return dynamicDataDimensionConfigDO;
    }

    @Override
    public void updateDynamicDataDimensionConfigById(DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO) {
        if (Objects.isNull(dynamicDataDimensionConfigDO)) {
            return;
        }
        dynamicDataDimensionConfigDAO.updateById(dynamicDataDimensionConfigDO);

    }

    @Override
    public DynamicDataDimensionConfigDO getDynamicDataDimensionConfigByTypeCode(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return null;
        }
        List<DynamicDataDimensionConfigDO> dynamicDataDimensionConfigDO = dynamicDataDimensionConfigDAO.getByTypeCode(typeCode);
        return CollectionUtils.isEmpty(dynamicDataDimensionConfigDO) ? null : dynamicDataDimensionConfigDO.get(0);
    }

    @Override
    public void deleteCustomDynamicDataConfig(String typeCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        List<DynamicDataDimensionConfigDO> list = dynamicDataDimensionConfigDAO.getByTypeCode(typeCode);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(list), PermissionErrorCodeEnums.TYPE_CODE_NOT_EXIST);
        DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO = list.get(0);
        Integer dimension = dynamicDataDimensionConfigDO.getDimension();
        // 单维度数据
        if (Objects.equals(dimension, 0)) {
            Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeExist(typeCode);
            Boolean statusDynamic = refactoringPermissionCasbinRuleManage.checkTypeCodeExist("$" + typeCode);
            BusinessLogicException.checkTrue(status || statusDynamic, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
            dynamicDataDimensionConfigDO.setIsDelete(BusinessConstant.Y);
            dynamicDataDimensionConfigDAO.updateById(dynamicDataDimensionConfigDO);
            return;
        }
        // 多维度数据
        else if (Objects.equals(dimension, 1)) {
            String multiDimensionConfig = dynamicDataDimensionConfigDO.getMultiDimensionConfig();
            if (StringUtils.isBlank(multiDimensionConfig)) {
                dynamicDataDimensionConfigDO.setIsDelete(BusinessConstant.Y);
                dynamicDataDimensionConfigDAO.updateById(dynamicDataDimensionConfigDO);
                return;
            }
            List<DimensionData> dimensionDataList = JSON.parseArray(multiDimensionConfig, DimensionData.class);
            if (CollectionUtils.isEmpty(dimensionDataList)) {
                dynamicDataDimensionConfigDO.setIsDelete(BusinessConstant.Y);
                dynamicDataDimensionConfigDAO.updateById(dynamicDataDimensionConfigDO);
                return;
            }
            List<String> dimensionTypeCodeList = dimensionDataList.stream().map(DimensionData::getDimensionTypeCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dimensionTypeCodeList)) {
                List<String> tcList = dimensionTypeCodeList.stream()
                        .map(e -> typeCode + ">" + e)
                        .collect(Collectors.toList());

                Boolean status = refactoringPermissionCasbinRuleManage.checkTypeCodeListExist(tcList);
                BusinessLogicException.checkTrue(status, PermissionErrorCodeEnums.DATA_IS_REFERENCED_CANNOT_BE_DELETED);
            }

            dynamicDataDimensionConfigDO.setIsDelete(BusinessConstant.Y);
            dynamicDataDimensionConfigDAO.updateById(dynamicDataDimensionConfigDO);

        }

    }

    @Override
    public List<CustomDynamicDataConfigVO> listDynamicDataDimensionConfigBySystemCodeList(List<String> systemList) {
        if (CollectionUtils.isEmpty(systemList)) {
            return Lists.newArrayList();
        }
        List<DynamicDataDimensionConfigDO> list = dynamicDataDimensionConfigDAO.listBySystemCodeList(systemList);
        List<String> typeCodeList = list.stream().map(DynamicDataDimensionConfigDO::getTypeCode).collect(Collectors.toList());
        List<String> refMainTypeCode = list.stream()
                .map(DynamicDataDimensionConfigDO::getMultiDimensionRefMainDataCode)
                .filter(StringUtils::isNotBlank)
                .map(
                        e -> StringUtils.split(e, "::")
                )
                .flatMap(Arrays::stream)
                .distinct()
                .collect(Collectors.toList());

        List<String> allTypeCode = new ArrayList<>();
        allTypeCode.addAll(typeCodeList);
        allTypeCode.addAll(refMainTypeCode);

        List<String> existCodes = interfaceFieldMappingManage.selectSupportSearchTypeCodes(allTypeCode);


        Map<String, List<DynamicDataCodeVO>> dynamicDataCodeVOMap = Maps.newHashMap();
        Map<String, List<RuleDataVO>> ruleDataVOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(typeCodeList)) {
            List<SysDynamicDataPermissionDO> dynamicDataCodeList = sysDynamicDataPermissionDAO.listDataByTypeCodeList(typeCodeList);

            dynamicDataCodeVOMap.putAll(
                    dynamicDataCodeList.stream()
                            .map(
                                    e -> {
                                        DynamicDataCodeVO vo = new DynamicDataCodeVO();
                                        vo.setTypeCode(e.getTypeCode());
                                        vo.setDataCode(e.getDataCode());
                                        vo.setDataUrl(e.getDataUrl());
                                        vo.setOptionalParameter(e.getOptionalParameter());
                                        vo.setDescription(e.getDescription());
                                        return vo;
                                    }
                            )
                            .collect(Collectors.groupingBy(DynamicDataCodeVO::getTypeCode))
            );

            List<PermissionRuleDO> ruleDataList = permissionRuleDAO.listByTypeCodeList(typeCodeList);
            ruleDataVOMap.putAll(ruleDataList.stream()
                    .map(
                            e -> {
                                RuleDataVO vo = new RuleDataVO();
                                vo.setRuleName(e.getRuleName());
                                vo.setRuleCode(e.getRuleCode());
                                vo.setRuleType(e.getRuleType());
                                vo.setRelationTypeCode(e.getRelationTypeCode());
                                return vo;
                            }
                    )
                    .collect(Collectors.groupingBy(RuleDataVO::getRelationTypeCode)));
        }

        Map<String, List<CustomDynamicDataConfigNestedVO>> map = list.stream().collect(
                Collectors.groupingBy(
                        DynamicDataDimensionConfigDO::getSingleSystem,
                        Collectors.mapping(
                                e -> {
                                    CustomDynamicDataConfigNestedVO vo = new CustomDynamicDataConfigNestedVO();
                                    vo.setCustomDynamicId(e.getId());
                                    Integer dimension = e.getDimension();
                                    vo.setDimension(dimension);
                                    if (dimension.equals(0)) {
                                        SingleDimensionVO singleDimensionVO = new SingleDimensionVO();
                                        singleDimensionVO.setDataStructures(e.getDataStructures());
                                        singleDimensionVO.setDataUrl(e.getDataUrl());
                                        singleDimensionVO.setRequestType(e.getRequestType());
                                        singleDimensionVO.setTypeCode(e.getTypeCode());
                                        singleDimensionVO.setTypeName(e.getTypeName());
                                        if (RequestInfoHolder.isChinese() || StringUtils.isEmpty(e.getTypeNameEn())) {
                                            singleDimensionVO.setShowTypeName(e.getTypeName());
                                        } else {
                                            singleDimensionVO.setShowTypeName(e.getTypeNameEn());
                                        }
                                        singleDimensionVO.setTypeNameEn(e.getTypeNameEn());
                                        singleDimensionVO.setUseCaseDescription(e.getUseCaseDescription());
                                        if (existCodes.contains(e.getTypeCode())) {
                                            singleDimensionVO.setIsSupportSearch(1);
                                        }
                                        singleDimensionVO.setDynamicDataCodeList(dynamicDataCodeVOMap.getOrDefault(e.getTypeCode(), Lists.newArrayList()));
                                        singleDimensionVO.setRuleDataList(ruleDataVOMap.getOrDefault(e.getTypeCode(), Lists.newArrayList()));
                                        singleDimensionVO.setCreateDate(e.getCreateDate());
                                        vo.setSingleDimensionVO(singleDimensionVO);

                                    } else if (dimension.equals(1)) {
                                        MultiDimensionVO multiDimensionVO = new MultiDimensionVO();

                                        multiDimensionVO.setTypeCode(e.getTypeCode());
                                        multiDimensionVO.setTypeName(e.getTypeName());
                                        if (RequestInfoHolder.isChinese() || StringUtils.isEmpty(e.getTypeNameEn())) {
                                            multiDimensionVO.setShowTypeName(e.getTypeName());
                                        } else {
                                            multiDimensionVO.setShowTypeName(e.getTypeNameEn());
                                        }
                                        multiDimensionVO.setTypeNameEn(e.getTypeNameEn());
                                        multiDimensionVO.setUseCaseDescription(e.getUseCaseDescription());
                                        multiDimensionVO.setCreateDate(e.getCreateDate());

                                        String multiDimensionConfig = e.getMultiDimensionConfig();
                                        if (StringUtils.isNotEmpty(multiDimensionConfig)) {
                                            List<DimensionDataExt> dimensionDataExtList = JSON.parseArray(multiDimensionConfig, DimensionDataExt.class);
                                            if (CollectionUtils.isNotEmpty(dimensionDataExtList)) {
                                                for (DimensionDataExt dimensionDataExt : dimensionDataExtList) {
                                                    if (existCodes.contains(dimensionDataExt.getDimensionTypeCode())) {
                                                        dimensionDataExt.setIsSupportSearch(1);
                                                    }
                                                }

                                            }
                                            multiDimensionVO.setDimensionDataList(dimensionDataExtList);
                                        }

                                        vo.setMultiDimensionVO(multiDimensionVO);

                                    }
                                    return vo;
                                }
                                , Collectors.toList())
                )
        );

        return map.entrySet()
                .stream()
                .map(e -> {
                    CustomDynamicDataConfigVO vo = new CustomDynamicDataConfigVO();
                    vo.setSystemCode(e.getKey());
                    vo.setDynamicTypeCodeVOList(e.getValue());
                    return vo;
                }).collect(Collectors.toList());

    }

    @Override
    public List<String> getSystemTypeCode(String systemCode) {
        if (StringUtils.isBlank(systemCode)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        List<String> baseTypeCode = sysDataPermissionDAO.getSystemTypeCode(systemCode);
        List<String> mainTypeCode = businessBasicDataPermissionConfigDAO.getSystemTypeCode(systemCode);
        List<String> dynamicTypeCode = sysDynamicDataPermissionDAO.getSystemTypeCode(systemCode);
        List<DynamicDataDimensionConfigDO> dynamicDimensionTypeCode = dynamicDataDimensionConfigDAO.getBySystem(systemCode);

        result.addAll(baseTypeCode);
        result.addAll(mainTypeCode);
        result.addAll(dynamicTypeCode);

        for (DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO : dynamicDimensionTypeCode) {
            Integer dimension = dynamicDataDimensionConfigDO.getDimension();
            if (Objects.isNull(dimension)) {
                continue;
            }
            if (dimension.equals(0)) {
                result.add(dynamicDataDimensionConfigDO.getTypeCode());
            } else if (dimension.equals(1)) {
                String multiDimensionConfig = dynamicDataDimensionConfigDO.getMultiDimensionConfig();
                if (StringUtils.isBlank(multiDimensionConfig) || multiDimensionConfig.equals("[]")) {
                    continue;
                }
                List<DimensionData> dimensionDataList = JSONArray.parseArray(multiDimensionConfig, DimensionData.class);
                for (DimensionData dimensionData : dimensionDataList) {
                    String dimensionTypeCode = dimensionData.getDimensionTypeCode();
                    if (StringUtils.isBlank(dimensionTypeCode)) {
                        continue;
                    }

                    result.add(dynamicDataDimensionConfigDO.getTypeCode() + ">" + dimensionTypeCode);
                }
            }
        }
        return result;
    }

    @Override
    public List<String> getTypeCodesBySystemList(List<String> systemCodeList) {
        if (CollectionUtils.isEmpty(systemCodeList)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        List<String> baseTypeCode = sysDataPermissionDAO.getTypeCodesBySystemList(systemCodeList);
        List<String> mainTypeCode = businessBasicDataPermissionConfigDAO.getTypeCodesBySystemList(systemCodeList);
        List<String> dynamicTypeCode = sysDynamicDataPermissionDAO.getTypeCodesBySystemList(systemCodeList);
        List<DynamicDataDimensionConfigDO> dynamicDimensionTypeCode = dynamicDataDimensionConfigDAO.getBySystemList(systemCodeList);

        result.add(BusinessConstant.SELECT_ALL_TYPE_CODE);
        result.addAll(baseTypeCode);
        result.addAll(mainTypeCode);
        result.addAll(dynamicTypeCode);

        for (DynamicDataDimensionConfigDO dynamicDataDimensionConfigDO : dynamicDimensionTypeCode) {
            Integer dimension = dynamicDataDimensionConfigDO.getDimension();
            if (Objects.isNull(dimension)) {
                continue;
            }
            if (dimension.equals(0)) {
                result.add(dynamicDataDimensionConfigDO.getTypeCode());
            } else if (dimension.equals(1)) {
                String multiDimensionConfig = dynamicDataDimensionConfigDO.getMultiDimensionConfig();
                if (StringUtils.isBlank(multiDimensionConfig) || multiDimensionConfig.equals("[]")) {
                    continue;
                }
                List<DimensionData> dimensionDataList = JSONArray.parseArray(multiDimensionConfig, DimensionData.class);
                for (DimensionData dimensionData : dimensionDataList) {
                    String dimensionTypeCode = dimensionData.getDimensionTypeCode();
                    if (StringUtils.isBlank(dimensionTypeCode)) {
                        continue;
                    }

                    result.add(dynamicDataDimensionConfigDO.getTypeCode() + ">" + dimensionTypeCode);
                }
            }
        }
        return result;
    }

    @Override
    public void editMultiDimension(EditMultiDimensionParam editMultiDimension) {
        String typeCode = editMultiDimension.getTypeCode();
        List<DimensionData> dimensionDataList = Optional.ofNullable(editMultiDimension.getDimensionDataList()).orElse(Lists.newArrayList());
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(dimensionDataList), PermissionErrorCodeEnums.DIMENSION_DATA_LIST_NOT_NULL);

        DynamicDataDimensionConfigDO dynamicDataDimensionConfig = getDynamicDataDimensionConfigByTypeCode(typeCode);
        updateDimensionData(dynamicDataDimensionConfig, dimensionDataList);
    }

    @Override
    public Boolean checkMultiDimensionRef(MultiDimensionTypeCodeParam multiDimensionTypeCodeParam) {
        String typeCode = multiDimensionTypeCodeParam.getTypeCode();
        String dimensionTypeCode = multiDimensionTypeCodeParam.getDimensionTypeCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(dimensionTypeCode), PermissionErrorCodeEnums.DIMENSION_TYPE_CODE_NOT_NULL);
        return refactoringPermissionCasbinRuleManage.checkTypeCodeExist(typeCode + ">" + dimensionTypeCode);
    }

    private void processChildStrMap(BusinessBasicDataTreeVO vo, Map<String, String> childrenStrMap) {
        List<BusinessBasicDataTreeVO> childrenList = vo.getChildrenList();
        if (CollectionUtils.isEmpty(childrenList)) {
            childrenList = Lists.newArrayList();
        }
        StringBuilder idStr = new StringBuilder();
        for (BusinessBasicDataTreeVO child : childrenList) {
            processChildStrMap(child, childrenStrMap);
            if (idStr.length() > 0) {
                idStr.append("|");
            }
            idStr.append(child.getId());
        }
        childrenStrMap.put(vo.getId(), String.valueOf(idStr));
    }

    private void hmsetWithExpire(String key, Map<String, String> map, long expireTime) {
        this.redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                operations.hasKey(key);
                operations.opsForHash().putAll(key, map);
                operations.expire(key, expireTime, TimeUnit.SECONDS);
                return null;
            }
        });
    }
}

package com.imile.permission.manage.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.permission.api.dto.MenuPermissionApiDTO;
import com.imile.permission.api.dto.PermissionApiDTO;
import com.imile.permission.api.dto.RocketMessageApiDTO;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.PermissionSuperAccountDAO;
import com.imile.permission.domain.entity.PermissionSuperAccountDO;
import com.imile.permission.integration.hermes.OrgSystemResourceIntegration;
import com.imile.permission.integration.resource.ResSystemResourceIntegration;
import com.imile.permission.jcasbin.domain.dto.RefactoringPermissionCasbinRuleDTO;
import com.imile.permission.manage.EnforcerRoleWorkCenterManage;
import com.imile.permission.manage.EsWithCacheManage;
import com.imile.permission.manage.ResourceManage;
import com.imile.permission.manage.UserAuthorizationManage;
import com.imile.permission.util.CasbinParseUtil;
import com.imile.permission.util.UserAndRolePermissionUtil;
import com.imile.permission.util.UserInfoUtil;
import com.imile.resource.api.dto.ResResourceTreeForAuthApiDTO;
import com.imile.resource.api.dto.ResSystemResourceTreeApiDTO;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/25
 */

@Service
public class ResourceManageImpl implements ResourceManage {

    @Autowired
    private OrgSystemResourceIntegration orgSystemResourceIntegration;

    @Autowired
    private ResSystemResourceIntegration resSystemResourceIntegration;

    @Autowired
    private PermissionSuperAccountDAO permissionSuperAccountDAO;

    @Autowired
    private EnforcerRoleWorkCenterManage enforcerRoleWorkCenterManage;
    @Autowired
    private EsWithCacheManage esWithCacheManage;

    @Autowired
    private UserAuthorizationManage userAuthorizationManage;


    @Autowired
    private ApplicationEventPublisher publisher;

    @Value("${rocketmq.producer.auto-authorize.user.topic}")
    private String userAutoAuthorizeTopic;

    @Value("${rocketmq.producer.auto-authorize.role.topic}")
    private String roleAutoAuthorizeTopic;


    @Override
    public List<ResSystemResourceTreeApiDTO> getTree(String lang) {
        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();
        String userCode = userInfo.getUserCode();
        Long orgId = userInfo.getOrgId();

        List<PermissionSuperAccountDO> superAccountList = permissionSuperAccountDAO.getByUserCode(userCode);
        if (CollectionUtils.isNotEmpty(superAccountList)) {
            return resSystemResourceIntegration.getMenuTree(null, lang);
        }

        Map<Long, List<Long>> menuMap = enforcerRoleWorkCenterManage.getMenuByUserCode(userCode);

        if (MapUtils.isEmpty(menuMap)) {
            return Collections.EMPTY_LIST;
        }
        return resSystemResourceIntegration.getMenuTree( menuMap, lang);
    }

    @Override
    public void addFullPermission(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return;
        }
        List<PermissionSuperAccountDO> superAccountList = permissionSuperAccountDAO.getByUserCode(userCode);
        if (CollectionUtils.isNotEmpty(superAccountList)) {
            return;
        }
        PermissionSuperAccountDO permissionSuperAccountDO = new PermissionSuperAccountDO();
        permissionSuperAccountDO.setUserCode(userCode);
        permissionSuperAccountDAO.save(permissionSuperAccountDO);
    }

    @Override
    public void deleteFullPermission(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return;
        }
        List<PermissionSuperAccountDO> superAccountList = permissionSuperAccountDAO.getByUserCode(userCode);
        if (CollectionUtils.isNotEmpty(superAccountList)) {
            permissionSuperAccountDAO.removeByIds(superAccountList.stream().map(PermissionSuperAccountDO::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public List<String> getFullPermission() {
        List<PermissionSuperAccountDO> superAccountList = permissionSuperAccountDAO.list();
        return Optional.ofNullable(superAccountList)
                .orElse(Collections.emptyList())
                .stream()
                .map(PermissionSuperAccountDO::getUserCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<ResSystemResourceTreeApiDTO> getAllTree(String lang) {
        UserInfoDTO userInfo = Optional.ofNullable(UserInfoUtil.getUserInfo()).orElse(UserInfoUtil.getAdmin());
        Long orgId = userInfo.getOrgId();
        return resSystemResourceIntegration.getMenuTree(null, lang);
    }

    @Override
    public List<ResResourceTreeForAuthApiDTO> getUserTree(String userCode, String system, String[] nodeType, String lang, String parentCode) {

        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();
        Long orgId = userInfo.getOrgId();
        // 查询缓存
        PermissionApiDTO userAuthorizationAndDB = esWithCacheManage.getAuthorizationAndRecordThreadLocal(userCode);
        if (Objects.isNull(userAuthorizationAndDB)) {
            return Lists.newArrayList();
        }
        List<MenuPermissionApiDTO> menuPermissionDTOList = userAuthorizationAndDB.getMenuPermissionDTOList();
        if (CollectionUtils.isEmpty(menuPermissionDTOList)) {
            return Lists.newArrayList();
        }
        Map<Long,String> allMap = Maps.newHashMap();
        Map<String,Long> rootIdMap = Maps.newHashMap();
        for (MenuPermissionApiDTO menuPermissionApiDTO : menuPermissionDTOList) {
            if(menuPermissionApiDTO.getMenuId() != null){
                allMap.put(menuPermissionApiDTO.getMenuId(),menuPermissionApiDTO.getSystemPlatform());
                rootIdMap.put(menuPermissionApiDTO.getSystemPlatform(), menuPermissionApiDTO.getMenuId());
            }
            if(CollectionUtils.isNotEmpty(menuPermissionApiDTO.getMenuIdList())){
                menuPermissionApiDTO.getMenuIdList().forEach(item -> {
                    allMap.put(item,menuPermissionApiDTO.getSystemPlatform());
                });
            }
            if(CollectionUtils.isNotEmpty(menuPermissionApiDTO.getPartiallyMenuIdList())){
                menuPermissionApiDTO.getPartiallyMenuIdList().forEach(item -> {
                    allMap.put(item,menuPermissionApiDTO.getSystemPlatform());
                });
            }
        }
        if (MapUtils.isEmpty(allMap)) {
            return Collections.EMPTY_LIST;
        }
        Set<Long> allMenuId = allMap.keySet();
        List<ResResourceTreeForAuthApiDTO> tree = resSystemResourceIntegration.getAuthTreeByResourceId(new ArrayList<>(allMenuId), system, null, nodeType, lang, parentCode);
        autoAuthMoveMenu(tree,allMap,rootIdMap);
        return tree;
    }

    @Override
    public void autoAuthMoveMenu(List<ResResourceTreeForAuthApiDTO> tree, Map<Long,String> allIdSystemMap, Map<String,Long> rootIdMap) {
        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();
        String userCode = userInfo.getUserCode();
        if(CollectionUtils.isEmpty(tree)){
            return;
        }
        Map<Long, Set<Long>> fromChildMap = Maps.newHashMap();
        for (ResResourceTreeForAuthApiDTO menu : tree) {
            processToAllMenuIdSet(menu,fromChildMap);
        }
        Set<Long> userMenuIdList = UserAndRolePermissionUtil.getUserMenuIdList();
        UserAndRolePermissionUtil.removeUserMenuIdList();
        Map<Long, Set<Long>> roleMenuMap = UserAndRolePermissionUtil.getRoleMenuMap();
        UserAndRolePermissionUtil.removeRoleMenuMap();
        List<RefactoringPermissionCasbinRuleDTO> userAutoAuthorizeEntityList = Lists.newArrayList();
        Map<Long,List<RefactoringPermissionCasbinRuleDTO>> roleAutoAuthorizeEntityMap = Maps.newHashMap();
        buildAutoAuthorizeEntity(allIdSystemMap, rootIdMap,fromChildMap, userMenuIdList, userCode, userAutoAuthorizeEntityList, roleMenuMap, roleAutoAuthorizeEntityMap);
        sendMenuMoveAuthMQ(userCode, userAutoAuthorizeEntityList, roleAutoAuthorizeEntityMap);
    }

    private static void buildAutoAuthorizeEntity(Map<Long, String> allMap, Map<String,Long> rootIdMap, Map<Long, Set<Long>> fromChildMap, Set<Long> userMenuIdList, String userCode, List<RefactoringPermissionCasbinRuleDTO> userAutoAuthorizeEntityList, Map<Long, Set<Long>> roleMenuMap, Map<Long, List<RefactoringPermissionCasbinRuleDTO>> roleAutoAuthorizeEntityMap) {

        for (Map.Entry<Long, Set<Long>> entry : fromChildMap.entrySet()) {
            Long menuId = entry.getKey();
            Set<Long> fromSet = entry.getValue();
            if(CollectionUtils.isEmpty(fromSet)){
                continue;
            }
            for (Long fromId : fromSet) {
                String system = allMap.get(fromId);
                if(system == null){
                    continue;
                }
                Long rootId = rootIdMap.get(system);
                if(rootId == null){
                    continue;
                }
                if (CollectionUtils.isNotEmpty(userMenuIdList) && userMenuIdList.contains(fromId)) {
                    RefactoringPermissionCasbinRuleDTO dto = new RefactoringPermissionCasbinRuleDTO();
                    dto.setPtype(BusinessConstant.CASBIN_PTYPE_P);
                    dto.setV0(CasbinParseUtil.formatUserCode(userCode));
                    dto.setV1(CasbinParseUtil.formatSP(system));
                    dto.setV2(CasbinParseUtil.formatRM(rootId));
                    dto.setV3(CasbinParseUtil.formatM(menuId));
                    dto.setV4(BusinessConstant.PARTIALLY);
                    dto.setV5(BusinessConstant.POUND);
                    userAutoAuthorizeEntityList.add(dto);
                }
                if(roleMenuMap != null && CollectionUtils.isNotEmpty(roleMenuMap.entrySet())) {
                    for (Map.Entry<Long, Set<Long>> roleMenuEntry : roleMenuMap.entrySet()) {
                        Set<Long> menuIdList = roleMenuEntry.getValue();
                        if (CollectionUtils.isEmpty(menuIdList)) {
                            continue;
                        }
                        if (menuIdList.contains(fromId)) {
                            RefactoringPermissionCasbinRuleDTO dto = new RefactoringPermissionCasbinRuleDTO();
                            dto.setPtype(BusinessConstant.CASBIN_PTYPE_P);
                            dto.setV0(CasbinParseUtil.formatR(roleMenuEntry.getKey()));
                            dto.setV1(CasbinParseUtil.formatSP(system));
                            dto.setV2(CasbinParseUtil.formatRM(rootId));
                            dto.setV3(CasbinParseUtil.formatM(menuId));
                            dto.setV4(BusinessConstant.PARTIALLY);
                            dto.setV5(BusinessConstant.POUND);
                            List<RefactoringPermissionCasbinRuleDTO> list = roleAutoAuthorizeEntityMap.computeIfAbsent(roleMenuEntry.getKey(), k -> Lists.newArrayList());
                            list.add(dto);
                        }
                    }
                }
            }
        }
    }


    private void sendMenuMoveAuthMQ(String userCode, List<RefactoringPermissionCasbinRuleDTO> userAutoAuthorizeEntityList, Map<Long, List<RefactoringPermissionCasbinRuleDTO>> roleAutoAuthorizeEntityMap) {
        if(CollectionUtils.isNotEmpty(userAutoAuthorizeEntityList)) {
            publisher.publishEvent(
                    RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                            .topic(userAutoAuthorizeTopic)
                            .tag("MENU_MOVE")
                            .key(userCode)
                            .data(userAutoAuthorizeEntityList)
                            .build()
            );
        }
        if(MapUtils.isNotEmpty(roleAutoAuthorizeEntityMap)) {
            for (Map.Entry<Long, List<RefactoringPermissionCasbinRuleDTO>> mqEntry : roleAutoAuthorizeEntityMap.entrySet()) {
                if (CollectionUtils.isEmpty(mqEntry.getValue())) {
                    continue;
                }
                publisher.publishEvent(
                        RocketMessageApiDTO.<List<RefactoringPermissionCasbinRuleDTO>>builder()
                                .topic(roleAutoAuthorizeTopic)
                                .tag("MENU_MOVE")
                                .key(String.valueOf(mqEntry.getKey()))
                                .data(mqEntry.getValue())
                                .build()
                );
            }
        }
    }

    private void processToAllMenuIdSet(ResResourceTreeForAuthApiDTO menu, Map<Long,Set<Long>> fromChildMap) {
        if(menu == null){
            return ;
        }
        if(menu.getFromChildMenuId() != null){
            fromChildMap.put(menu.getId(),menu.getFromChildMenuId());
        }
        if(CollectionUtils.isEmpty(menu.getChildren())){
            return;
        }
        for (ResResourceTreeForAuthApiDTO child : menu.getChildren()) {
            processToAllMenuIdSet(child,fromChildMap);
        }

    }
}

package com.imile.permission.manage.impl;

import com.imile.permission.dao.AuthorityApprovalMainDataDAO;
import com.imile.permission.domain.entity.AuthorityApprovalMainDataDO;
import com.imile.permission.manage.AuthorityApprovalMainDataManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/18
 */
@Service
public class AuthorityApprovalMainDataManageImpl implements AuthorityApprovalMainDataManage {

    @Autowired
    private AuthorityApprovalMainDataDAO authorityApprovalMainDataDAO;

    @Override
    public void save(List<AuthorityApprovalMainDataDO> mainData) {
        if (CollectionUtils.isEmpty(mainData)) {
            return;
        }
        authorityApprovalMainDataDAO.saveBatch(mainData);
    }

    @Override
    public List<AuthorityApprovalMainDataDO> getMainDataByAuthorityApprovalId(Long authorityApprovalId) {
        return authorityApprovalMainDataDAO.getMainDataByAuthorityApprovalId(authorityApprovalId);
    }

    @Override
    public void deleteByAuthorityApprovalId(Long authorityApprovalId) {
        authorityApprovalMainDataDAO.removeByAuthorityApprovalId(authorityApprovalId);
    }
}

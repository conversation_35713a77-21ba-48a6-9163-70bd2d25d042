package com.imile.permission.manage.impl;

import com.alibaba.fastjson.JSONArray;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.PermissionSuperAccountDAO;
import com.imile.permission.dao.SysSubAdminDAO;
import com.imile.permission.domain.entity.PermissionSuperAccountDO;
import com.imile.permission.domain.entity.SysSubAdminDO;
import com.imile.permission.domain.permission.dto.AdminDTO;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.manage.AdminManage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdminManageImpl implements AdminManage {

    @Autowired
    private SysSubAdminDAO sysSubAdminDAO;

    @Autowired
    private PermissionSuperAccountDAO permissionSuperAccountDAO;

    @Override
    public AdminDTO getAdminDTO(String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);

        // 超级管理员身份
        List<PermissionSuperAccountDO> superAccountList = permissionSuperAccountDAO.getByUserCode(userCode);
        if (CollectionUtils.isNotEmpty(superAccountList)) {
            AdminDTO adminDTO = new AdminDTO();
            adminDTO.setIsSuperAccount(Boolean.TRUE);
            adminDTO.setRecordVersion(superAccountList.get(BusinessConstant.ZERO).getRecordVersion());
            return adminDTO;
        }

        // 子管理员身份
        List<SysSubAdminDO> sysSubAdminList = sysSubAdminDAO.getByUserCode(userCode);

        if (CollectionUtils.isEmpty(sysSubAdminList)) {
            return null;
        }

        AdminDTO adminDTO = new AdminDTO();
        // 取一个账号
        SysSubAdminDO sysSubAdminDO = sysSubAdminList.get(BusinessConstant.ZERO);
        adminDTO.setIsSysSubAdmin(Boolean.TRUE);
        adminDTO.setIsEnabled(sysSubAdminDO.getIsEnabled());
        String multipleSystem = sysSubAdminDO.getMultipleSystem();
        if (StringUtils.isNotBlank(multipleSystem)) {
            List<String> list = JSONArray.parseArray(multipleSystem, String.class);
            adminDTO.setSystemList(list);
        }

        String batchAuthorizationSystem = sysSubAdminDO.getBatchAuthorizationSystem();
        if (StringUtils.isNotBlank(batchAuthorizationSystem)) {
            List<String> list = JSONArray.parseArray(batchAuthorizationSystem, String.class);
            adminDTO.setBatchAuthorizationList(list);
        }

        String sensitiveMenusSystem = sysSubAdminDO.getSensitiveMenusSystem();
        if (StringUtils.isNotBlank(sensitiveMenusSystem)) {
            List<String> list = JSONArray.parseArray(sensitiveMenusSystem, String.class);
            adminDTO.setSensitiveMenusList(list);
        }

        adminDTO.setRecordVersion(sysSubAdminDO.getRecordVersion());
        return adminDTO;
    }
}

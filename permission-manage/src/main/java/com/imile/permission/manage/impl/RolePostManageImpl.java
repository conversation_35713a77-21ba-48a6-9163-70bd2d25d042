package com.imile.permission.manage.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.resource.dto.DictDataDTO;
import com.imile.hrms.api.base.param.PostConditionParam;
import com.imile.hrms.api.base.result.PostDTO;
import com.imile.hrms.api.organization.dto.EntPostApiDTO;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.MenuPermissionApiDTO;
import com.imile.permission.api.dto.RoleAddApiDTO;
import com.imile.permission.api.dto.RoleApiDTO;
import com.imile.permission.api.dto.RoleMenuBindApiDTO;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.context.RequestInfoHolder;
import com.imile.permission.convert.RoleCacheConvert;
import com.imile.permission.convert.SysRoleConvert;
import com.imile.permission.dao.PostRoleOperationCollectDAO;
import com.imile.permission.dao.PostRoleOperationDetailDAO;
import com.imile.permission.dao.SysDataPermissionDAO;
import com.imile.permission.dao.SysRoleDao;
import com.imile.permission.domain.applicationApprove.vo.EffectiveLossCountVO;
import com.imile.permission.domain.dataPermission.dto.DataCodeExtensionTagDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.dto.DimensionDataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.MultiDynamicDataConfigValueDTO;
import com.imile.permission.domain.dataPermission.vo.PermissionBaseTypeVO;
import com.imile.permission.domain.dataPermission.vo.SpecificRuleConfigVO;
import com.imile.permission.domain.entity.PermissionAuthorizationRuleDO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.permission.dto.AdminDTO;
import com.imile.permission.domain.permission.dto.PermissionDTO;
import com.imile.permission.domain.permission.vo.PermissionVO;
import com.imile.permission.domain.relation.dto.PostRoleDTO;
import com.imile.permission.domain.relation.dto.PostRoleDetailsDTO;
import com.imile.permission.domain.relation.dto.PostRoleOperationCollectionDTO;
import com.imile.permission.domain.relation.param.BatchBindingParam;
import com.imile.permission.domain.relation.param.PostQueryParam;
import com.imile.permission.domain.relation.query.PostRolePageQuery;
import com.imile.permission.domain.relation.vo.PostVO;
import com.imile.permission.domain.relation.vo.SysPostRoleRelationDetailVO;
import com.imile.permission.domain.report.SystemCountDTO;
import com.imile.permission.domain.role.dto.RoleBasicDTO;
import com.imile.permission.domain.role.dto.RoleCacheDTO;
import com.imile.permission.domain.role.dto.SysRoleDTO;
import com.imile.permission.domain.role.param.RoleAddPermissionRuleParam;
import com.imile.permission.domain.role.param.RoleBasicParam;
import com.imile.permission.domain.role.param.RolePageQueryParam;
import com.imile.permission.domain.role.query.SysRoleQuery;
import com.imile.permission.domain.role.vo.ParentChildRoleVO;
import com.imile.permission.domain.role.vo.RoleEditableVO;
import com.imile.permission.domain.role.vo.RolePageVO;
import com.imile.permission.domain.role.vo.SysRoleResultVO;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.enums.PostRoleOperationTypeEnum;
import com.imile.permission.enums.RoleApplyTypeEnum;
import com.imile.permission.enums.RoleAuthSceneEnum;
import com.imile.permission.enums.RoleTypeEnum;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.helper.PermissionPageHelper;
import com.imile.permission.integration.dict.DictIntegration;
import com.imile.permission.integration.hrms.HrmsPostIntegration;
import com.imile.permission.integration.resource.ResSystemResourceIntegration;
import com.imile.permission.manage.AdminManage;
import com.imile.permission.manage.AuthorizationSubjectModelManage;
import com.imile.permission.manage.ClientRoleAuthorityManage;
import com.imile.permission.manage.DataPermissionManage;
import com.imile.permission.manage.EnforcerManage;
import com.imile.permission.manage.PermissionSystemManage;
import com.imile.permission.manage.PostCacheManage;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.manage.RoleCacheManage;
import com.imile.permission.manage.RoleManager;
import com.imile.permission.manage.RolePostManage;
import com.imile.permission.util.OrikaUtil;
import com.imile.permission.util.PageUtil;
import com.imile.permission.util.PermissionCollectionUtils;
import com.imile.permission.util.UserInfoUtil;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.imile.permission.constants.BusinessConstant.Language.EN_US;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RolePostManageImpl implements RolePostManage {

    @Autowired
    private SysRoleDao roleDao;

    @Autowired
    private SysDataPermissionDAO dataPermissionDAO;

    @Autowired
    private EnforcerManage enforcerManage;

    @Autowired
    private AdminManage adminManage;

    @Autowired
    private HrmsPostIntegration hrmsPostIntegration;
    @Autowired
    private PermissionSystemManage permissionSystemManage;
    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;
    @Autowired
    private RoleCacheManage roleCacheManage;
    @Autowired
    private PostCacheManage postCacheManage;
    @Autowired
    private PostRoleOperationCollectDAO postRoleOperationCollectDAO;
    @Autowired
    private ClientRoleAuthorityManage clientRoleAuthorityManage;

    @Autowired
    private PostRoleOperationDetailDAO postRoleOperationDetailDAO;

    @Autowired
    private DataPermissionManage dataPermissionManage;
    @Autowired
    private RoleManager roleManager;

    @Autowired
    private ResSystemResourceIntegration resSystemResourceIntegration;

    @Autowired
    private DictIntegration dictIntegration;
    @Autowired
    private AuthorizationSubjectModelManage authorizationSubjectModelManage;

    @Override
    public SysRoleResultVO findRoleByRoleId(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        SysRoleDO sysRoleDO = roleDao.getById(id);
        BusinessLogicException.checkTrue(Objects.isNull(sysRoleDO), PermissionErrorCodeEnums.ROLE_NOT_EXIST);

        RoleBasicDTO roleBasicDTO = OrikaUtil.map(sysRoleDO, RoleBasicDTO.class);
        roleBasicDTO.setRoleName(UserEvnHolder.getLocal().equals(Locale.US) ? sysRoleDO.getRoleNameEn() : sysRoleDO.getRoleName());
        roleBasicDTO.setDescription(UserEvnHolder.getLocal().equals(Locale.US) ? sysRoleDO.getDescriptionEn() : sysRoleDO.getDescription());
        String multipleSystem = sysRoleDO.getMultipleSystem();
        if (StringUtils.isNotBlank(multipleSystem)) {
            roleBasicDTO.setSystemCodeList(JSONArray.parseArray(multipleSystem, String.class));
        }
        if (StringUtils.isNotBlank(sysRoleDO.getRoleCountry())) {
            roleBasicDTO.setRoleCountryList(JSON.parseArray(sysRoleDO.getRoleCountry(), String.class));
        }

        // 权限数据
        PermissionDTO permissionDTO = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(sysRoleDO.getId());
        SysRoleResultVO sysRoleResultVO = new SysRoleResultVO();
        sysRoleResultVO.setId(id);
        sysRoleResultVO.setRoleBasicDTO(roleBasicDTO);
        sysRoleResultVO.setIsDisable(sysRoleDO.getIsDisable());
        sysRoleResultVO.setMenuPermissionDTOList(permissionDTO.getMenuPermissionDTOList());
        List<DataPermissionDTO> dataPermissionDTOList = permissionDTO.getDataPermissionDTOList();
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            List<String> typeCodeList = dataPermissionDTOList.stream()
                    .map(DataPermissionDTO::getTypeCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(typeCodeList)) {
                List<PermissionBaseTypeVO> typeList = dataPermissionDAO.typeList(typeCodeList);
                Map<String, String> typeCodeToTypeNameMap = typeList.stream()
                        .collect(Collectors.toMap(PermissionBaseTypeVO::getTypeCode, PermissionBaseTypeVO::getTypeName));
                dataPermissionDTOList.forEach(
                        e -> e.setTypeName(typeCodeToTypeNameMap.getOrDefault(e.getTypeCode(), BusinessConstant.EMPTY_TYPE_NAME))
                );
            }
        }
        sysRoleResultVO.setDataPermissionDTOList(dataPermissionDTOList);
        return sysRoleResultVO;
    }


    @Override
    public PaginationResult<RolePageVO> findRolePage(RolePageQueryParam rolePageQueryParam) {
        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();

        // 获取管理员身份
        AdminDTO adminDTO = adminManage.getAdminDTO(userInfo.getUserCode());

        // 非管理员处理
        if (Objects.isNull(adminDTO)) {
            return PageUtil.emptyPageResult(rolePageQueryParam);
        }


        // 拼装 query
        SysRoleQuery query = OrikaUtil.map(rolePageQueryParam, SysRoleQuery.class);
        query.setMultipleSystemList(Lists.newArrayList(rolePageQueryParam.getSystemCode()));
        query.setRoleName(rolePageQueryParam.getRoleName());


        // 岗位与角色筛选
        List<Long> postIdList = rolePageQueryParam.getPostIdList();
        if (CollectionUtils.isNotEmpty(postIdList)) {
            List<Long> roleIdList = new ArrayList<>();
            List<Long> postBindingRole = enforcerManage.getPostBindingRole(postIdList);
            roleIdList.addAll(postBindingRole);
            if (CollectionUtils.isEmpty(roleIdList)) {
                return PageUtil.emptyPageResult(rolePageQueryParam);
            }
        }
        // 系统参与角色筛选
        List<String> systemPlatformList = rolePageQueryParam.getSystemPlatformList();
        if (CollectionUtils.isNotEmpty(systemPlatformList)) {
            systemPlatformList = systemPlatformList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            query.setSystemPlatformList(systemPlatformList);
        }

        // 执行 查询
        // 子管理员系统限制逻辑
        if (adminDTO.getIsSysSubAdmin()) {
            List<String> systemList = adminDTO.getSystemList();
            // 无系统权限
            if (CollectionUtils.isEmpty(systemList)) {
                return PageUtil.emptyPageResult(rolePageQueryParam);
            }

            // 全部系统 Code
            List<String> systemCodeList = permissionSystemManage.getSystemCodeList();
            List<String> system = authorizationSubjectModelManage.getSystemBySubjectModelCodeAndSystem("role", systemList);

            // 计算当前子管理员未拥有的权限
            Collection<String> nonOwnedSystemCodeList = CollectionUtils.removeAll(systemCodeList, system);
            if (CollectionUtils.isNotEmpty(nonOwnedSystemCodeList)) {
                // 设置 非系统条件
                query.setNonOwnedSystemCodeList(Lists.newArrayList(nonOwnedSystemCodeList));
            }
        }

        // 名称与 ID 模糊查询
        String roleNameOrId = rolePageQueryParam.getRoleNameOrId();
        if (StringUtils.isNotBlank(roleNameOrId)) {
            if (roleNameOrId.matches("\\d+")) {
                Long roleId = Long.valueOf(roleNameOrId);
                query.setId(roleId);
            } else {
                query.setRoleName(roleNameOrId);
            }
        }

        PageInfo<SysRoleDO> pageInfo = roleDao.selectPage(query);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PageUtil.emptyPageResult(query, pageInfo);
        }

        //  国际化处理
        List<RolePageVO> rolePageList = mapRoleDOListToRolePageVOList(pageInfo.getList());

        List<Long> roleIdList = rolePageList.stream().map(RolePageVO::getId).collect(Collectors.toList());

        Map<Long, Integer> map = refactoringPermissionCasbinRuleManage.getRoleBindingUserCountMap(roleIdList);
        Map<Long, Integer> rolePostMap = refactoringPermissionCasbinRuleManage.getRoleBindingPostCountMap(roleIdList);
        List<SysRoleDO> childRoleList = roleDao.listChildRoleByParentId(roleIdList);
        List<RolePageVO> childRoleVO = mapRoleDOListToRolePageVOList(childRoleList);
        Map<Long, RolePageVO> childMap = childRoleVO.stream()
                .filter(e -> Objects.nonNull(e.getParentId()))
                .collect(Collectors.toMap(RolePageVO::getParentId, Function.identity(), (k1, k2) -> k1));
        Map<Long, Integer> clientCountMap = clientRoleAuthorityManage.countRoleMap(roleIdList);

        rolePageList.forEach(
                e -> {
                    Integer count = map.getOrDefault(e.getId(), BusinessConstant.ZERO);
                    Integer postCount = rolePostMap.getOrDefault(e.getId(), BusinessConstant.ZERO);
                    e.setUserCount(count);
                    e.setRoleBindPostCount(postCount);

                    Integer clientCount = clientCountMap.getOrDefault(e.getId(), 0);
                    e.setBoundClientCount(clientCount);
                    if (RoleTypeEnum.DEFAULT_ROLE.getCode().equals(e.getRoleType())) {
                        e.setUserCount(null);
                        e.setRoleBindPostCount(null);
                        e.setBoundClientCount(null);
                    }
                    RolePageVO childRole = childMap.get(e.getId());
                    if (Objects.nonNull(childRole)) {
                        childRole.setSystemCodeList(e.getSystemCodeList());
                        childRole.setUserCount(e.getUserCount());
                        childRole.setRoleBindPostCount(e.getRoleBindPostCount());
                        e.setBoundClientCount(e.getBoundClientCount());
                        e.setChildRole(childRole);
                    }
                }
        );


        return PageUtil.getPageResult(rolePageList, query, pageInfo);
    }

    @Override
    public void roleNameExist(Long roleId, String roleName, String roleNameEn) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLE_NAME_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLE_NAME_NOT_NULL);
        // 角色名称重复校验
        BusinessLogicException.checkTrue(
                roleDao.selectRoleNameCount(roleName, roleId) > BusinessConstant.ZERO,
                PermissionErrorCodeEnums.ROLE_NAME_EXIST);
        BusinessLogicException.checkTrue(
                roleDao.selectRoleNameEnCount(roleNameEn, roleId) > BusinessConstant.ZERO,
                PermissionErrorCodeEnums.ROLE_NAME_EN_EXIST);
    }


    @Override
    public Boolean deleteRole(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        roleDao.delete(id);
        enforcerManage.deleteByRole(id);
        return true;
    }

    @Override
    public List<RolePageVO> findRoleList(RolePageQueryParam rolePageQueryParam) {
        SysRoleQuery sysRoleQuery = OrikaUtil.map(rolePageQueryParam, SysRoleQuery.class);
        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();
        String userCode = userInfo.getUserCode();
        AdminDTO adminDTO = adminManage.getAdminDTO(userCode);

        // 非管理员处理
        if (Objects.isNull(adminDTO)) {
            return Collections.emptyList();
        }

        if (adminDTO.getIsSysSubAdmin()) {
            List<String> systemList = adminDTO.getSystemList();
            // 无系统权限
            if (CollectionUtils.isEmpty(systemList)) {
                return Collections.emptyList();
            }

            // 全部系统 Code
            List<String> systemCodeList = permissionSystemManage.getSystemCodeList();

            // 计算当前子管理员未拥有的权限
            Collection<String> nonOwnedSystemCodeList = CollectionUtils.removeAll(systemCodeList, systemList);
            if (CollectionUtils.isNotEmpty(nonOwnedSystemCodeList)) {
                // 设置 非系统条件
                sysRoleQuery.setNonOwnedSystemCodeList(Lists.newArrayList(nonOwnedSystemCodeList));
            }
        }
        List<RolePageVO> rolePageVOS = mapRoleDOListToRolePageVOList(roleDao.select(sysRoleQuery));
        List<Long> sensitiveIdList = resSystemResourceIntegration.getSensitiveIdList(sysRoleQuery.getSystemCode());
        if (CollectionUtils.isNotEmpty(sensitiveIdList)) {
            List<Long> sensitiveRoleIds = refactoringPermissionCasbinRuleManage.getRoleIdListByMenuIdList(sensitiveIdList);
            if (CollectionUtils.isNotEmpty(sensitiveRoleIds)) {
                for (RolePageVO role : rolePageVOS) {
                    if (sensitiveRoleIds.contains(role.getId())) {
                        role.setSensitiveLevel(1);
                    } else {
                        role.setSensitiveLevel(0);
                    }
                }
            }
        }
        return rolePageVOS;
    }

    private List<RolePageVO> mapRoleDOListToRolePageVOList(List<SysRoleDO> roleDOList) {
        return roleDOList.stream().map(SysRoleConvert::convertRolePageVO).collect(Collectors.toList());
    }

    @Override
    public List<String> getRoleDataCodeByTypeCode(Long roleId, String typeCode) {
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_ID_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);
        return enforcerManage.getRoleDataCodeByTypeCode(roleId, typeCode);
    }

    @Override
    public List<DataPermissionDTO> getRoleDataPermission(Long roleId) {
        PermissionDTO permissionDTO = enforcerManage.getPermissionByRoleId(roleId);
        return permissionDTO.getDataPermissionDTOList();
    }

    /**
     * 保存数据权限
     */
    @Override
    public void savePermission(Long roleId, List<MenuPermissionDTO> menuPermissionList, List<DataPermissionDTO> dataPermissionDTOList) {
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ID_NOT_NULL);
        SysRoleDO sysRoleDO = roleDao.getById(roleId);
        BusinessLogicException.checkTrue(Objects.isNull(sysRoleDO), PermissionErrorCodeEnums.ROLE_IS_NULL);
        // casbin 角色的 菜单、数据权限
        refactoringPermissionCasbinRuleManage.saveRolePermission(roleId, menuPermissionList, dataPermissionDTOList);
        RoleCacheDTO roleCacheDTO = RoleCacheConvert.permissionRoleCacheConvert(
                roleId, menuPermissionList, dataPermissionDTOList, sysRoleDO);
        roleCacheManage.addRoleCache(roleCacheDTO);
    }

    @Override
    public List<Long> getSystemFilterRole(Collection<String> nonOwnedSystemCodeList, List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(nonOwnedSystemCodeList)) {
            return roleIdList;
        }
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        SysRoleQuery sysRoleQuery = new SysRoleQuery();

        sysRoleQuery.setIdList(roleIdList);
        sysRoleQuery.setNonOwnedSystemCodeList(nonOwnedSystemCodeList);
        return roleDao.select(sysRoleQuery)
                .stream()
                .map(SysRoleDO::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysRoleDO> listById(List<Long> newBindingRoleIdList) {
        if (CollectionUtils.isEmpty(newBindingRoleIdList)) {
            return Collections.emptyList();
        }
        return roleDao.listByIds(newBindingRoleIdList);
    }

    @Override
    public List<RolePageVO> allList(RolePageQueryParam rolePageQueryParam) {
        SysRoleQuery sysRoleQuery = OrikaUtil.map(rolePageQueryParam, SysRoleQuery.class);
        String systemCode = rolePageQueryParam.getSystemCode();
        if (StringUtils.isNotBlank(systemCode)) {
            sysRoleQuery.setSystemCode(systemCode);
        }
        List<RolePageVO> rolePageVOS = mapRoleDOListToRolePageVOList(roleDao.select(sysRoleQuery));
        List<Long> sensitiveIdList = resSystemResourceIntegration.getSensitiveIdList(sysRoleQuery.getSystemCode());
        if (CollectionUtils.isNotEmpty(sensitiveIdList)) {
            List<Long> sensitiveRoleIds = refactoringPermissionCasbinRuleManage.getRoleIdListByMenuIdList(sensitiveIdList);
            if (CollectionUtils.isNotEmpty(sensitiveRoleIds)) {
                for (RolePageVO role : rolePageVOS) {
                    if (sensitiveRoleIds.contains(role.getId())) {
                        role.setSensitiveLevel(1);
                    } else {
                        role.setSensitiveLevel(0);
                    }
                }
            }
        }
        return rolePageVOS;
    }


    @Override
    public void saveRole(SysRoleDO role) {
        BusinessLogicException.checkTrue(Objects.isNull(role), PermissionErrorCodeEnums.ROLE_IS_NULL);
        roleDao.save(role);
    }

    @Override
    public SysPostRoleRelationDetailVO postRoleDetail(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);

        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();
        AdminDTO adminDTO = adminManage.getAdminDTO(userInfo.getUserCode());
        BusinessLogicException.checkTrue(Objects.isNull(adminDTO), PermissionErrorCodeEnums.DONT_ADMIN);

        SysPostRoleRelationDetailVO detailVO = new SysPostRoleRelationDetailVO();

        PostConditionParam param = new PostConditionParam();
        param.setPostCodeList(Arrays.asList(id.toString()));
        PaginationResult<PostDTO> postPage = hrmsPostIntegration.getPostPage(param);
        List<PostDTO> orgPostInfoApiList = postPage.getResults();

        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(orgPostInfoApiList), PermissionErrorCodeEnums.POST_NOT_EXIST);
        detailVO.setPostId(id);
        // 获取当前语言环境
        detailVO.setPostName(orgPostInfoApiList.get(BusinessConstant.ZERO).getPostName());
        List<RoleEditableVO> roleEditableVOList = Lists.newArrayList();
        // 员工岗位默认角色
        roleEditableVOList.addAll(getPostDefaultRole(UserEvnHolder.getLocal()));
        // 员工绑定的业务角色
        roleEditableVOList.addAll(getBusinessRole(id, UserEvnHolder.getLocal(), adminDTO));
        detailVO.setRoleList(roleEditableVOList);
        return detailVO;
    }

    private Collection<? extends RoleEditableVO> getBusinessRole(Long id, Locale locale, AdminDTO adminDTO) {
        // 绑定的员工业务角色
        List<Long> postBindingRole = refactoringPermissionCasbinRuleManage.getRoleIdByPostId(id);
        if (CollectionUtils.isEmpty(postBindingRole)) {
            return Lists.newArrayList();
        }
        SysRoleQuery roleQuery = new SysRoleQuery();
        roleQuery.setIdList(postBindingRole);
        List<SysRoleDO> sysRoleList = roleDao.select(roleQuery);
        if (CollectionUtils.isEmpty(sysRoleList)) {
            return Lists.newArrayList();
        }
        return sysRoleList.stream().map(role -> SysRoleConvert.convertRoleEditableVO(role, locale, adminDTO)).collect(Collectors.toList());
    }

    private Collection<? extends RoleEditableVO> getPostDefaultRole(Locale local) {
        // 员工默认角色
        List<SysRoleDTO> sysRoleDTOList = roleManager.listRoleByType(
                RoleTypeEnum.DEFAULT_ROLE.getCode(), RoleAuthSceneEnum.EMPLOYEE.getCode());
        // 过滤出来，授权给岗位的默认角色
        List<SysRoleDTO> postDefaultRoleList = Optional.ofNullable(sysRoleDTOList).orElse(Lists.newArrayList()).stream()
                .filter(role -> RoleApplyTypeEnum.POST.getCode().equals(role.getApplyType())).collect(Collectors.toList());
        return SysRoleConvert.convertRoleEditable(postDefaultRoleList, local);
    }

    @Override
    public PaginationResult<PostVO> findPostPage(PostQueryParam postQueryParam) {
        PostConditionParam param = new PostConditionParam();
        param.setCurrentPage(postQueryParam.getCurrentPage());
        param.setShowCount(postQueryParam.getShowCount());

        Long postId = postQueryParam.getPostId();
        List<Long> postIdList = postQueryParam.getPostIdList();

        List<Long> allPostIdList = new ArrayList<>();

        if (Objects.nonNull(postId)) {
            allPostIdList.add(postId);
        }

        if (CollectionUtils.isNotEmpty(postIdList)) {
            allPostIdList.addAll(postIdList);
        }

        if (CollectionUtils.isNotEmpty(allPostIdList)) {
            param.setPostCodeList(allPostIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        param.setPostName(postQueryParam.getPostName());
        param.setStatus(postQueryParam.getStatus());

        PaginationResult<PostDTO> postPage = hrmsPostIntegration.getPostPage(param);
        List<PostDTO> results = postPage.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return PageUtil.emptyPageResult(postQueryParam, postPage.getPagination());
        }
        List<PostVO> resultList = postDTOToPostVOS(results);
        return PageUtil.getPageResult(resultList, postQueryParam, postPage.getPagination());
    }


    @Override
    public Boolean batchBindingRoleForPost(BatchBindingParam batchBindingParam) {
        List<Long> roleIdList = batchBindingParam.getRoleIdList();
        List<Long> postIdList = batchBindingParam.getPostIdList();
        if (CollectionUtils.isNotEmpty(postIdList)) {
            for (Long postId : postIdList) {
                postCacheManage.bindingRoleToPostByCache(roleIdList, postId);
            }
        }
        return true;
    }


    @Override
    public List<PostVO> findPostList(PostQueryParam postQueryParam) {
        PostConditionParam param = new PostConditionParam();
        param.setShowCount(1000);

        Long postId = postQueryParam.getPostId();
        List<Long> postIdList = postQueryParam.getPostIdList();

        List<Long> allPostIdList = new ArrayList<>();

        if (Objects.nonNull(postId)) {
            allPostIdList.add(postId);
        }

        if (CollectionUtils.isNotEmpty(postIdList)) {
            allPostIdList.addAll(postIdList);
        }

        if (CollectionUtils.isNotEmpty(allPostIdList)) {
            param.setPostCodeList(allPostIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        param.setPostName(postQueryParam.getPostName());
        param.setStatus(postQueryParam.getStatus());


        PaginationResult<PostDTO> postPage = hrmsPostIntegration.getPostPage(param);
        List<PostDTO> results = postPage.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return Collections.EMPTY_LIST;
        }
        List<PostVO> list = new ArrayList<>();
        for (PostDTO apiDTO : results) {
            PostVO postVO = new PostVO();
            postVO.setPostId(Long.valueOf(apiDTO.getPostCode()));
            postVO.setPostName(apiDTO.getPostName());
            // 不分页情况下，不直接查询角色关联数量，设置默认为 0
            postVO.setRoleCount(0);
            list.add(postVO);
        }

        return list;
    }


    @Override
    public void updateDO(SysRoleDO role) {
        roleDao.updateDO(role);
    }

    @Override
    public SysRoleDO getRoleDOById(Long roleId) {
        return roleDao.getById(roleId);
    }

    @Override
    public List<SysRoleDO> selectRoleDOList(SysRoleQuery roleQuery) {
        if (Objects.isNull(roleQuery)) {
            return Collections.emptyList();
        }
        return roleDao.select(roleQuery);
    }

    @Override
    public PageInfo<SysRoleDO> selectRoleDOPage(SysRoleQuery roleQuery) {
        return roleDao.selectPage(roleQuery);
    }

    @Override
    public Long rpcAddRole(RoleAddApiDTO roleAddApiDTO) {
        SysRoleDO role = OrikaUtil.map(roleAddApiDTO, SysRoleDO.class);
        // 英文环境
        role.setRoleNameEn(roleAddApiDTO.getRoleName());
        role.setDescriptionEn(roleAddApiDTO.getDescription());
        role.setRoleName(roleAddApiDTO.getRoleName());
        role.setDescription(roleAddApiDTO.getDescription());
        role.setRoleCountry(JSON.toJSONString(Lists.newArrayList(BusinessConstant.DEFAULT_ROLE_COUNTRY)));
        BusinessLogicException.checkTrue(
                roleDao.selectRoleNameCount(roleAddApiDTO.getRoleName(), null) > BusinessConstant.ZERO,
                PermissionErrorCodeEnums.ROLE_EXIST);
        BusinessLogicException.checkTrue(
                roleDao.selectRoleNameEnCount(roleAddApiDTO.getRoleName(), null) > BusinessConstant.ZERO,
                PermissionErrorCodeEnums.ROLE_EXIST);

        List<MenuPermissionApiDTO> menuPermissionApiDTOListDTOList = roleAddApiDTO.getMenuPermissionDTOList();
        List<MenuPermissionDTO> menuPermissionDTOList = OrikaUtil.mapAsList(menuPermissionApiDTOListDTOList, MenuPermissionDTO.class);
        if (CollectionUtils.isNotEmpty(menuPermissionDTOList)) {
            List<String> systemPlatformList = menuPermissionDTOList.stream()
                    .map(MenuPermissionDTO::getSystemPlatform)
                    .collect(Collectors.toList());
            if (PermissionCollectionUtils.isNotEmpty(systemPlatformList, StringUtils::isNotBlank)) {
                systemPlatformList = systemPlatformList.stream()
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                role.setSystemPlatformJson(JSONArray.toJSONString(systemPlatformList));
            }
        } else {
            menuPermissionDTOList = Collections.emptyList();
        }

        if (StringUtils.isNotBlank(role.getSystemPlatformJson())) {
            role.setMultipleSystem(role.getSystemPlatformJson());
        }

        roleDao.save(role);
        Long roleId = role.getId();

        List<DataPermissionApiDTO> dataPermissionApiDTOList = roleAddApiDTO.getDataPermissionDTOList();
        List<DataPermissionDTO> dataPermissionDTOList = OrikaUtil.mapAsList(dataPermissionApiDTOList, DataPermissionDTO.class);
        savePermission(roleId, menuPermissionDTOList, dataPermissionDTOList);
        return roleId;
    }

    @Override
    public void rpcUpdateDO(SysRoleDO role) {
        roleDao.rpcUpdateDO(role);
    }

    @Override
    public PermissionVO getRoleImplicitPermission(Long roleId) {
        PermissionDTO permissionDTO = enforcerManage.getImplicitPermissionByRoleId(roleId);
        return OrikaUtil.map(permissionDTO, PermissionVO.class);
    }

    @Override
    public void updateRoleDisable(Long roleId, Integer isDisable) {
        roleDao.updateRoleDisable(roleId, isDisable);
    }

    @Override
    public List<RoleApiDTO> selectRoleByMenuId(Long menuId) {
        List<Long> roleIdList = enforcerManage.getRoleIdByMenuId(menuId);
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        SysRoleQuery sysRoleQuery = new SysRoleQuery();
        sysRoleQuery.setIdList(roleIdList);
        sysRoleQuery.setIsDisable(BusinessConstant.N);
        List<SysRoleDO> roleList = roleDao.select(sysRoleQuery);
        return OrikaUtil.mapAsList(roleList, RoleApiDTO.class);
    }


    @Override
    public SysRoleDO getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return roleDao.getById(id);
    }

    @Override
    public boolean updateDTO(SysRoleDTO dto) {
        return roleDao.updateDTO(dto);
    }

    @Override
    public Integer selectRoleNameCount(String roleName, Long roleId) {
        return roleDao.selectRoleNameCount(roleName, roleId);
    }

    @Override
    public Integer selectRoleNameEnCount(String roleNameEn, Long roleId) {
        return roleDao.selectRoleNameEnCount(roleNameEn, roleId);
    }

    @NotNull
    private List<PostVO> getPostVOS(List<EntPostApiDTO> relationList) {
        // rpc 获取 hr 岗位信息
        List<PostVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }
        BusinessLogicException.checkTrue(relationList.size() > 100, PermissionErrorCodeEnums.POST_ID_LENGTH_GREATER_THAN_100);
        List<Long> postIdList = relationList.stream().map(EntPostApiDTO::getId).collect(Collectors.toList());
        Map<Long, List<Long>> postMap = refactoringPermissionCasbinRuleManage.getPostBindingRoleList(postIdList);
        // 获取员工默认角色
        List<SysRoleDTO> defaultRoleList = roleManager.listRoleByType(RoleTypeEnum.DEFAULT_ROLE.getCode(), RoleAuthSceneEnum.EMPLOYEE.getCode());
        // 过滤岗位角色
        long count = defaultRoleList.stream()
                .filter(
                        role -> RoleApplyTypeEnum.POST.getCode().equals(role.getApplyType())
                )
                .count();

        for (EntPostApiDTO apiDTO : relationList) {
            PostVO postVO = new PostVO();
            postVO.setPostId(apiDTO.getId());
            postVO.setPostName(RequestInfoHolder.isChinese() ? apiDTO.getPostNameCn() : apiDTO.getPostNameEn());
            postVO.setStatus(apiDTO.getStatus());
            List<Long> postBindingRole = postMap.getOrDefault(apiDTO.getId(), Collections.emptyList());
            postVO.setRoleCount((int) (postBindingRole.size() + count));
            resultList.add(postVO);
        }
        return resultList;
    }


    private List<PostVO> postDTOToPostVOS(List<PostDTO> relationList) {
        // rpc 获取 hr 岗位信息
        List<PostVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }
        BusinessLogicException.checkTrue(relationList.size() > 100, PermissionErrorCodeEnums.POST_ID_LENGTH_GREATER_THAN_100);
        List<Long> postIdList = relationList.stream().map(e -> Long.valueOf(e.getPostCode())).collect(Collectors.toList());
        Map<Long, List<Long>> postMap = refactoringPermissionCasbinRuleManage.getPostBindingRoleList(postIdList);
        // 获取员工默认角色
        List<SysRoleDTO> defaultRoleList = roleManager.listRoleByType(RoleTypeEnum.DEFAULT_ROLE.getCode(), RoleAuthSceneEnum.EMPLOYEE.getCode());
        // 过滤岗位角色
        long count = defaultRoleList.stream()
                .filter(
                        role -> RoleApplyTypeEnum.POST.getCode().equals(role.getApplyType())
                )
                .count();

        for (PostDTO apiDTO : relationList) {
            PostVO postVO = new PostVO();
            postVO.setPostId(Long.valueOf(apiDTO.getPostCode()));
            postVO.setPostName(apiDTO.getPostName());
            postVO.setStatus(apiDTO.getStatus());
            List<Long> postBindingRole = postMap.getOrDefault(Long.valueOf(apiDTO.getPostCode()), Collections.emptyList());
            postVO.setRoleCount((int) (postBindingRole.size() + count));
            resultList.add(postVO);
        }
        return resultList;
    }

    @Override
    public EffectiveLossCountVO getRoleCount(Long postId) {
        if (Objects.isNull(postId)) {
            return null;
        }

        List<PostRoleOperationCollectionDTO> list = postRoleOperationCollectDAO.getRoleCount(postId);
        Map<String, Long> map = list.stream().collect(Collectors.toMap(PostRoleOperationCollectionDTO::getOperationType, PostRoleOperationCollectionDTO::getCount));
        Long binding = map.getOrDefault(PostRoleOperationTypeEnum.BINDING.getCode(), 0L);
        Long unbinding = map.getOrDefault(PostRoleOperationTypeEnum.UNBINDING.getCode(), 0L);

        EffectiveLossCountVO vo = new EffectiveLossCountVO();
        vo.setLoseCount(unbinding.intValue());
        vo.setEffectiveCount(binding.intValue());

        return vo;
    }

    @Override
    public PaginationResult<PostRoleDTO> getRolePage(PostRolePageQuery pageQuery) {
        PageInfo<PostRoleDTO> pageInfo = PermissionPageHelper.startPage(pageQuery)
                .doSelectPageInfo(() -> postRoleOperationCollectDAO.getRolePage(pageQuery));
        // 角色涉敏判断
        List<Long> sensitiveIdList = resSystemResourceIntegration.getSensitiveIdList(null);
        if (CollectionUtils.isNotEmpty(sensitiveIdList)) {
            List<Long> sensitiveRoleIds = refactoringPermissionCasbinRuleManage.getRoleIdListByMenuIdList(sensitiveIdList);
            if (CollectionUtils.isNotEmpty(sensitiveRoleIds)) {
                for (PostRoleDTO role : pageInfo.getList()) {
                    if (sensitiveRoleIds.contains(role.getRoleId())) {
                        role.setSensitiveLevel(1);
                    } else {
                        role.setSensitiveLevel(0);
                    }
                }
            }
        }
        return PageUtil.getPageResult(pageInfo.getList(), pageQuery, pageInfo);
    }

    @Override
    public List<PostRoleDetailsDTO> getRoleDetails(Long roleId, Long postId) {
        if (Objects.isNull(roleId) || Objects.isNull(postId)) {
            return Collections.emptyList();
        }
        return postRoleOperationDetailDAO.getRoleDetails(roleId, postId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPermissionAndRule(RoleAddPermissionRuleParam roleAddPermissionRuleParam) {
        // 新增角色
        RoleBasicParam roleBasicParam = roleAddPermissionRuleParam.getRoleBasicParam();
        // 参数校验
        BusinessLogicException.check(Objects.nonNull(roleBasicParam.getRoleName()), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.check(Objects.nonNull(roleBasicParam.getRoleNameEn()), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);
        // 角色名称重复校验
        BusinessLogicException.checkTrue(
                roleDao.selectRoleNameCount(roleBasicParam.getRoleName(), null) > BusinessConstant.ZERO,
                PermissionErrorCodeEnums.ROLE_NAME_EXIST);
        BusinessLogicException.checkTrue(
                roleDao.selectRoleNameEnCount(roleBasicParam.getRoleNameEn(), null) > BusinessConstant.ZERO,
                PermissionErrorCodeEnums.ROLE_NAME_EN_EXIST);
        // 判断是否是超管，超管可以创建员工默认角色；
        checkDefaultRoleCanAdd(roleBasicParam, UserInfoUtil.getUserInfo());

        // 商家角色信息校验
        clientRoleCheck(roleBasicParam);

        // 主数据
        List<DataPermissionRuleDTO> mainDataPermissionDTO = Optional.ofNullable(roleAddPermissionRuleParam.getMainDataPermissionDTO()).orElse(Lists.newArrayList());
        // 动态函数
        List<DataPermissionRuleDTO> dynamicDataPermissionDTO = Optional.ofNullable(roleAddPermissionRuleParam.getDynamicDataPermissionDTO()).orElse(Lists.newArrayList());
        // 基础数据
        List<DataPermissionRuleDTO> baseDataPermissionDTO = Optional.ofNullable(roleAddPermissionRuleParam.getBaseDataPermissionDTO()).orElse(Lists.newArrayList());
        // 菜单权限
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(roleAddPermissionRuleParam.getMenuPermissionDTOList()).orElse(Collections.emptyList());
        // 校验主数据、动态函数权限 必填字段校验
        checkMainDataAndDynamicData(mainDataPermissionDTO, dynamicDataPermissionDTO);

        List<DataPermissionRuleDTO> singleDynamicDataConfigValueDTO = Optional.ofNullable(roleAddPermissionRuleParam.getSingleDynamicDataConfigValueDTO()).orElse(Lists.newArrayList());
        List<MultiDynamicDataConfigValueDTO> multiDynamicDataConfigValueDTO = Optional.ofNullable(roleAddPermissionRuleParam.getMultiDynamicDataConfigValueDTO()).orElse(Lists.newArrayList());

        checkSpecificRuleConfigVO(singleDynamicDataConfigValueDTO);

        // 保存角色
        SysRoleDO sysRoleDO = SysRoleConvert.convertAddRoleDO(roleAddPermissionRuleParam);
        if (Objects.isNull(sysRoleDO)) {
            throw BusinessException.ofI18nCode(PermissionErrorCodeEnums.ROLE_NOT_NULL.getCode(), PermissionErrorCodeEnums.ROLE_NOT_NULL.getDesc());
        }
        roleDao.save(sysRoleDO);

        // 商家子角色保存
        if (RoleAuthSceneEnum.CLIENT.getCode().equals(sysRoleDO.getAuthScene())) {
            SysRoleDO childRole = SysRoleConvert.getChildRole(sysRoleDO);
            roleDao.save(childRole);
        }
        // 保存菜单权限
        Long roleId = sysRoleDO.getId();

        DataPermissionDTO selectAllDataPermissionDTO = new DataPermissionDTO();
        selectAllDataPermissionDTO.setTypeCode(BusinessConstant.SELECT_ALL_TYPE_CODE);
        List<String> selectAllCodeList = Lists.newArrayList();
        selectAllDataPermissionDTO.setDataCodeList(selectAllCodeList);

        List<DataPermissionDTO> dataPermissionDTOList = new ArrayList<>();
        List<PermissionAuthorizationRuleDO> ruleList = new ArrayList<>();
        baseDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            List<String> dataCodeList = e.getDataCodeList();
                            if (CollectionUtils.isNotEmpty(dataCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode(e.getTypeCode());
                                dataPermissionDTO.setTypeName(e.getTypeName());
                                dataPermissionDTO.setDataCodeList(dataCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }
                        }
                );

        mainDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            String typeCode = e.getTypeCode();
                            String typeName = e.getTypeName();
                            List<String> dataCodeList = e.getDataCodeList();
                            List<DataCodeExtensionTagDTO> paramTagList = e.getDataExtensionTagList();
                            if (CollectionUtils.isEmpty(paramTagList)) {
                                paramTagList = Lists.newArrayList();
                                e.setDataExtensionTagList(paramTagList);
                            }
                            List<String> functionCodeList = e.getFunctionCodeList();
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();

                            // 全选
                            if (e.getSelectAll() != null && e.getSelectAll() == 1) {
                                selectAllCodeList.add(e.getTypeCode());
                            }

                            List<String> extDataCodes = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(paramTagList)) {
                                extDataCodes.addAll(paramTagList.stream().map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                            }
                            if (CollectionUtils.isNotEmpty(dataCodeList)) {
                                dataCodeList.removeAll(extDataCodes);
                                for (String code : dataCodeList) {
                                    DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                                    tag.setDataCode(code);
                                    tag.setExtensionTagCodeList(Lists.newArrayList());
                                    paramTagList.add(tag);
                                }
                            }

                            if (CollectionUtils.isNotEmpty(paramTagList) || Objects.equals(e.getSelectAll(), 1)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode(typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataTagList(paramTagList);
                                dataPermissionDTO.setSelectAll(e.getSelectAll());
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(functionCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode("$" + typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataCodeList(functionCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = new PermissionAuthorizationRuleDO();
                                permissionAuthorizationRuleDO.setAuthorizationType(BusinessConstant.ROLE);
                                permissionAuthorizationRuleDO.setAuthorizationRoleId(roleId);
                                permissionAuthorizationRuleDO.setRelationType(BusinessConstant.MAINDATA);
                                permissionAuthorizationRuleDO.setRelationTypeCode(typeCode);
                                permissionAuthorizationRuleDO.setAuthorizationRuleJson(JSON.toJSONString(specificRuleConfigList));
                                ruleList.add(permissionAuthorizationRuleDO);
                            }
                        }
                );
        singleDynamicDataConfigValueDTO.stream()
                .forEach(
                        e -> {
                            String typeCode = e.getTypeCode();
                            String typeName = e.getTypeName();
                            List<String> dataCodeList = e.getDataCodeList();
                            List<DataCodeExtensionTagDTO> paramTagList = e.getDataExtensionTagList();
                            if (CollectionUtils.isEmpty(paramTagList)) {
                                paramTagList = Lists.newArrayList();
                                e.setDataExtensionTagList(paramTagList);
                            }
                            List<String> functionCodeList = e.getFunctionCodeList();
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();

                            // 全选
                            if (e.getSelectAll() != null && e.getSelectAll() == 1) {
                                selectAllCodeList.add(e.getTypeCode());
                            }

                            List<String> extDataCodes = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(paramTagList)) {
                                extDataCodes.addAll(paramTagList.stream().map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                            }
                            if (CollectionUtils.isNotEmpty(dataCodeList)) {
                                dataCodeList.removeAll(extDataCodes);
                                for (String code : dataCodeList) {
                                    DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                                    tag.setDataCode(code);
                                    tag.setExtensionTagCodeList(Lists.newArrayList());
                                    paramTagList.add(tag);
                                }
                            }

                            if (CollectionUtils.isNotEmpty(paramTagList) || Objects.equals(e.getSelectAll(), 1)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode(typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataTagList(paramTagList);
                                dataPermissionDTO.setSelectAll(e.getSelectAll());
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(functionCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode("$" + typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataCodeList(functionCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = new PermissionAuthorizationRuleDO();
                                permissionAuthorizationRuleDO.setAuthorizationType(BusinessConstant.ROLE);
                                permissionAuthorizationRuleDO.setAuthorizationRoleId(roleId);
                                permissionAuthorizationRuleDO.setRelationType(BusinessConstant.SINGLEDIMENSION);
                                permissionAuthorizationRuleDO.setRelationTypeCode(typeCode);
                                permissionAuthorizationRuleDO.setAuthorizationRuleJson(JSON.toJSONString(specificRuleConfigList));
                                ruleList.add(permissionAuthorizationRuleDO);
                            }
                        }
                );

        for (MultiDynamicDataConfigValueDTO dto : multiDynamicDataConfigValueDTO) {
            String typeCodeDynamic = dto.getTypeCode();
            String typeNameDynamic = dto.getTypeName();
            List<DimensionDataPermissionRuleDTO> dimensionConfigList = Optional.ofNullable(dto.getDimensionConfigList()).orElse(Lists.newArrayList());
            for (DimensionDataPermissionRuleDTO e : dimensionConfigList) {
                String typeCode = typeCodeDynamic + ">" + e.getTypeCode();
                String typeName = typeNameDynamic + ">" + e.getTypeName();
                // 全选
                if (e.getSelectAll() != null && e.getSelectAll() == 1) {
                    selectAllCodeList.add(typeCode);
                }
                List<String> dataCodeList = e.getDataCodeList();
                List<DataCodeExtensionTagDTO> paramTagList = e.getDataExtensionTagList();
                if (CollectionUtils.isEmpty(paramTagList)) {
                    paramTagList = Lists.newArrayList();
                    e.setDataExtensionTagList(paramTagList);
                }

                List<String> extDataCodes = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(paramTagList)) {
                    extDataCodes.addAll(paramTagList.stream().map(DataCodeExtensionTagDTO::getDataCode).collect(Collectors.toList()));
                }

                if (CollectionUtils.isNotEmpty(dataCodeList)) {
                    dataCodeList.removeAll(extDataCodes);
                    for (String code : dataCodeList) {
                        DataCodeExtensionTagDTO tag = new DataCodeExtensionTagDTO();
                        tag.setDataCode(code);
                        tag.setExtensionTagCodeList(Lists.newArrayList());
                        paramTagList.add(tag);
                    }
                }

                if (CollectionUtils.isNotEmpty(paramTagList) || Objects.equals(e.getSelectAll(), 1)) {
                    DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                    dataPermissionDTO.setTypeCode(typeCode);
                    dataPermissionDTO.setTypeName(typeName);
                    dataPermissionDTO.setDataTagList(paramTagList);
                    dataPermissionDTO.setSelectAll(e.getSelectAll());
                    dataPermissionDTOList.add(dataPermissionDTO);
                }

            }

        }

        dynamicDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            String typeCode = e.getTypeCode();
                            String typeName = e.getTypeName();
                            List<String> functionCodeList = e.getFunctionCodeList();
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();


                            if (CollectionUtils.isNotEmpty(functionCodeList)) {
                                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                                dataPermissionDTO.setTypeCode("$" + typeCode);
                                dataPermissionDTO.setTypeName(typeName);
                                dataPermissionDTO.setDataCodeList(functionCodeList);
                                dataPermissionDTOList.add(dataPermissionDTO);
                            }

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                PermissionAuthorizationRuleDO permissionAuthorizationRuleDO = new PermissionAuthorizationRuleDO();
                                permissionAuthorizationRuleDO.setAuthorizationType(BusinessConstant.ROLE);
                                permissionAuthorizationRuleDO.setAuthorizationRoleId(roleId);
                                permissionAuthorizationRuleDO.setRelationType(BusinessConstant.FUNCTIONDATA);
                                permissionAuthorizationRuleDO.setRelationTypeCode(typeCode);
                                permissionAuthorizationRuleDO.setAuthorizationRuleJson(JSON.toJSONString(specificRuleConfigList));
                                ruleList.add(permissionAuthorizationRuleDO);
                            }
                        }
                );
        // 全选处理
        if (CollectionUtils.isNotEmpty(selectAllCodeList)) {
            dataPermissionDTOList.add(selectAllDataPermissionDTO);
        }

        savePermission(roleId, menuPermissionDTOList, dataPermissionDTOList);
        dataPermissionManage.savePermissionAuthorizationRule(ruleList);
    }

    private void checkSpecificRuleConfigVO(List<DataPermissionRuleDTO> singleDynamicDataConfigValueDTO) {
        if (CollectionUtils.isNotEmpty(singleDynamicDataConfigValueDTO)) {
            singleDynamicDataConfigValueDTO.stream()
                    .forEach(
                            e -> {
                                List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();

                                if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                    specificRuleConfigList.stream().forEach(
                                            SpecificRuleConfigVO::check
                                    );
                                }
                            }
                    );
        }
    }

    private void clientRoleCheck(RoleBasicParam roleBasicParam) {
        Integer authScene = roleBasicParam.getAuthScene();
        BusinessLogicException.checkTrue(Objects.isNull(authScene), PermissionErrorCodeEnums.AUTH_SCENE_IS_NULL);
        if (!RoleAuthSceneEnum.CLIENT.getCode().equals(authScene)) {
            return;
        }
        Integer roleType = roleBasicParam.getRoleType();
        BusinessLogicException.checkTrue(Objects.isNull(roleType), PermissionErrorCodeEnums.ROLE_TYPE_IS_NULL);
        if (RoleTypeEnum.BUSINESS_ROLE.getCode().equals(roleType)) {
            return;
        }
        SysRoleDO clientDefaultRole = getClientDefaultRole();
        BusinessLogicException.checkTrue(Objects.nonNull(clientDefaultRole), PermissionErrorCodeEnums.CLIENT_DEFAULT_ROLE_EXIST);
    }

    private void checkMainDataAndDynamicData(List<DataPermissionRuleDTO> mainDataPermissionDTO, List<DataPermissionRuleDTO> dynamicDataPermissionDTO) {
        mainDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                specificRuleConfigList.stream().forEach(
                                        SpecificRuleConfigVO::check
                                );
                            }
                        }
                );
        dynamicDataPermissionDTO.stream()
                .forEach(
                        e -> {
                            List<SpecificRuleConfigVO> specificRuleConfigList = e.getSpecificRuleConfigList();

                            if (CollectionUtils.isNotEmpty(specificRuleConfigList)) {
                                specificRuleConfigList.stream().forEach(
                                        SpecificRuleConfigVO::check
                                );
                            }
                        }
                );
    }

    @Override
    public List<Long> getExistRoleIds(Collection<Long> roleIdSet) {
        if (CollectionUtils.isEmpty(roleIdSet)) {
            return Collections.emptyList();
        }
        return roleDao.getExistRoleIds(roleIdSet);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchBindingRoleMenu(List<RoleMenuBindApiDTO> roleMenuBindList) {
        BusinessLogicException.checkTrue(roleMenuBindList.size() > 50, PermissionErrorCodeEnums.ROLE_BATCH_SIZE_EXCEED);
        UserInfoDTO userInfo = UserInfoUtil.getUserInfo();
        BusinessLogicException.checkTrue(Objects.isNull(userInfo), PermissionErrorCodeEnums.USERINFODTO_IS_NULL);

        Map<Long, RoleMenuBindApiDTO> bindMap = roleMenuBindList.stream().collect(Collectors.toMap(RoleMenuBindApiDTO::getRoleId, Function.identity(), (v1, v2) -> v1));
        Set<Long> roleIdSet = roleMenuBindList.stream().map(RoleMenuBindApiDTO::getRoleId).collect(Collectors.toSet());
        List<SysRoleDO> existRoles = this.listById(Lists.newArrayList(roleIdSet));
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(existRoles), PermissionErrorCodeEnums.ROLE_IS_NULL);
        // 增量添加role menu
        List<List<String>> ruleList = Lists.newArrayList();
        List<Long> removeIdList = Lists.newArrayList();
        List<RoleCacheDTO> roleCacheDTOList = Lists.newArrayList();
        for (SysRoleDO entity : existRoles) {
            PermissionDTO permissionDTO = enforcerManage.getPermissionByRoleId(entity.getId());
            List<MenuPermissionDTO> menuPermissionDTOList = permissionDTO.getMenuPermissionDTOList();
            RoleMenuBindApiDTO bindApiDTO = bindMap.get(entity.getId());
            if (bindApiDTO == null) {
                continue;
            }
            List<String> systems = JSON.parseArray(entity.getMultipleSystem(), String.class);
            Set<String> existSystems = menuPermissionDTOList.stream().map(MenuPermissionDTO::getSystemPlatform).collect(Collectors.toSet());
            systems.removeAll(existSystems);
            if (CollectionUtils.isNotEmpty(systems)) {
                List<MenuPermissionDTO> newList = new ArrayList<>(menuPermissionDTOList);
                List<DictDataDTO> resourceSystem = dictIntegration.findDictDataByOrgIdAndTypeAndLang("ResourceSystem", EN_US);
                Map<String, DictDataDTO> resourceSystemMap = resourceSystem.stream().collect(Collectors.toMap(DictDataDTO::getDataValue, Function.identity(), (v1, v2) -> v1));
                for (String system : systems) {
                    MenuPermissionDTO dto = new MenuPermissionDTO();
                    dto.setMenuIdList(Lists.newArrayList());
                    dto.setPartiallyMenuIdList(Lists.newArrayList());
                    dto.setSystemPlatform(system);
                    DictDataDTO dictDataDTO = resourceSystemMap.get(system);
                    if (dictDataDTO == null) {
                        log.info("RolePostManageImpl | batchBindingRoleMenu | cannot find resource system : {}, resourceSystemMap: {}", system, resourceSystemMap);
                        continue;
                    }
                    dto.setMenuId(-dictDataDTO.getId());
                    newList.add(dto);
                }
                permissionDTO.setMenuPermissionDTOList(newList);
                menuPermissionDTOList = newList;
            }
            for (MenuPermissionDTO menuPermissionDTO : menuPermissionDTOList) {
                if (bindApiDTO.getSystem().equals(menuPermissionDTO.getSystemPlatform())) {
                    List<Long> menuIdList = menuPermissionDTO.getMenuIdList();
                    if (menuIdList == null) {
                        menuIdList = Lists.newArrayList();
                    }
                    menuIdList.addAll(bindApiDTO.getMenuIdList());
                    List<Long> distinctIdList = menuIdList.stream().distinct().collect(Collectors.toList());
                    menuPermissionDTO.setMenuIdList(distinctIdList);
                    ruleList.addAll(enforcerManage.buildRoleMenuRules(entity.getId(), menuPermissionDTOList));
                    removeIdList.add(entity.getId());
                    RoleCacheDTO roleCacheDTO = RoleCacheConvert.permissionRoleCacheConvert(entity.getId(),
                            permissionDTO.getMenuPermissionDTOList(), permissionDTO.getDataPermissionDTOList(), entity);
                    roleCacheDTOList.add(roleCacheDTO);
                    break;
                }
            }
        }
        refactoringPermissionCasbinRuleManage.batchRemoveRoleMenuPermission(removeIdList);
        // 保存ruleList
        enforcerManage.addPolicies(ruleList);
        // 刷新角色缓存
        roleCacheDTOList.forEach(dto -> roleCacheManage.addRoleCache(dto));
    }

    @Override
    public List<SystemCountDTO> notInRole(List<Long> roleIdList, List<String> targetSystems) {
        List<SystemCountDTO> list = Optional.ofNullable(roleDao.notInRole(roleIdList, targetSystems)).orElse(Lists.newArrayList());
        for (SystemCountDTO systemCountDTO : list) {
            String system = systemCountDTO.getSystem();
            if (StringUtils.isNotBlank(system)) {
                system = system.replace("[\"", "")
                        .replace("\"]", "");
                systemCountDTO.setSystem(system);
            }
        }
        return list;
    }


    private void checkDefaultRoleCanAdd(RoleBasicParam roleBasicParam, UserInfoDTO userInfo) {
        // 如果不是默认角色的新增或编辑，则不用校验
        if (!RoleTypeEnum.DEFAULT_ROLE.getCode().equals(roleBasicParam.getRoleType())) {
            return;
        }
        // 如果是商家默认角色，只能创建一个默认角色
        if (RoleTypeEnum.isClientDefaultRole(roleBasicParam.getRoleType(), roleBasicParam.getAuthScene())) {
            List<SysRoleDTO> sysRoleDTOList = roleManager.listRoleByType(roleBasicParam.getRoleType(), roleBasicParam.getAuthScene());
            if (CollectionUtils.isNotEmpty(sysRoleDTOList)) {
                throw BusinessException.ofI18nCode(PermissionErrorCodeEnums.ONLY_ONE_DEFAULT_ROLE_CAN_CREATE.getCode(),
                        PermissionErrorCodeEnums.ONLY_ONE_DEFAULT_ROLE_CAN_CREATE.getDesc());
            }
        }
        // 如果是员工默认角色，只有超管能够操作
        if (RoleTypeEnum.isEmployeeDefaultRole(roleBasicParam.getRoleType(), roleBasicParam.getAuthScene())) {
            AdminDTO adminDTO = adminManage.getAdminDTO(userInfo.getUserCode());
            // 不是超管，则不能操作
            if (Objects.isNull(adminDTO) || Boolean.FALSE.equals(adminDTO.getIsSuperAccount())) {
                throw BusinessException.ofI18nCode(PermissionErrorCodeEnums.SUPER_ADMIN_CAN_CREATE_DEFAULT_ROLE.getCode(),
                        PermissionErrorCodeEnums.SUPER_ADMIN_CAN_CREATE_DEFAULT_ROLE.getDesc());
            }
        }
    }


    @Override
    public List<SysRoleDO> listBySystemRoleId(List<String> systemList, List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(systemList) || CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        return roleDao.listBySystemRoleId(systemList, roleIdList);
    }

    @Override
    public SysRoleDO getClientDefaultRole() {
        return roleDao.getClientDefaultRole();
    }

    @Override
    public SysRoleDO getChildRole(Long parentRoleId) {
        return roleDao.getChildRole(parentRoleId);
    }

    @Override
    public List<ParentChildRoleVO> parentChildRoleList() {
        List<SysRoleDO> roleList = roleDao.getParentRole();
        if (CollectionUtils.isEmpty(roleList)) {
            return Collections.emptyList();
        }
        List<Long> roleIdList = roleList.stream()
                .map(SysRoleDO::getId)
                .collect(Collectors.toList());
        List<SysRoleDO> childList = roleDao.getChildRoleByParentIdList(roleIdList);
        Map<Long, SysRoleDO> map = childList.stream()
                .collect(Collectors.toMap(SysRoleDO::getParentId, Function.identity(), (k1, k2) -> k1));
        return roleList.stream()
                .map(role -> {
                    ParentChildRoleVO parentChildRoleVO = new ParentChildRoleVO();
                    ParentChildRoleVO.RoleVO roleVO = SysRoleConvert.sysRoleDOToRoleVO(role);
                    SysRoleDO roleDO = map.get(role.getId());
                    ParentChildRoleVO.RoleVO child = SysRoleConvert.sysRoleDOToRoleVO(roleDO);
                    parentChildRoleVO.setParentRole(roleVO);
                    parentChildRoleVO.setChildRole(child);
                    return parentChildRoleVO;
                })
                .collect(Collectors.toList());

    }
}

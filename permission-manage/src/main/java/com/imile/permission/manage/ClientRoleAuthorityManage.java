package com.imile.permission.manage;

import com.github.pagehelper.PageInfo;
import com.imile.common.page.PaginationResult;
import com.imile.common.query.BaseQuery;
import com.imile.permission.domain.applicationApprove.vo.EffectiveLossCountVO;
import com.imile.permission.domain.client.query.ClientRoleDetailsQuery;
import com.imile.permission.domain.client.vo.ClientRoleDetailsVO;
import com.imile.permission.domain.client.vo.ClientRoleRecordVO;
import com.imile.permission.domain.entity.ClientRoleAuthorityCollectDO;
import com.imile.permission.domain.role.param.RoleIdPageQueryParam;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
public interface ClientRoleAuthorityManage {
    List<ClientRoleAuthorityCollectDO> getActiveRoleByClientCode(String clientCode, LocalDateTime localDateTime);

    void bindingClientCodeRole(String clientCode, Long parentRoleId);

    void cleanRole(String clientCode);

    PaginationResult<ClientRoleRecordVO> roleRecordPage(BaseQuery query, String clientCode, Boolean isExpired);

    EffectiveLossCountVO getRecordCount(String clientCode);

    void revokeRole(String clientCode, Long roleId);

    Map<Long, Integer> countRoleMap(List<Long> roleIdList);

    PageInfo<String> pageClientCode(RoleIdPageQueryParam param);

    List<ClientRoleDetailsVO> getRoleApplicationDetails(ClientRoleDetailsQuery roleApplicationDetailsQuery);

    void removeRoleCollect(Long id);
}

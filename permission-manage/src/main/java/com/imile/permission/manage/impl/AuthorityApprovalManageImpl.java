package com.imile.permission.manage.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.convert.AuthorityApprovalConvert;
import com.imile.permission.dao.AuthorityApprovalDAO;
import com.imile.permission.dao.DirectAuthorizationApprovalCollectDAO;
import com.imile.permission.dao.DirectAuthorizationApprovalDetailDAO;
import com.imile.permission.dao.RoleAuthorityApprovalCollectDAO;
import com.imile.permission.dao.RoleAuthorityApprovalDetailDAO;
import com.imile.permission.dao.WorkCenterApprovalCollectDAO;
import com.imile.permission.dao.WorkCenterApprovalDetailDAO;
import com.imile.permission.domain.applicationApprove.dto.OperationDTO;
import com.imile.permission.domain.applicationApprove.query.ApprovalQuery;
import com.imile.permission.domain.applicationApprove.query.AuthorityApprovalPageQuery;
import com.imile.permission.domain.applicationApprove.query.DirectAuthorizationApplicationDetailsQuery;
import com.imile.permission.domain.applicationApprove.query.RoleAuthorityApprovalDetailQuery;
import com.imile.permission.domain.applicationApprove.query.WorkCenterApplicationDetailsQuery;
import com.imile.permission.domain.applicationApprove.vo.ApplicationRecordVO;
import com.imile.permission.domain.applicationApprove.vo.DirectAuthorizationApplicationRecordVO;
import com.imile.permission.domain.applicationApprove.vo.EffectiveLossCountVO;
import com.imile.permission.domain.applicationApprove.vo.UserDirectAuthCollectVO;
import com.imile.permission.domain.applicationApprove.vo.UserRoleCollectVO;
import com.imile.permission.domain.applicationApprove.vo.WorkCenterApplicationVO;
import com.imile.permission.domain.dataPermission.dto.SystemTypeDTO;
import com.imile.permission.domain.entity.AuthorityApprovalDO;
import com.imile.permission.domain.entity.DirectAuthorizationApprovalCollectDO;
import com.imile.permission.domain.entity.DirectAuthorizationApprovalDetailDO;
import com.imile.permission.domain.entity.RoleAuthorityApprovalCollectDO;
import com.imile.permission.domain.entity.RoleAuthorityApprovalDetailDO;
import com.imile.permission.domain.entity.WorkCenterApprovalCollectDO;
import com.imile.permission.domain.entity.WorkCenterApprovalDetailDO;
import com.imile.permission.domain.permission.dto.UserPermissionDTO;
import com.imile.permission.domain.role.dto.UserRoleDTO;
import com.imile.permission.enums.ApprovalInfoStatusEnum;
import com.imile.permission.enums.OperationTypeStatusEnum;
import com.imile.permission.enums.PermissionApplyTypeEnum;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.enums.RemarkStatusEnum;
import com.imile.permission.enums.SourceTypeEnums;
import com.imile.permission.helper.PermissionPageHelper;
import com.imile.permission.integration.resource.ResSystemResourceIntegration;
import com.imile.permission.manage.AuthorityApprovalManage;
import com.imile.permission.manage.DataPermissionManage;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Slf4j
@Service
public class AuthorityApprovalManageImpl implements AuthorityApprovalManage {

    @Autowired
    private AuthorityApprovalDAO authorityApprovalDAO;

    @Autowired
    private RoleAuthorityApprovalCollectDAO roleAuthorityApprovalCollectDAO;

    @Autowired
    private RoleAuthorityApprovalDetailDAO roleAuthorityApprovalDetailDAO;

    @Autowired
    private DirectAuthorizationApprovalDetailDAO directAuthorizationApprovalDetailDAO;

    @Autowired
    private DirectAuthorizationApprovalCollectDAO directAuthorizationApprovalCollectDAO;

    @Autowired
    private WorkCenterApprovalDetailDAO workCenterApprovalDetailDAO;

    @Autowired
    private WorkCenterApprovalCollectDAO workCenterApprovalCollectDAO;

    @Autowired
    private DataPermissionManage dataPermissionManage;

    @Autowired
    private ResSystemResourceIntegration resSystemResourceIntegration;

    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;

    @Autowired
    private AuthorityApprovalManage self;
    // ====================================================================

    @Override
    public List<AuthorityApprovalDO> getApprovingUserRole(String userCode, Long roleId) {
        if (StringUtils.isBlank(userCode) || Objects.isNull(roleId)) {
            return Collections.emptyList();
        }
        return authorityApprovalDAO.getApprovingUserRole(userCode, roleId);
    }

    @Override
    public RoleAuthorityApprovalCollectDO getUserRoleCollect(String userCode, Long roleId) {
        if (StringUtils.isBlank(userCode) || Objects.isNull(roleId)) {
            return null;
        }
        List<RoleAuthorityApprovalCollectDO> userRoleCollect = roleAuthorityApprovalCollectDAO.getUserRoleCollect(userCode, roleId);
        if (CollectionUtils.isEmpty(userRoleCollect)) {
            return null;
        }
        return userRoleCollect.get(0);
    }

    @Override
    public void saveAuthorityApproval(AuthorityApprovalDO authorityApproval) {
        if (Objects.isNull(authorityApproval)) {
            return;
        }
        authorityApprovalDAO.save(authorityApproval);
    }

    @Override
    public List<ApplicationRecordVO> getApplicationRecordList(AuthorityApprovalPageQuery query) {
        if (Objects.isNull(query)) {
            return Collections.emptyList();
        }
        return authorityApprovalDAO.getApplicationRecordList(query);
    }

    @Override
    public PaginationResult<ApplicationRecordVO> getApplicationRecordPage(AuthorityApprovalPageQuery authorityApprovalPageQuery) {
        PageInfo<ApplicationRecordVO> pageInfo = PermissionPageHelper.startPage(authorityApprovalPageQuery)
                .doSelectPageInfo(() -> authorityApprovalDAO.getApplicationRecordList(authorityApprovalPageQuery));
        List<ApplicationRecordVO> list = pageInfo.getList();

        // 返回系统
        if (CollectionUtils.isNotEmpty(list)) {
            for (ApplicationRecordVO applicationRecordVO : list) {
                String multipleSystem = applicationRecordVO.getMultipleSystem();
                String roleCountry = applicationRecordVO.getRoleCountry();
                if (StringUtils.isNotBlank(multipleSystem)) {
                    List<String> systemCodeList = JSONArray.parseArray(multipleSystem, String.class);
                    applicationRecordVO.setSystemCodeList(systemCodeList);
                }
                if (StringUtils.isNotBlank(roleCountry)) {
                    List<String> roleCountryList = JSONArray.parseArray(roleCountry, String.class);
                    applicationRecordVO.setRoleCountryList(roleCountryList);
                }
            }
        }
        List<Long> sensitiveIdList = resSystemResourceIntegration.getSensitiveIdList(null);
        if (CollectionUtils.isNotEmpty(sensitiveIdList)) {
            List<Long> sensitiveRoleIds = refactoringPermissionCasbinRuleManage.getRoleIdListByMenuIdList(sensitiveIdList);
            if (CollectionUtils.isNotEmpty(sensitiveRoleIds)) {
                for (ApplicationRecordVO role : list) {
                    if (sensitiveRoleIds.contains(role.getRoleId())) {
                        role.setSensitiveLevel(1);
                    } else {
                        role.setSensitiveLevel(0);
                    }
                }
            }
        }
        return PageUtil.getPageResult(list, authorityApprovalPageQuery, pageInfo);
    }

    @Override
    public AuthorityApprovalDO getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return authorityApprovalDAO.getById(id);
    }

    @Override
    public void updateById(AuthorityApprovalDO authorityApprovalDO) {
        if (Objects.isNull(authorityApprovalDO) || Objects.isNull(authorityApprovalDO.getId())) {
            return;
        }
        authorityApprovalDAO.updateById(authorityApprovalDO);
    }

    @Override
    public void saveRoleAuthorityApprovalDetail(RoleAuthorityApprovalDetailDO detail) {
        if (Objects.isNull(detail)) {
            return;
        }
        roleAuthorityApprovalDetailDAO.save(detail);
    }

    @Override
    public void saveRoleAuthorityApprovalCollect(RoleAuthorityApprovalCollectDO collectAdd) {
        if (Objects.isNull(collectAdd)) {
            return;
        }
        roleAuthorityApprovalCollectDAO.userRoleUpdateOrSave(collectAdd);
    }

    @Override
    public void updateRoleAuthorityApprovalCollectExpirationDateApplicationDate(RoleAuthorityApprovalCollectDO collect) {
        if (Objects.isNull(collect) || StringUtils.isBlank(collect.getUserCode()) || Objects.isNull(collect.getRoleId())) {
            return;
        }
        roleAuthorityApprovalCollectDAO.updateRoleAuthorityApprovalCollectExpirationDateApplicationDate(collect);
    }

    @Override
    public List<RoleAuthorityApprovalDetailDO> selectRoleAuthorityApprovalDetail(RoleAuthorityApprovalDetailQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getRoleId())) {
            return Collections.emptyList();
        }
        return roleAuthorityApprovalDetailDAO.select(query);
    }

    @Override
    public List<AuthorityApprovalDO> getByApprovalId(Long approvalId) {
        if (Objects.isNull(approvalId)) {
            return Collections.emptyList();
        }
        return authorityApprovalDAO.getByApprovalId(approvalId);
    }

    @Override
    public void saveBatchRoleAuthorityApprovalDetail(List<RoleAuthorityApprovalDetailDO> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        roleAuthorityApprovalDetailDAO.saveBatch(detailList);
    }

    @Override
    public List<RoleAuthorityApprovalCollectDO> getUserRoleList(String userCode, Collection<Long> roleIdList) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        return roleAuthorityApprovalCollectDAO.getUserRoleList(userCode, roleIdList);
    }

    @Override
    public List<RoleAuthorityApprovalCollectDO> getUserAndRoleList(List<String> userCodeList, Collection<Long> roleIdList) {
        if (CollectionUtils.isEmpty(userCodeList) || CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        return roleAuthorityApprovalCollectDAO.getUserAndRoleList(userCodeList, roleIdList);
    }

    @Override
    public void saveBatchRoleAuthorityApprovalCollect(List<RoleAuthorityApprovalCollectDO> issertList) {
        if (CollectionUtils.isEmpty(issertList)) {
            return;
        }
        roleAuthorityApprovalCollectDAO.userRoleBatchUpsert(issertList);
    }

    @Override
    public void updateUserRoleExpirationDate(String userCode, Collection<Long> roleIdList, LocalDateTime now) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(roleIdList)) {
            return;
        }
        roleAuthorityApprovalCollectDAO.updateUserExpirationDate(userCode, roleIdList, now);
    }

    @Override
    public void saveDirectAuthorizationApprovalDetail(List<DirectAuthorizationApprovalDetailDO> directDetailList) {
        if (CollectionUtils.isEmpty(directDetailList)) {
            return;
        }
        directAuthorizationApprovalDetailDAO.patchSave(directDetailList);
    }

    @Override
    public List<DirectAuthorizationApprovalCollectDO> getUserDirectAuthorizationApprovalCollect(String userCode, String sourceType, List<String> sourceIdList) {
        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(sourceType) || CollectionUtils.isEmpty(sourceIdList)) {
            return Collections.emptyList();
        }
        return directAuthorizationApprovalCollectDAO.getUserDirectAuthorizationApprovalCollect(userCode, sourceType, sourceIdList);
    }

    @Override
    public void saveDirectAuthorizationApprovalCollect(List<DirectAuthorizationApprovalCollectDO> directCollectAddList) {
        if (CollectionUtils.isEmpty(directCollectAddList)) {
            return;
        }

        directAuthorizationApprovalCollectDAO.directUserBatchUpsert(directCollectAddList);
    }

    @Override
    public void updateDirectAuthorizationApprovalCollectExpirationDateApplicationDate(DirectAuthorizationApprovalCollectDO directCollect) {
        if (Objects.isNull(directCollect) || StringUtils.isBlank(directCollect.getUserCode()) || StringUtils.isBlank(directCollect.getSourceType()) || StringUtils.isBlank(directCollect.getSourceId())) {
            return;
        }
        directAuthorizationApprovalCollectDAO.updateDirectAuthorizationApprovalCollectExpirationDateApplicationDate(directCollect);
    }

    @Override
    public PaginationResult<DirectAuthorizationApplicationRecordVO> getAuthorizationRecordDepartmentAuthorizationList(AuthorityApprovalPageQuery pageQuery) {
        PageInfo<DirectAuthorizationApplicationRecordVO> pageInfo = PermissionPageHelper.startPage(pageQuery)
                .doSelectPageInfo(() -> directAuthorizationApprovalCollectDAO.getApplicationRecordList(pageQuery));
        return PageUtil.getPageResult(pageInfo.getList(), pageQuery, pageInfo);
    }

    @Override
    public List<DirectAuthorizationApprovalDetailDO> selectDirectAuthorizationApplicationDetails(DirectAuthorizationApplicationDetailsQuery directAuthorizationApplicationDetailsQuery) {
        if (Objects.isNull(directAuthorizationApplicationDetailsQuery)) {
            return Collections.emptyList();
        }
        return directAuthorizationApprovalDetailDAO.select(directAuthorizationApplicationDetailsQuery);
    }

    @Override
    public void expireUserPermissionsImmediatelyCollect(List<String> userCodeList, String sourceType, List<String> sourceIdList) {
        if (CollectionUtils.isEmpty(userCodeList) || StringUtils.isBlank(sourceType) || CollectionUtils.isEmpty(sourceIdList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        for (String userCode : userCodeList) {
            List<DirectAuthorizationApprovalCollectDO> directAuthorizationApprovalCollect = this.getUserDirectAuthorizationApprovalCollect(userCode, sourceType, sourceIdList);
            Map<String, DirectAuthorizationApprovalCollectDO> directCollectMap = directAuthorizationApprovalCollect.stream().collect(Collectors.toMap(DirectAuthorizationApprovalCollectDO::getSourceId, Function.identity(), (v1, v2) -> v1));
            Set<String> keySet = directCollectMap.keySet();
            // 新增汇总记录处理
            Collection<String> addSourceIdList = CollectionUtils.removeAll(sourceIdList, keySet);
            if (CollectionUtils.isNotEmpty(addSourceIdList)) {
                List<DirectAuthorizationApprovalCollectDO> directCollectAddList = new ArrayList<>(addSourceIdList.size());
                for (String sourceId : addSourceIdList) {
                    DirectAuthorizationApprovalCollectDO directCollectAdd = new DirectAuthorizationApprovalCollectDO();

                    directCollectAdd.setUserCode(userCode);
                    directCollectAdd.setSourceType(sourceType);
                    directCollectAdd.setSourceId(sourceId);
                    directCollectAdd.setApplicationDate(now);
                    directCollectAdd.setExpirationDate(now);
                    // 设置执行处理
                    directCollectAdd.setIsExecute(BusinessConstant.Y);
                    directCollectAdd.setId(IdWorker.getId());
                    directCollectAddList.add(directCollectAdd);
                }
                this.saveDirectAuthorizationApprovalCollect(directCollectAddList);
            }

            // 更新编辑记录
            if (CollectionUtils.isNotEmpty(directAuthorizationApprovalCollect)) {
                List<Long> idList = directAuthorizationApprovalCollect.stream().map(DirectAuthorizationApprovalCollectDO::getId).collect(Collectors.toList());
                directAuthorizationApprovalCollectDAO.updateApplicationDateExpirationDate(idList, now, now);
            }

        }
    }


    @Override
    public void authorizeUserPermissionsImmediatelyCollect(List<String> userCodeList, String sourceType, List<String> sourceIdList) {
        authorizeUserPermissionsImmediatelyCollect(userCodeList, sourceType, sourceIdList, BusinessConstant.MAX_EXPIRATION_DATE);
    }

    @Override
    public void authorizeAndRenewalCollect(List<String> userCodeList, String sourceType, List<String> sourceIdList, LocalDateTime applyDate, LocalDateTime expDate) {
        if (CollectionUtils.isEmpty(userCodeList) || StringUtils.isBlank(sourceType) || CollectionUtils.isEmpty(sourceIdList)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(sourceIdList)) {
            for (String userCode : userCodeList) {
                // 保存直接授权汇总记录
                List<DirectAuthorizationApprovalCollectDO> directCollectEntityList = getUserDirectAuthorizationApprovalCollect(userCode, sourceType, sourceIdList);
                Map<String, DirectAuthorizationApprovalCollectDO> directCollectMap = directCollectEntityList.stream().collect(Collectors.toMap(DirectAuthorizationApprovalCollectDO::getSourceId, Function.identity(), (v1, v2) -> v1));
                Set<String> keySet = directCollectMap.keySet();

                // 新增汇总记录处理
                Collection<String> addDataList = CollectionUtils.removeAll(sourceIdList, keySet);
                if (CollectionUtils.isNotEmpty(addDataList)) {
                    List<DirectAuthorizationApprovalCollectDO> directCollectAddList = new ArrayList<>(addDataList.size());
                    for (String sourceId : addDataList) {
                        // 汇总记录
                        DirectAuthorizationApprovalCollectDO directCollectAdd = new DirectAuthorizationApprovalCollectDO();
                        directCollectAdd.setUserCode(userCode);
                        directCollectAdd.setSourceType(sourceType);
                        directCollectAdd.setSourceId(sourceId);
                        directCollectAdd.setApplicationDate(applyDate);
                        directCollectAdd.setExpirationDate(expDate);
                        directCollectAdd.setIsExecute(BusinessConstant.N);
                        directCollectAdd.setId(IdWorker.getId());
                        directCollectAddList.add(directCollectAdd);

                    }
                    self.saveDirectAuthorizationApprovalCollect(directCollectAddList);
                }


                List<Long> updateExpDateIdList = Lists.newArrayList();
                // 编辑汇总记录处理
                directCollectEntityList.forEach(directCollect -> {
                    if (!sourceIdList.contains(directCollect.getSourceId())) {
                        return;
                    }
                    LocalDateTime expirationDate = directCollect.getExpirationDate();
                    // 设置为未执行过期
                    directCollect.setIsExecute(BusinessConstant.N);
                    if (!(expirationDate.isEqual(BusinessConstant.MAX_EXPIRATION_DATE)) ||
                            expirationDate.isAfter(BusinessConstant.MAX_EXPIRATION_DATE)) {
                        // 已过期

                        if (expirationDate.isBefore(now)) {
                            updateExpDateIdList.add(directCollect.getId());

                        }
                        // 未过期
                        else {
                            if (expDate.isAfter(directCollect.getExpirationDate())) {
                                updateExpDateIdList.add(directCollect.getId());
                            }
                        }

                        // 清理变更信息
                        directCollect.cleanLastUpd();
                        // 更新汇总记录
                    }
                });
                if (CollectionUtils.isNotEmpty(updateExpDateIdList)) {
                    directAuthorizationApprovalCollectDAO.updateApplicationDateExpirationDate(updateExpDateIdList, applyDate, expDate);
                }

            }
        }
    }


    @Override
    public void authorizeUserPermissionsImmediatelyCollect(List<String> userCodeList, String sourceType, List<String> sourceIdList, LocalDateTime time) {
        if (CollectionUtils.isEmpty(userCodeList) || StringUtils.isBlank(sourceType) || CollectionUtils.isEmpty(sourceIdList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        for (String userCode : userCodeList) {
            List<DirectAuthorizationApprovalCollectDO> directAuthorizationApprovalCollect = this.getUserDirectAuthorizationApprovalCollect(userCode, sourceType, sourceIdList);
            Map<String, DirectAuthorizationApprovalCollectDO> directCollectMap = directAuthorizationApprovalCollect.stream().collect(Collectors.toMap(DirectAuthorizationApprovalCollectDO::getSourceId, Function.identity(), (v1, v2) -> v1));
            Set<String> keySet = directCollectMap.keySet();
            // 新增汇总记录处理
            Collection<String> addSourceIdList = CollectionUtils.removeAll(sourceIdList, keySet);
            if (CollectionUtils.isNotEmpty(addSourceIdList)) {
                List<DirectAuthorizationApprovalCollectDO> directCollectAddList = new ArrayList<>(addSourceIdList.size());
                for (String sourceId : addSourceIdList) {
                    DirectAuthorizationApprovalCollectDO directCollectAdd = new DirectAuthorizationApprovalCollectDO();
                    directCollectAdd.setId(IdWorker.getId());
                    directCollectAdd.setUserCode(userCode);
                    directCollectAdd.setSourceType(sourceType);
                    directCollectAdd.setSourceId(sourceId);
                    directCollectAdd.setApplicationDate(now);
                    directCollectAdd.setExpirationDate(time);
                    directCollectAdd.setIsExecute(BusinessConstant.N);
                    directCollectAddList.add(directCollectAdd);
                }
                this.saveDirectAuthorizationApprovalCollect(directCollectAddList);
            }

            // 更新编辑记录
            if (CollectionUtils.isNotEmpty(directAuthorizationApprovalCollect)) {
                List<Long> idList = directAuthorizationApprovalCollect.stream().map(DirectAuthorizationApprovalCollectDO::getId).collect(Collectors.toList());
                directAuthorizationApprovalCollectDAO.updateApplicationDateExpirationDate(idList, now, time);
            }

        }
    }

    @Override
    public void addUserPermissionsImmediatelyDetail(List<String> userCodeList, String sourceType, List<String> sourceIdList, OperationDTO operationDTO) {
        if (CollectionUtils.isEmpty(userCodeList) || StringUtils.isBlank(sourceType) || CollectionUtils.isEmpty(sourceIdList)) {
            return;
        }
        List<DirectAuthorizationApprovalDetailDO> directDetailList = new ArrayList<>(userCodeList.size() * sourceIdList.size());
        for (String userCode : userCodeList) {
            for (String sourceId : sourceIdList) {
                DirectAuthorizationApprovalDetailDO directDetail = new DirectAuthorizationApprovalDetailDO();
                directDetail.setUserCode(userCode);
                directDetail.setSourceType(sourceType);
                directDetail.setSourceId(sourceId);
                directDetail.setExpirationDate(operationDTO.getExpirationDate());

                // 注意后台处理与直接授权需要分开处理
                directDetail.setOperationType(operationDTO.getOperationType());
                directDetail.setOperator(operationDTO.getOperator());
                directDetail.setOperationTime(operationDTO.getOperationTime());
                directDetail.setRemark(operationDTO.getRemark());

                directDetail.setId(IdWorker.getId());

                directDetail.setCreateUserCode(operationDTO.getCreateUserCode());
                directDetail.setCreateUserName(operationDTO.getCreateUserName());
                directDetail.setLastUpdUserCode(operationDTO.getLastUpdUserCode());
                directDetail.setLastUpdUserName(operationDTO.getLastUpdUserName());
                directDetail.setCreateDate(new Date());
                directDetail.setLastUpdDate(new Date());
                directDetail.setIsDelete(BusinessConstant.N);
                directDetail.setApprovalId(operationDTO.getApprovalId());
                directDetailList.add(directDetail);
            }
        }
        directAuthorizationApprovalDetailDAO.patchSave(directDetailList);
    }

    @Override
    public EffectiveLossCountVO getApplicationRecordCount(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now().withNano(0);
        Integer effectiveCount = roleAuthorityApprovalCollectDAO.getApplicationRecordEffectiveCount(userCode, now);
        Integer lossCount = roleAuthorityApprovalCollectDAO.getApplicationRecordLossCount(userCode, now);

        EffectiveLossCountVO effectiveLossCountVO = new EffectiveLossCountVO();

        effectiveLossCountVO.setEffectiveCount(effectiveCount);
        effectiveLossCountVO.setLoseCount(lossCount);
        return effectiveLossCountVO;
    }

    @Override
    public EffectiveLossCountVO getAuthorizationRecordDepartmentAuthorizationCount(String userCode) {
        List<String> sourceTypeList = Arrays.asList(
                SourceTypeEnums.MAIN_DATA_BASE_DEPT_DATA.getCode(),
                SourceTypeEnums.MENU.getCode()
        );

        return getEffectiveLossCountVO(userCode, sourceTypeList);
    }

    @Override
    public EffectiveLossCountVO getFunctionAuthorizationRecordDepartmentAuthorizationCount(String userCode) {
        List<String> sourceTypeList = Collections.singletonList(
                SourceTypeEnums.MENU.getCode()
        );

        return getEffectiveLossCountVO(userCode, sourceTypeList);
    }

    @Override
    public EffectiveLossCountVO getDataAuthorizationRecordDepartmentAuthorizationCount(String userCode) {
        List<String> sourceTypeList = Collections.singletonList(
                SourceTypeEnums.MAIN_DATA_BASE_DEPT_DATA.getCode()
        );

        Map<String, SystemTypeDTO> systemCodeMap = dataPermissionManage.getDataSystem();
        Set<String> typeCodeSet = systemCodeMap.keySet();
        return getEffectiveLossCountVO(userCode, new ArrayList<>(typeCodeSet));
    }

    private @Nullable EffectiveLossCountVO getEffectiveLossCountVO(String userCode, List<String> sourceTypeList) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now().withNano(0);

        Integer effectiveCount = directAuthorizationApprovalCollectDAO.getApplicationRecordEffectiveCount(userCode, now, sourceTypeList);
        Integer lossCount = directAuthorizationApprovalCollectDAO.getApplicationRecordLossCount(userCode, now, sourceTypeList);

        EffectiveLossCountVO effectiveLossCountVO = new EffectiveLossCountVO();

        effectiveLossCountVO.setEffectiveCount(effectiveCount);
        effectiveLossCountVO.setLoseCount(lossCount);
        return effectiveLossCountVO;
    }

    @Override
    public void directUserUpdateOrSave(DirectAuthorizationApprovalCollectDO directAuthorizationApprovalCollectDO) {
        if (Objects.isNull(directAuthorizationApprovalCollectDO)) {
            return;
        }
        directAuthorizationApprovalCollectDAO.directUserUpdateOrSave(directAuthorizationApprovalCollectDO);
    }

    @Override
    public void directUserBatchUpsert(List<DirectAuthorizationApprovalCollectDO> directList) {
        directAuthorizationApprovalCollectDAO.directUserBatchUpsert(directList);
    }

    @Override
    public List<UserRoleCollectVO> getUserRoleCollectList() {
        return roleAuthorityApprovalCollectDAO.getUserRoleCollect();
    }

    @Override
    public List<UserDirectAuthCollectVO> getUserDirectAuthList() {
        return directAuthorizationApprovalCollectDAO.getUserDirectAuthCollect();
    }

    @Override
    public List<String> checkUserBindingRoleCollect(Collection<String> userCodeList, Long roleId) {
        if (Objects.isNull(roleId) || CollectionUtils.isEmpty(userCodeList)) {
            return Collections.emptyList();
        }
        return roleAuthorityApprovalCollectDAO.getUserBindingRoleCollect(userCodeList, roleId)
                .stream()
                .map(RoleAuthorityApprovalCollectDO::getUserCode)
                .collect(Collectors.toList());
    }

    @Override
    public void updateUserBindingRoleCollectExpirationDate(List<String> userCodeList, Long roleId, LocalDateTime time) {
        if (Objects.isNull(roleId) || CollectionUtils.isEmpty(userCodeList)) {
            return;
        }
        roleAuthorityApprovalCollectDAO.updateUserBindingRoleCollectExpirationDate(userCodeList, roleId, time);
    }

    @Override
    public void updateUserRoleCollectExpirationDate(List<String> userCodeList, List<Long> roleIdList, LocalDateTime time) {
        if (CollectionUtils.isEmpty(userCodeList) || CollectionUtils.isEmpty(roleIdList)) {
            return;
        }
        roleAuthorityApprovalCollectDAO.updateUserRoleCollectExpirationDate(userCodeList, roleIdList, time);
    }

    @Override
    public void saveBatchWorkCenterApprovalDetail(List<WorkCenterApprovalDetailDO> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        workCenterApprovalDetailDAO.saveBatch(detailList);
    }

    @Override
    public List<WorkCenterApprovalCollectDO> getUserWorkCenterList(String userCode, List<Long> newIdList) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(newIdList)) {
            return Collections.emptyList();
        }
        return workCenterApprovalCollectDAO.getUserWorkCenterList(userCode, newIdList);
    }

    @Override
    public void updateUserWorkCenterExpirationDate(String userCode, List<Long> newIdList, LocalDateTime now) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(newIdList)) {
            return;
        }
        workCenterApprovalCollectDAO.updateUserExpirationDate(userCode, newIdList, now);
    }

    @Override
    public void saveBatchWorkCenterApprovalCollect(List<WorkCenterApprovalCollectDO> issertList) {
        if (CollectionUtils.isEmpty(issertList)) {
            return;
        }
        workCenterApprovalCollectDAO.batchUpsert(issertList);
    }

    @Override
    public void saveWorkCenterApprovalDetail(WorkCenterApprovalDetailDO detail) {
        if (Objects.isNull(detail)) {
            return;
        }
        workCenterApprovalDetailDAO.save(detail);
    }

    @Override
    public WorkCenterApprovalCollectDO getUserWorkCenterCollect(String userCode, Long workCenterId) {
        if (StringUtils.isBlank(userCode) || Objects.isNull(workCenterId)) {
            return null;
        }
        List<WorkCenterApprovalCollectDO> userWorkCenterCollect = workCenterApprovalCollectDAO.getUserWorkCenterCollect(userCode, workCenterId);
        if (CollectionUtils.isEmpty(userWorkCenterCollect)) {
            return null;
        }
        return userWorkCenterCollect.get(0);
    }

    @Override
    public void saveWorkCenterApprovalCollect(WorkCenterApprovalCollectDO collectAdd) {
        if (Objects.isNull(collectAdd)) {
            return;
        }
        workCenterApprovalCollectDAO.userWorkCenterUpdateOrSave(collectAdd);
    }

    @Override
    public void updateWorkCenterApprovalCollectExpirationDateApplicationDate(WorkCenterApprovalCollectDO collect) {
        if (Objects.isNull(collect) || StringUtils.isBlank(collect.getUserCode()) || Objects.isNull(collect.getWorkCenterId())) {
            return;
        }
        workCenterApprovalCollectDAO.updateWorkCenterAuthorityApprovalCollectExpirationDateApplicationDate(collect);
    }

    @Override
    public PageInfo<WorkCenterApplicationVO> getWorkCenterApplicationPage(AuthorityApprovalPageQuery pageQuery) {

        PageInfo<WorkCenterApplicationVO> pageInfo = PermissionPageHelper.startPage(pageQuery)
                .doSelectPageInfo(() -> authorityApprovalDAO.getWorkCenterApplicationPage(pageQuery));
        return pageInfo;
    }

    @Override
    public EffectiveLossCountVO getWorkCenterApplicationCount(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            throw BusinessException.ofI18nCode(PermissionErrorCodeEnums.USER_NOT_EXIST.getCode(), PermissionErrorCodeEnums.USER_NOT_EXIST.getDesc());
        }
        AuthorityApprovalPageQuery query = new AuthorityApprovalPageQuery();
        query.setUserCode(userCode);
        query.setExpirationDate(LocalDateTime.now().withNano(0));
        query.setIsExpired(true);
        // 查询失效的个数
        Integer loseCount = authorityApprovalDAO.getWorkCenterApplicationCount(query);
        query.setIsExpired(false);
        Integer effectCount = authorityApprovalDAO.getWorkCenterApplicationCount(query);

        EffectiveLossCountVO countVO = new EffectiveLossCountVO();
        countVO.setEffectiveCount(effectCount);
        countVO.setLoseCount(loseCount);
        return countVO;
    }

    @Override
    public List<WorkCenterApprovalDetailDO> getWorkCenterApplicationDetails(WorkCenterApplicationDetailsQuery query) {
        List<WorkCenterApprovalDetailDO> detailDOList = workCenterApprovalDetailDAO.getWorkCenterApplicationDetails(query);
        return detailDOList;
    }

    @Override
    public List<AuthorityApprovalDO> getApprovingUserWorkCenter(String userCode, Long workCenterId) {
        if (StringUtils.isBlank(userCode) || Objects.isNull(workCenterId)) {
            return Collections.emptyList();
        }
        return authorityApprovalDAO.getApprovingUserWorkCenter(userCode, workCenterId);
    }

    @Override
    public List<AuthorityApprovalDO> getApprovingUserMenuId(String userCode, Long menuId) {

        if (StringUtils.isBlank(userCode) || Objects.isNull(menuId)) {
            return Collections.emptyList();
        }
        return authorityApprovalDAO.getApprovingUserMenuId(userCode, menuId);
    }

    @Override
    public List<AuthorityApprovalDO> getUserApprovalList(ApprovalQuery query) {
        return authorityApprovalDAO.getUserApprovalList(query);
    }

    @Override
    public List<AuthorityApprovalDO> getUserApprovingMenuList(String userCode, String systemCode) {
        ApprovalQuery query = new ApprovalQuery();
        query.setApprovalStatus(ApprovalInfoStatusEnum.APPROVING.getCode());
        query.setPermissionType(PermissionApplyTypeEnum.MENU.getCode());
        query.setUserCode(userCode);
        query.setSystemCode(systemCode);
        return authorityApprovalDAO.getUserApprovalList(query);
    }

    @Override
    public void userRoleUpdateOrSave(RoleAuthorityApprovalCollectDO roleAuthorityApprovalCollectDO) {
        if (Objects.isNull(roleAuthorityApprovalCollectDO)) {
            return;
        }
        roleAuthorityApprovalCollectDAO.userRoleUpdateOrSave(roleAuthorityApprovalCollectDO);
    }

    @Override
    public void userRoleBatchUpsert(List<RoleAuthorityApprovalCollectDO> collectDOList) {
        roleAuthorityApprovalCollectDAO.userRoleBatchUpsert(collectDOList);
    }

    @Override
    public void authUserDataPermissionsImmediately(List<UserPermissionDTO> userPermissionDTOList, OperationDTO operationDTO) {
        if (CollectionUtils.isEmpty(userPermissionDTOList)) {
            return;
        }
        // 可能会出现大数据量提交的情况
        List<DirectAuthorizationApprovalDetailDO> detailDOList = AuthorityApprovalConvert.directDetailListConvert(userPermissionDTOList, operationDTO);
        this.saveDirectAuthorizationApprovalDetail(detailDOList);
        List<DirectAuthorizationApprovalCollectDO> directCollectAddList = AuthorityApprovalConvert.directCollectListConvert(userPermissionDTOList, operationDTO);
        this.saveDirectAuthorizationApprovalCollect(directCollectAddList);
    }

    @Override
    public List<UserRoleDTO> listSystemRoleByUserCode(List<String> system, List<String> userCodeList) {
        if (CollectionUtils.isEmpty(system) || CollectionUtils.isEmpty(userCodeList)) {
            return Collections.emptyList();
        }
        List<UserRoleDTO> list = roleAuthorityApprovalCollectDAO.listSystemRoleByUserCode(system, userCodeList);
        return list;
    }

    @Override
    public void updateByIds(List<DirectAuthorizationApprovalCollectDO> directCollectUpdateList) {
        if (CollectionUtils.isEmpty(directCollectUpdateList)) {
            return;
        }
        directAuthorizationApprovalCollectDAO.updateBatchById(directCollectUpdateList);
    }

    @Override
    public List<DirectAuthorizationApprovalCollectDO> listUserListDirectAuthorizationApprovalCollect(List<String> existUserCodes, List<String> sourceTypeList, List<String> menuIdStrList) {
        return directAuthorizationApprovalCollectDAO.listUserListDirectAuthorizationApprovalCollect(existUserCodes, sourceTypeList, menuIdStrList);
    }

    @Override
    public void addUserRole(String operator, String userCode, List<Long> roleIdList) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(roleIdList)) {

            return;
        }
        LocalDateTime now = LocalDateTime.now();

        List<RoleAuthorityApprovalDetailDO> detailList = new ArrayList<>(roleIdList.size());
        List<RoleAuthorityApprovalCollectDO> collectList = new ArrayList<>(roleIdList.size());
        for (Long roleId : roleIdList) {
            RoleAuthorityApprovalDetailDO detailDO = new RoleAuthorityApprovalDetailDO();
            detailDO.setOperationType(OperationTypeStatusEnum.ADD.getCode());
            detailDO.setOperator("MQ");
            detailDO.setUserCode(userCode);
            detailDO.setRoleId(roleId);
            detailDO.setOperationTime(now);
            detailDO.setExpirationDate(BusinessConstant.MAX_EXPIRATION_DATE);
            detailDO.setRemark(RemarkStatusEnum.MQ_USER_INFO_UPDATE.getCode());
            detailList.add(detailDO);

            RoleAuthorityApprovalCollectDO collect = new RoleAuthorityApprovalCollectDO();
            collect.setUserCode(userCode);
            collect.setRoleId(roleId);
            collect.setExpirationDate(BusinessConstant.MAX_EXPIRATION_DATE);
            collect.setApplicationDate(now);
            collect.setId(IdWorker.getId());
            collectList.add(collect);
        }

        saveBatchRoleAuthorityApprovalDetail(detailList);
        saveBatchRoleAuthorityApprovalCollect(collectList);


    }

    @Override
    public void deleteUserRole(String operator, String userCode, List<Long> roleIdList) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(roleIdList)) {
            return;
        }
        List<RoleAuthorityApprovalDetailDO> detailList = new ArrayList<>(roleIdList.size());
        List<RoleAuthorityApprovalCollectDO> collectList = new ArrayList<>(roleIdList.size());
        LocalDateTime now = LocalDateTime.now();
        for (Long roleId : roleIdList) {
            RoleAuthorityApprovalDetailDO detailDO = new RoleAuthorityApprovalDetailDO();
            detailDO.setOperationType(OperationTypeStatusEnum.RECYCLE.getCode());
            detailDO.setOperator("MQ");
            detailDO.setUserCode(userCode);
            detailDO.setRoleId(roleId);
            detailDO.setOperationTime(now);
            detailDO.setExpirationDate(now);
            detailDO.setRemark(RemarkStatusEnum.MQ_USER_INFO_UPDATE.getCode());
            detailList.add(detailDO);

            RoleAuthorityApprovalCollectDO collect = new RoleAuthorityApprovalCollectDO();
            collect.setUserCode(userCode);
            collect.setRoleId(roleId);
            collect.setExpirationDate(now);
            collect.setApplicationDate(now);
            collect.setId(IdWorker.getId());
            collectList.add(collect);
        }

        saveBatchRoleAuthorityApprovalDetail(detailList);
        saveBatchRoleAuthorityApprovalCollect(collectList);
    }
}

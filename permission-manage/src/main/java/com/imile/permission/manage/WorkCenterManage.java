package com.imile.permission.manage;

import com.github.pagehelper.PageInfo;
import com.imile.common.page.PaginationResult;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionVO;
import com.imile.permission.domain.entity.WorkCenterDO;
import com.imile.permission.domain.entity.WorkCenterNodeDO;
import com.imile.permission.domain.role.vo.RoleListVO;
import com.imile.permission.domain.workCenter.dto.WorkCenterDTO;
import com.imile.permission.domain.workCenter.param.WorkCenterPageQueryParam;
import com.imile.permission.domain.workCenter.param.WorkCenterWithNodeParam;
import com.imile.permission.domain.workCenter.query.WorkCenterNodeQuery;
import com.imile.permission.domain.workCenter.vo.SourceNodePermission;
import com.imile.permission.domain.workCenter.vo.WorkCenterDetailVO;
import com.imile.permission.domain.workCenter.vo.WorkCenterPermissionVO;
import com.imile.permission.domain.workCenter.vo.WorkCenterVO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
public interface WorkCenterManage {
    /**
     * 工作中心列表查询
     *
     * @param workCenterPageQueryParam workCenterPageQueryParam
     * @return PaginationResult<WorkCenterVO>
     */
    PaginationResult<WorkCenterVO> findWorkCenterPage(WorkCenterPageQueryParam workCenterPageQueryParam);

    /**
     * 分页查询流程
     *
     * @param workCenterDTO workCenterDTO
     * @return WorkCenterDO
     */
    PageInfo<WorkCenterDO> selectPage(WorkCenterWithNodeParam param);


    /**
     * 获取工作中心详情
     *
     * @param id id
     * @return WorkCenterDetailVO
     */
    WorkCenterDetailVO findWorkCenterById(Long id);

    /**
     * 禁用工作中心
     *
     * @param id id
     * @return
     */
    Boolean disableWorkCenter(Long id);

    /**
     * 获取工作中心未拥有的角色
     *
     * @param workCenterId workCenterId
     * @param roleName     roleName
     * @return List<RoleListVO>
     */
    List<RoleListVO> getRoleNotInWorkCenter(Long workCenterId, String roleName);


    /**
     * 获取工作中心拥有的角色
     *
     * @param workCenterId workCenterId
     * @return List<RoleListVO>
     */
    List<RoleListVO> getRoleInWorkCenter(Long workCenterId);

    /**
     * 获取工作中心权限
     *
     * @param workCenterId workCenterId
     * @return List<SourceNodePermission>
     */
    List<SourceNodePermission> getPermissionByWorkCenterId(Long workCenterId);

    /**
     * 工作中心查询列表
     *
     * @param workCenterPageQueryParam workCenterPageQueryParam
     * @return List<>
     */
    List<WorkCenterVO> findWorkCenter(WorkCenterPageQueryParam workCenterPageQueryParam);

    /**
     * 删除工作中心
     *
     * @param id id
     */
    void deleteWorkCenter(Long id);

    List<WorkCenterDO> selectByWorkCenterName(String workCenterName);

    WorkCenterDO saveDTO(WorkCenterDTO workCenterDTO);

    WorkCenterDO getById(Long workCenterId);

    List<WorkCenterDO> selectByWorkCenterName(String workCenterName, Long workCenterId);

    void updateDO(WorkCenterDO workCenter);

    void deleteNodeByWorkCenterId(Long workCenterId);

    List<WorkCenterNodeDO> selectNodeByQuery(WorkCenterNodeQuery workCenterNodeQuery);

    void saveNodeBatch(Collection<WorkCenterNodeDO> nodeList);

    void updateNode(WorkCenterNodeDO node);

    void removeNodeByIds(List<Long> idList);

    List<Long> getSystemFilterWorkCenter(List<String> systemList, List<Long> workCenterIdList);

    List<WorkCenterDO> listById(List<Long> newBindingWorkCenterIdList);

    List<WorkCenterPermissionVO> getWorkCenterPermission(Long workCenterId);

    List<SystemDataPermissionVO> getMainDataByNodeId(String workNodeId);
}

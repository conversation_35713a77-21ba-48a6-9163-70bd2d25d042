package com.imile.permission.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Data
public class PermissionApiDTO implements Serializable {


    /**
     * 角色列表
     * 包含岗位角色
     * 默认角色
     */
    private List<Long> roleList;

    /**
     * 数据权限
     *
     */
    private List<DataPermissionApiDTO> dataPermissionDTOList;

    /**
     * 菜单权限
     */
    private List<MenuPermissionApiDTO> menuPermissionDTOList;
}

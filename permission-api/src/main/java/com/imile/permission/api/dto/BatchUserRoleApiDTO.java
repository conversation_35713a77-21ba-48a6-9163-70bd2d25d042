package com.imile.permission.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Data
public class BatchUserRoleApiDTO implements Serializable {

    /**
     * 员工code，最多50个
     */
    private List<String> userCodeList;

    /**
     * 需要解绑的角色id
     */
    private Long removeRoleId;

    /**
     * 需要绑定的角色id
     */
    private Long addRoleId;
}

package com.imile.permission.api;

import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * 废弃，2024-07-31 下线
 *
 * <AUTHOR>
 * @since 2024/1/25
 */
@Deprecated
public interface PermissionManagerApi {

    /**
     * 新增某个数据类型下的子数据权限
     *
     * @param typeCode typeCode
     * @param parentId parentId
     * @param newId    newId
     * @return RpcResult<Boolean>
     */
    RpcResult<Boolean> addDataPermission(String typeCode, String parentId, String newId);


    /**
     * 变更某个数据类型下的子数据权限
     *
     * @param typeCode  typeCode
     * @param parentId  parentId
     * @param currentId currentId
     * @return RpcResult<Boolean>
     */
    RpcResult<Boolean> changeDataPermission(String typeCode, String parentId, String currentId, List<String> subIdList);
}

<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.imile</groupId>
        <artifactId>com.imile.framework.jar</artifactId>
        <version>1.0.12</version>
    </parent>

    <groupId>com.imile</groupId>
    <artifactId>permission</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>


    <modules>
        <module>permission-common</module>
        <module>permission-config</module>
        <module>permission-dao</module>
        <module>permission-manage</module>
        <module>permission-service</module>
        <module>permission-web</module>
        <module>permission-integration</module>
        <module>permission-domain</module>
    </modules>

    <properties>
        <revision>1.0.0</revision>
        <start-class>com.imile.permission.Application</start-class>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>


        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <mybatis.plus.version>3.4.0</mybatis.plus.version>
        <mysql.version>8.0.22</mysql.version>
        <dubbo.starter.version>2.7.4.1</dubbo.starter.version>
        <xxl.job.version>2.1.2</xxl.job.version>
        <!-- 该版本对应springboot 2.3.2.RELEASE -->
        <spring.cloud.context.version>2.2.0.RELEASE</spring.cloud.context.version>
        <imile.sentinel.version>1.0.0</imile.sentinel.version>
        <logcenter.producer.version>1.0.8</logcenter.producer.version>

        <framework.rpc.version>1.0.12</framework.rpc.version>
        <framework.common.version>1.0.12</framework.common.version>
        <ucenter.version>1.0.13</ucenter.version>
        <hrms-api.version>1.7.68</hrms-api.version>

        <bpm-api.version>1.0.24</bpm-api.version>
        <permission-api.version>1.0.31</permission-api.version>
        <resource-api.version>1.0.10</resource-api.version>
        <permission-jcasbin-api.version>1.0.7</permission-jcasbin-api.version>
        <!--    hermes 测试和开发一个分支，所以只用 test    -->
        <hermes-api.version>2.11.74</hermes-api.version>
        <imile.rocketmq.version>1.0.5</imile.rocketmq.version>
        <lombok.version>1.18.8</lombok.version>
        <velocity-engine-core.version>2.3</velocity-engine-core.version>
        <bcprov-jdk15to18.version>1.68</bcprov-jdk15to18.version>
        <alibaba-dingtalk-service-sdk.version>2.0.0</alibaba-dingtalk-service-sdk.version>
        <orika-core.version>1.5.4</orika-core.version>
        <groovy-all.version>2.4.16</groovy-all.version>
        <unirest-java.version>3.14.2</unirest-java.version>
        <xxl-job-core.version>2.1.2</xxl-job-core.version>
        <curator-recipes.revision>4.2.0</curator-recipes.revision>
        <elasticsearch.revision>7.17.12</elasticsearch.revision>
        <apm-toolkit-log4j2.revision>8.0.1</apm-toolkit-log4j2.revision>
        <message.version>1.0.22</message.version>

        <ipep.version>1.0.0-SNAPSHOT</ipep.version>
        <rocketmq.client.version>4.7.1</rocketmq.client.version>
        <genesis-api.version>1.0.8</genesis-api.version>
        <permission-auth-flow-api.version>1.1.0</permission-auth-flow-api.version>
        <easy-log.version>1.0.0</easy-log.version>
        <dynamic.datasource.version>3.0.0</dynamic.datasource.version>
        <HikariCP.version>3.4.1</HikariCP.version>
        <pcs-api.version>1.0.2</pcs-api.version>
        <lm-express-base-api.version>1.5.22</lm-express-base-api.version>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>ucenter-api</artifactId>
                <version>${ucenter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.imile</groupId>
                        <artifactId>com.imile.framework.facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.imile</groupId>
                        <artifactId>com.imile.framework.rpc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>jakarta.validation</groupId>
                        <artifactId>jakarta.validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>com.imile.framework.rpc</artifactId>
                <version>${framework.rpc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>com.imile.framework.common</artifactId>
                <version>${framework.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>com.imile.framework.aliyun</artifactId>
                <version>${framework.common.version}</version>
                <exclusions>
                    <!-- 这里将以下依赖全部排除掉,在项目基本不要使用里面的拦截器 -->
                    <exclusion>
                        <groupId>com.imile</groupId>
                        <artifactId>com.imile.framework.facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>commons-collections</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>logcenter-producer</artifactId>
                <version>${logcenter.producer.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.imile</groupId>
                        <artifactId>com.imile.framework.common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 支持apollo动态刷新，需要引入该包 https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-context -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>${spring.cloud.context.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>hrms-api</artifactId>
                <version>${hrms-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>bpm-api</artifactId>
                <version>${bpm-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>hermes-api</artifactId>
                <version>${hermes-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>ipep-api</artifactId>
                <version>${ipep.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity-engine-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bcprov-jdk15to18.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${alibaba-dingtalk-service-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>${orika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-dao</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-config</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-domain</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-integration</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-manage</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-api</artifactId>
                <version>${permission-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>resource-api</artifactId>
                <version>${resource-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-jcasbin-api</artifactId>
                <version>${permission-jcasbin-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>${groovy-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.konghq</groupId>
                <artifactId>unirest-java</artifactId>
                <version>${unirest-java.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator-recipes.revision}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.revision}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.revision}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-log4j-2.x</artifactId>
                <version>${apm-toolkit-log4j2.revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>message-api</artifactId>
                <version>${message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>genesis-api</artifactId>
                <version>${genesis-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-auth-flow-api</artifactId>
                <version>${permission-auth-flow-api.version}</version>

            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>pcs-api</artifactId>
                <version>${pcs-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>lm-express-base-api</artifactId>
                <version>${lm-express-base-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github</groupId>
                <artifactId>easy-log</artifactId>
                <version>${easy-log.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId> <!-- 防止版本冲突 -->
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${HikariCP.version}</version> <!-- 强制锁定版本 -->
            </dependency>


        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>utf-8</encoding>
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.8.RELEASE</version>
                <configuration>
                    <mainClass>com.imile.permission.Application</mainClass>
                    <layout>ZIP</layout>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version> <!-- 一个与 Java 8 兼容的版本 -->
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.imile.com/nexus/content/repositories/releases</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.imile.com/nexus/content/repositories/snapshots</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>

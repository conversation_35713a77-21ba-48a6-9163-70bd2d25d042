package com.imile.permission.log;

import com.github.easylog.model.EasyLogInfo;
import com.github.easylog.service.ILogRecordService;
import com.github.easylog.util.PlaceholderResolver;
import com.imile.permission.mq.basic.ProducerBasicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */
@Service
@Slf4j
public class PermissionLogRecordService implements ILogRecordService {

    @Value("${topic.permission.log}")
    private String topicPermissionLog;
    @Autowired
    private ProducerBasicService producerBasicService;

    @Override
    public void record(EasyLogInfo easyLogInfo) {

        //记录条件为 false，则不记录
        if (!StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), easyLogInfo.getCondition())) {
            producerBasicService.sendMessage(topicPermissionLog, null, null, easyLogInfo);
        }
        String resolve = PlaceholderResolver.getDefaultResolver().resolve(easyLogInfo.getContent(), easyLogInfo.getContentParam());
        easyLogInfo.setContent(resolve);
        log.info("PermissionLogRecordService | record | 操作日志={}", easyLogInfo);
    }
}

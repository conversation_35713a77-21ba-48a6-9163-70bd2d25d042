package com.imile.permission.utils.authorityApproval.stategy.dataDeserializer;

import com.alibaba.fastjson.JSON;
import com.imile.permission.domain.dataPermission.vo.MultiDynamicDataConfigValueDTO;
import com.imile.permission.enums.PermissionDataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Slf4j
public class MultiDimensionDataDeserializer implements DataPermissionDeserializer {
    @Override
    public boolean canDeserialize(Integer configType) {
        return PermissionDataTypeEnum.MULTI_DIMENSION_DATA.getCode().equals(configType);
    }

    @Override
    public Object deserialize(String contentJson) {
        if (StringUtils.isBlank(contentJson)) {
            log.info("MultiDimensionDataDeserializer | deserialize | Empty JSON content for multi dimension data permission");
            return Collections.emptyList();
        }
        try {
            return JSON.parseArray(contentJson, MultiDynamicDataConfigValueDTO.class);
        } catch (Exception e) {
            log.info("MultiDimensionDataDeserializer | deserialize | multi dimension data deserialization failed. JSON: {}", contentJson);
            return Collections.emptyList();
        }
    }
}
package com.imile.permission.utils.authorityApproval.stategy.dataProcessor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.entity.AuthorityApprovalDO;
import com.imile.permission.domain.entity.AuthorityApprovalDataDO;
import com.imile.permission.enums.PermissionDataTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

import static com.imile.permission.utils.authorityApproval.builder.AuthorityApprovalDataBuilder.buildAuthorityApprovalDataDO;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
public class MainDataProcessor implements DataPermissionProcessor {
    @Override
    public boolean canProcess(List<?> dataPermissionDTO) {
        return CollectionUtils.isNotEmpty(dataPermissionDTO);
    }

    @Override
    public AuthorityApprovalDataDO process(AuthorityApprovalDO authorityApproval, List<?> dataPermissionDTO) {
        List<DataPermissionRuleDTO> dtos = (List<DataPermissionRuleDTO>) dataPermissionDTO;
        Set<String> typeCodeSet = extractTypeCodes(dtos);
        
        if (CollectionUtils.isEmpty(typeCodeSet)) {
            return null;
        }
        
        return buildAuthorityApprovalDataDO(authorityApproval, typeCodeSet, 
                JSON.toJSONString(dtos), PermissionDataTypeEnum.MAIN_DATA.getCode());
    }

    private Set<String> extractTypeCodes(List<DataPermissionRuleDTO> dtos) {
        Set<String> typeCodeSet = Sets.newHashSet();
        for (DataPermissionRuleDTO dto : dtos) {
            if (StringUtils.isNotBlank(dto.getTypeCode())) {
                typeCodeSet.add(dto.getTypeCode());
            }
        }
        return typeCodeSet;
    }
}
package com.imile.permission.service.rpc.common;

import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.MenuPermissionApiDTO;
import com.imile.permission.api.dto.PermissionApiDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.util.OrikaUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/15
 */
public class RpcDataConvert {

    public static PermissionApiDTO convertPermissionApiDTO(
            List<DataPermissionDTO> dataPermissionDTOList,
            List<MenuPermissionDTO> menuPermissionDTOList
    ) {
        PermissionApiDTO permissionApiDTO = new PermissionApiDTO();
        permissionApiDTO.setDataPermissionDTOList(OrikaUtil.mapAsList(dataPermissionDTOList, DataPermissionApiDTO.class));
        permissionApiDTO.setMenuPermissionDTOList(OrikaUtil.mapAsList(menuPermissionDTOList, MenuPermissionApiDTO.class));
        return permissionApiDTO;
    }

    public static PermissionApiDTO convertPermissionApiDTO(List<Long> roleList, List<DataPermissionDTO> dataPermissionDTOList, List<MenuPermissionDTO> menuPermissionDTOList) {
        PermissionApiDTO permissionApiDTO = new PermissionApiDTO();
        permissionApiDTO.setRoleList(roleList);
        permissionApiDTO.setDataPermissionDTOList(OrikaUtil.mapAsList(dataPermissionDTOList, DataPermissionApiDTO.class));
        permissionApiDTO.setMenuPermissionDTOList(OrikaUtil.mapAsList(menuPermissionDTOList, MenuPermissionApiDTO.class));
        return permissionApiDTO;
    }
}

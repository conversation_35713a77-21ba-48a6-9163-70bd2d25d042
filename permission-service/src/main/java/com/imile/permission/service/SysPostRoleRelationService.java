package com.imile.permission.service;


import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.user.dto.UserPostChangeDTO;
import com.imile.permission.domain.applicationApprove.vo.EffectiveLossCountVO;
import com.imile.permission.domain.relation.dto.UserPostDTO;
import com.imile.permission.domain.relation.param.BatchBindingParam;
import com.imile.permission.domain.relation.param.PostQueryParam;
import com.imile.permission.domain.relation.query.PostRoleDetailsQuery;
import com.imile.permission.domain.relation.query.PostRolePageQuery;
import com.imile.permission.domain.relation.vo.PostRoleDetailsVO;
import com.imile.permission.domain.relation.vo.PostRoleVO;
import com.imile.permission.domain.relation.vo.PostVO;
import com.imile.permission.domain.relation.vo.SysPostRoleRelationDetailVO;
import com.imile.permission.domain.role.param.RoleIdPageQueryParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SysPostRoleRelationService {

    /**
     * 获取系统岗位角色关联关系详情
     *
     * @param id id
     * @return SysPostRoleRelationDetailVO
     */
    SysPostRoleRelationDetailVO postRoleDetail(Long id);

    /**
     * 岗位分页查询
     *
     * @param postQueryParam postQueryParam
     * @return PaginationResult<PostVO>
     */
    PaginationResult<PostVO> findPostPage(PostQueryParam postQueryParam);

    /**
     * 批量关联角色
     *
     * @param batchBindingParam batchBindingParam
     * @return
     */
    Boolean batchBindingRoleForPost(BatchBindingParam batchBindingParam);


    /**
     * 岗位列表
     *
     * @param postQueryParam postQueryParam
     * @return List<PostVO>
     */
    List<PostVO> findPostList(PostQueryParam postQueryParam);

    /**
     * 员工的岗位变更
     * @param eventDTO
     */
    void userPostChange(UserPostDTO eventDTO);

    EffectiveLossCountVO getRoleCount(Long postId);

    PaginationResult<PostRoleVO> getRolePage(PostRolePageQuery pageQuery);

    List<PostRoleDetailsVO> getRoleDetails(PostRoleDetailsQuery postRoleDetailsQuery);

    void handleHrmsPermissionUserEntryEvent(String userCode);

    /**
     * @param roleIdPageQueryParam
     * @return
     */
    PaginationResult<PostVO> getRelatedPostByRole(RoleIdPageQueryParam roleIdPageQueryParam);
}

package com.imile.permission.service;

import com.imile.common.page.PaginationResult;
import com.imile.permission.domain.applicationApprove.vo.EffectiveLossCountVO;
import com.imile.permission.domain.client.param.BindingParentRoleParam;
import com.imile.permission.domain.client.param.ClientRoleRevokeParam;
import com.imile.permission.domain.client.query.ClientRecordPageQuery;
import com.imile.permission.domain.client.query.ClientRoleDetailsQuery;
import com.imile.permission.domain.client.vo.ClientRoleDetailsVO;
import com.imile.permission.domain.client.vo.ClientRoleRecordVO;
import com.imile.permission.domain.role.vo.ParentChildRoleVO;
import com.imile.permission.domain.user.vo.ParentChildClientRoleMenuVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
public interface ClientAuthorizationService {
    ParentChildRoleVO getBindingRole(String clientCode);

    List<ParentChildRoleVO> parentChildRoleList();

    void bindingParentRole(BindingParentRoleParam bindingParentRoleParam);

    PaginationResult<ClientRoleRecordVO> roleRecordPage(ClientRecordPageQuery clientRecordPageQuery);

    EffectiveLossCountVO getRecordCount(String clientCode);

    void revokeRole(ClientRoleRevokeParam param);

    ParentChildClientRoleMenuVO parentChildClientRoleMenu(Long roleId);

    ParentChildClientRoleMenuVO clientMenu(String clientCode);

    List<ClientRoleDetailsVO> getRoleApplicationDetails(ClientRoleDetailsQuery roleApplicationDetailsQuery);

}

package com.imile.permission.service.impl;

import com.imile.bpm.enums.ApprovalRoleEnum;
import com.imile.bpm.enums.ApprovalRoleValueEnum;
import com.imile.bpm.mq.dto.ApprovalCreateRoleUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalCreateUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalDetailChildInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordChildDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordWholeDTO;
import com.imile.bpm.mq.dto.ApprovalMergeDetailGroupDTO;
import com.imile.bpm.mq.dto.ApprovalUserInfoApiDTO;
import com.imile.hrms.api.organization.dto.OrgDeptApiDTO;
import com.imile.permission.context.RequestInfoHolder;
import com.imile.permission.domain.applicationApprove.dto.ApprovalCreateRoleUserDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalCreateUserDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalDetailChildInfoDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalDetailStepRecordChildDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalDetailStepRecordDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalDetailStepRecordV2DTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalDetailStepRecordWholeDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalMergeDetailProcessRecordDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalPreviewErrorUserDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalPreviewSuccessUserDTO;
import com.imile.permission.domain.applicationApprove.dto.ApprovalUserInfoDTO;
import com.imile.permission.domain.country.dto.CountryDTO;
import com.imile.permission.domain.user.dto.UserInfoDetailDTO;
import com.imile.permission.integration.hermes.CountryIntegration;
import com.imile.permission.integration.hrms.client.BizOrganizationInfoApiClient;
import com.imile.permission.service.ApprovalPreviewService;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/11
 */
@Slf4j
@Service
public class ApprovalPerviewServiceImpl implements ApprovalPreviewService {


    /**
     * 国家与对应主要hr负责人映射
     */
    @Value("#{${country.main.poc.map:{UAE:'2102255',OMN:'2102255',KSA:'2102474',CHN:'2101947',MEX:'2104959',TUR:'2102354',BRA:'2105554',KWT:'2103221',QAT:'2103221',JOR:'2103221',BHR:'2103221',LBN:'2103221',HQ:'2104453',POL:'2104453',DEU:'2104453',FRA:'2104453',GBR:'2104453',CHL:'2104453',RSA:'2104453',NLD:'2104453',AUS:'2104453',ITA:'2104453'}}}")
    private Map<String, String> countryMainPocMap;

    @Autowired
    private CountryIntegration countryIntegration;

    @Autowired
    private BizOrganizationInfoApiClient bizOrganizationInfoApiClient;

    @Override
    public void previewDTOBuild(ApprovalEmptyInfoApiDTO approvalEmptyInfoApiDTO, ApprovalDetailStepRecordV2DTO resultDTO) {
        resultDTO.setHasChildInfo(approvalEmptyInfoApiDTO.getHasChildInfo());
        resultDTO.setIsExpandApprovalStep(approvalEmptyInfoApiDTO.getIsExpandApprovalStep());
        //子单转换
        List<ApprovalDetailStepRecordChildDTO> approvalDetailStepRecordChildDTOS = new ArrayList<>();
        List<ApprovalEmptyRecordChildDTO> approvalEmptyRecordChildDTOS = approvalEmptyInfoApiDTO.getApprovalEmptyRecordChildDTOS();
        if (approvalEmptyRecordChildDTOS == null) {
            approvalEmptyRecordChildDTOS = new ArrayList<>();
        }
        for (ApprovalEmptyRecordChildDTO childDTO : approvalEmptyRecordChildDTOS) {
            ApprovalDetailStepRecordChildDTO detailStepRecordChildDTO = new ApprovalDetailStepRecordChildDTO();
            detailStepRecordChildDTO.setApprovalChildId(childDTO.getApprovalChildId());
            detailStepRecordChildDTO.setApprovalChildInfoName(childDTO.getApprovalChildInfoName());
            List<ApprovalDetailStepRecordDTO> approvalDetailStepRecordDTOS = new ArrayList<>();
            detailStepRecordChildDTO.setApprovalDetailStepRecordDTOS(approvalDetailStepRecordDTOS);
            List<ApprovalEmptyRecordApiDTO> approvalEmptyRecordApiDTOS = childDTO.getApprovalEmptyRecordApiDTOS();
            if (approvalEmptyRecordApiDTOS == null) {
                approvalEmptyRecordApiDTOS = new ArrayList<>();
            }
            int tag = 1;
            for (ApprovalEmptyRecordApiDTO recordApiDTO : approvalEmptyRecordApiDTOS) {
                ApprovalDetailStepRecordDTO approvalDetailStepRecordDTO = new ApprovalDetailStepRecordDTO();
                approvalDetailStepRecordDTO.setStepId(recordApiDTO.getStepId());
                approvalDetailStepRecordDTO.setStepName(recordApiDTO.getStepName());
                approvalDetailStepRecordDTO.setRecordStatus(1);
                approvalDetailStepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "审批中" : "In review");
                if (tag == 1) {
                    approvalDetailStepRecordDTO.setRecordStatus(-1);
                    approvalDetailStepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                    approvalDetailStepRecordDTO.setRecordStatusUpdateDate(new Date());
                }
                approvalDetailStepRecordDTO.setApprovalRecordType("approval");
                approvalDetailStepRecordDTO.setApprovalUserInfoDTOS(new ArrayList<>());
                if (CollectionUtils.isNotEmpty(recordApiDTO.getApprovalUserInfoDTOS())) {
                    approvalDetailStepRecordDTO.setApprovalUserInfoDTOS(BeanUtils.convert(ApprovalUserInfoDTO.class, recordApiDTO.getApprovalUserInfoDTOS()));
                    approvalDetailStepRecordDTO.getApprovalUserInfoDTOS().forEach(item -> {
                        item.setApprovalOperation("APPROVING");
                    });
                }
                //具体信息
                ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
                approvalDetailStepRecordDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);
                ApprovalCreateUserApiDTO approvalCreateUserApiDTO = recordApiDTO.getApprovalCreateUserApiDTO();
                if (approvalCreateUserApiDTO != null) {
                    List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
                    List<ApprovalUserInfoApiDTO> approvalUserInfos = approvalCreateUserApiDTO.getApprovalUserInfos();
                    if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                        approvalCreateRoleUserApiDTOS = new ArrayList<>();
                    }
                    if (CollectionUtils.isEmpty(approvalUserInfos)) {
                        approvalUserInfos = new ArrayList<>();
                    }
                    List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
                    for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                        ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                        roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                        roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                        roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                        roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                        roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                        roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                        roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                        if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                            roleUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, roleUserApiDTO.getApprovalUserInfos()));
                        }
                        approvalCreateRoleUserDTOS.add(roleUserDTO);
                    }
                    approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
                    approvalCreateUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfos));
                }
                approvalDetailStepRecordDTOS.add(approvalDetailStepRecordDTO);
                tag++;
            }
            approvalDetailStepRecordChildDTOS.add(detailStepRecordChildDTO);
        }
        resultDTO.setApprovalDetailStepRecordChildDTOS(approvalDetailStepRecordChildDTOS);

        //主流程转换
        List<ApprovalDetailStepRecordWholeDTO> approvalDetailStepRecordWholeDTOS = new ArrayList<>();
        List<ApprovalEmptyRecordWholeDTO> approvalEmptyRecordWholeDTOS = approvalEmptyInfoApiDTO.getApprovalEmptyRecordWholeDTOS();
        if (approvalEmptyRecordWholeDTOS == null) {
            approvalEmptyRecordWholeDTOS = new ArrayList<>();
        }
        int temp = 1;
        for (ApprovalEmptyRecordWholeDTO wholeDTO : approvalEmptyRecordWholeDTOS) {
            ApprovalDetailStepRecordWholeDTO approvalDetailStepRecordWholeDTO = new ApprovalDetailStepRecordWholeDTO();
            approvalDetailStepRecordWholeDTO.setStepName(wholeDTO.getStepName());
            approvalDetailStepRecordWholeDTO.setApprovalRecordType(wholeDTO.getApprovalRecordType());
            approvalDetailStepRecordWholeDTO.setRecordStatus(1);
            approvalDetailStepRecordWholeDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "审批中" : "In review");
            if (temp == 1) {
                approvalDetailStepRecordWholeDTO.setRecordStatus(-1);
                approvalDetailStepRecordWholeDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                approvalDetailStepRecordWholeDTO.setRecordStatusUpdateDate(new Date());
            }
            //组流程(若无组流程  直接取审批人员)
            List<ApprovalMergeDetailProcessRecordDTO> mergeProcessRecordDTOS = new ArrayList<>();
            List<ApprovalMergeDetailGroupDTO> approvalMergeDetailGroupDTOS = wholeDTO.getApprovalMergeDetailGroupDTOS();
            if (CollectionUtils.isEmpty(approvalMergeDetailGroupDTOS)) {
                approvalMergeDetailGroupDTOS = new ArrayList<>();
            }
            for (ApprovalMergeDetailGroupDTO groupDTO : approvalMergeDetailGroupDTOS) {
                ApprovalMergeDetailProcessRecordDTO recordDTO = new ApprovalMergeDetailProcessRecordDTO();

                List<ApprovalDetailChildInfoApiDTO> detailChildInfoApiDTOS = groupDTO.getDetailChildInfoApiDTOS();
                if (CollectionUtils.isEmpty(detailChildInfoApiDTOS)) {
                    detailChildInfoApiDTOS = new ArrayList<>();
                }
                recordDTO.setDetailChildInfoDTOS(BeanUtils.convert(ApprovalDetailChildInfoDTO.class, detailChildInfoApiDTOS));

                List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS = groupDTO.getApprovalUserInfoDTOS();
                if (CollectionUtils.isEmpty(approvalUserInfoApiDTOS)) {
                    approvalUserInfoApiDTOS = new ArrayList<>();
                }
                recordDTO.setApprovalUserInfoDTOS(BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfoApiDTOS));

                ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
                ApprovalCreateUserApiDTO approvalCreateUserApiDTO = groupDTO.getApprovalCreateUserApiDTO();
                if (approvalCreateUserApiDTO != null) {
                    List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
                    List<ApprovalUserInfoApiDTO> approvalUserInfos = approvalCreateUserApiDTO.getApprovalUserInfos();
                    if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                        approvalCreateRoleUserApiDTOS = new ArrayList<>();
                    }
                    if (CollectionUtils.isEmpty(approvalUserInfos)) {
                        approvalUserInfos = new ArrayList<>();
                    }
                    List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
                    for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                        ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                        roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                        roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                        roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                        roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                        roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                        roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                        roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                        if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                            roleUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, roleUserApiDTO.getApprovalUserInfos()));
                        }
                        approvalCreateRoleUserDTOS.add(roleUserDTO);
                    }
                    approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
                    approvalCreateUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfos));
                }
                recordDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);
                mergeProcessRecordDTOS.add(recordDTO);
            }
            approvalDetailStepRecordWholeDTO.setMergeProcessRecordDTOS(mergeProcessRecordDTOS);


            //审批人员
            List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS = wholeDTO.getApprovalUserInfoDTOS();
            if (CollectionUtils.isEmpty(approvalUserInfoApiDTOS)) {
                approvalUserInfoApiDTOS = new ArrayList<>();
            }
            List<ApprovalUserInfoDTO> approvalUserInfoDTOS = BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfoApiDTOS);
            approvalDetailStepRecordWholeDTO.setApprovalUserInfoDTOS(approvalUserInfoDTOS);


            ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = wholeDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO != null) {
                List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
                List<ApprovalUserInfoApiDTO> approvalUserInfos = approvalCreateUserApiDTO.getApprovalUserInfos();
                if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                    approvalCreateRoleUserApiDTOS = new ArrayList<>();
                }
                if (CollectionUtils.isEmpty(approvalUserInfos)) {
                    approvalUserInfos = new ArrayList<>();
                }
                List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
                for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                    ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                    roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                    roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                    roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                    roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                    roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                    roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                    roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                    if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                        roleUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, roleUserApiDTO.getApprovalUserInfos()));
                    }
                    approvalCreateRoleUserDTOS.add(roleUserDTO);
                }
                approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
                approvalCreateUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfos));
            }
            approvalDetailStepRecordWholeDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);

            approvalDetailStepRecordWholeDTOS.add(approvalDetailStepRecordWholeDTO);
            temp++;
        }
        resultDTO.setApprovalDetailStepRecordWholeDTOS(approvalDetailStepRecordWholeDTOS);
    }

    @Override
    public void previewInfoBuild(String userCode, ApprovalDetailStepRecordV2DTO resultDTO) {
        //该流程所有找不到的人员信息
        List<ApprovalPreviewErrorUserDTO> allApprovalPreviewErrorUserDTOList = new ArrayList<>();
        resultDTO.setAllApprovalPreviewErrorUserDTOList(allApprovalPreviewErrorUserDTOList);

        List<String> allAppointUserCodeList = new ArrayList<>();
        List<String> allRoleUserCodeList = new ArrayList<>();
        //获取所有国家的HR信息
        List<String> hrUserCodeList = new ArrayList<>();
        countryMainPocMap.forEach((k, v) -> hrUserCodeList.add(v));
        List<UserInfoDetailDTO> allHrUserInfoList = bizOrganizationInfoApiClient.userInfoList(hrUserCodeList);
        Map<String, List<UserInfoDetailDTO>> hrUserCodeMap = allHrUserInfoList.stream().collect(Collectors.groupingBy(o -> o.getUserCode()));

        //获取所有国家信息
        List<CountryDTO> countryDTOS = countryIntegration.queryAllCountryConfigList();
        Map<String, List<CountryDTO>> countryMap = countryDTOS.stream().collect(Collectors.groupingBy(o -> o.getCountryName()));

        //流程详情(整体流程)获取本次流程所有用户
        List<ApprovalDetailStepRecordWholeDTO> approvalDetailStepRecordWholeDTOS = resultDTO.getApprovalDetailStepRecordWholeDTOS();
        List<ApprovalDetailStepRecordChildDTO> approvalDetailStepRecordChildDTOS = resultDTO.getApprovalDetailStepRecordChildDTOS();
        for (ApprovalDetailStepRecordWholeDTO wholeDTO : approvalDetailStepRecordWholeDTOS) {
            //所有查询到的用户
            ApprovalCreateUserDTO approvalCreateUserDTO = wholeDTO.getApprovalCreateUserDTO();
            if (approvalCreateUserDTO == null) {
                approvalCreateUserDTO = new ApprovalCreateUserDTO();
            }
            //所有指定人员
            List<ApprovalUserInfoDTO> appointUserList = approvalCreateUserDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(appointUserList)) {
                appointUserList = new ArrayList<>();
            }
            List<String> appointUserCodeList = appointUserList.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
            allAppointUserCodeList.addAll(appointUserCodeList);

            //所有角色对应人员
            List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = approvalCreateUserDTO.getApprovalCreateRoleUserDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserDTOS)) {
                approvalCreateRoleUserDTOS = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserDTO roleUserDTO : approvalCreateRoleUserDTOS) {
                List<ApprovalUserInfoDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
                if (CollectionUtils.isEmpty(roleUserInfos)) {
                    continue;
                }
                List<String> roleUserCodeList = roleUserInfos.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
                allRoleUserCodeList.addAll(roleUserCodeList);
            }

            //获取整体流程中每组的信息
            List<ApprovalMergeDetailProcessRecordDTO> mergeProcessRecordDTOS = wholeDTO.getMergeProcessRecordDTOS();
            if (CollectionUtils.isEmpty(mergeProcessRecordDTOS)) {
                mergeProcessRecordDTOS = new ArrayList<>();
            }
            for (ApprovalMergeDetailProcessRecordDTO mergeDetailProcessRecordDTO : mergeProcessRecordDTOS) {
                ApprovalCreateUserDTO branchApprovalCreateUserDTO = mergeDetailProcessRecordDTO.getApprovalCreateUserDTO();
                if (branchApprovalCreateUserDTO == null) {
                    branchApprovalCreateUserDTO = new ApprovalCreateUserDTO();
                }
                //所有指定人员
                List<ApprovalUserInfoDTO> branchAppointUserList = branchApprovalCreateUserDTO.getApprovalUserInfos();
                if (CollectionUtils.isEmpty(branchAppointUserList)) {
                    branchAppointUserList = new ArrayList<>();
                }
                List<String> branchAppointUserCodeList = branchAppointUserList.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
                allAppointUserCodeList.addAll(branchAppointUserCodeList);

                //所有角色对应人员
                List<ApprovalCreateRoleUserDTO> branchApprovalCreateRoleUserDTOS = approvalCreateUserDTO.getApprovalCreateRoleUserDTOS();
                if (CollectionUtils.isEmpty(branchApprovalCreateRoleUserDTOS)) {
                    branchApprovalCreateRoleUserDTOS = new ArrayList<>();
                }
                for (ApprovalCreateRoleUserDTO roleUserDTO : branchApprovalCreateRoleUserDTOS) {
                    List<ApprovalUserInfoDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
                    if (CollectionUtils.isEmpty(roleUserInfos)) {
                        continue;
                    }
                    List<String> roleUserCodeList = roleUserInfos.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
                    allRoleUserCodeList.addAll(roleUserCodeList);
                }
            }
        }
        List<String> allUserCodeList = new ArrayList<>();
        allUserCodeList.add(userCode);
        allUserCodeList.addAll(allAppointUserCodeList);
        allUserCodeList.addAll(allRoleUserCodeList);

        //调用HR接口，获取所有人员信息
        List<UserInfoDetailDTO> allUserInfoList = bizOrganizationInfoApiClient.userInfoList(allUserCodeList);
        Map<String, List<UserInfoDetailDTO>> userCodeMap = allUserInfoList.stream().collect(Collectors.groupingBy(UserInfoDetailDTO::getUserCode));

        //整体流程
        for (ApprovalDetailStepRecordWholeDTO wholeDTO : approvalDetailStepRecordWholeDTOS) {
            //整体流程主节点信息
            List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList = new ArrayList<>();
            List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList = new ArrayList<>();
            //所有查询到的用户
            List<ApprovalUserInfoDTO> findUserList = wholeDTO.getApprovalUserInfoDTOS();

            ApprovalCreateUserDTO approvalCreateUserDTO = wholeDTO.getApprovalCreateUserDTO();
            if (approvalCreateUserDTO == null) {
                approvalCreateUserDTO = new ApprovalCreateUserDTO();
            }
            previewUserInfoFind(userCode, countryMap, hrUserCodeMap, userCodeMap, findUserList, approvalCreateUserDTO, approvalPreviewErrorUserDTOList, approvalPreviewSuccessUserDTOList);

            approvalCreateUserDTO.setApprovalPreviewErrorUserDTOList(approvalPreviewErrorUserDTOList);
            approvalCreateUserDTO.setApprovalPreviewSuccessUserDTOList(approvalPreviewSuccessUserDTOList);
            allApprovalPreviewErrorUserDTOList.addAll(approvalPreviewErrorUserDTOList);
            //整体流程中的子流程合单
            List<ApprovalMergeDetailProcessRecordDTO> mergeProcessRecordDTOS = wholeDTO.getMergeProcessRecordDTOS();
            if (CollectionUtils.isEmpty(mergeProcessRecordDTOS)) {
                mergeProcessRecordDTOS = new ArrayList<>();
            }
            for (ApprovalMergeDetailProcessRecordDTO recordDTO : mergeProcessRecordDTOS) {
                List<ApprovalPreviewErrorUserDTO> branchApprovalPreviewErrorUserDTOList = new ArrayList<>();
                List<ApprovalPreviewSuccessUserDTO> branchApprovalPreviewSuccessUserDTOList = new ArrayList<>();
                //所有查询到的用户
                List<ApprovalUserInfoDTO> branchFindUserList = recordDTO.getApprovalUserInfoDTOS();
                ApprovalCreateUserDTO branchApprovalCreateUserDTO = wholeDTO.getApprovalCreateUserDTO();
                if (branchApprovalCreateUserDTO == null) {
                    branchApprovalCreateUserDTO = new ApprovalCreateUserDTO();
                }
                previewUserInfoFind(userCode, countryMap, hrUserCodeMap, userCodeMap, branchFindUserList, branchApprovalCreateUserDTO, branchApprovalPreviewErrorUserDTOList, branchApprovalPreviewSuccessUserDTOList);

                branchApprovalCreateUserDTO.setApprovalPreviewErrorUserDTOList(branchApprovalPreviewErrorUserDTOList);
                branchApprovalCreateUserDTO.setApprovalPreviewSuccessUserDTOList(branchApprovalPreviewSuccessUserDTOList);
                allApprovalPreviewErrorUserDTOList.addAll(branchApprovalPreviewErrorUserDTOList);
            }
        }

        //流程详情(子单)
        for (ApprovalDetailStepRecordChildDTO childDTO : approvalDetailStepRecordChildDTOS) {
            List<ApprovalDetailStepRecordDTO> approvalDetailStepRecordDTOS = childDTO.getApprovalDetailStepRecordDTOS();
            if (CollectionUtils.isEmpty(approvalDetailStepRecordDTOS)) {
                approvalDetailStepRecordDTOS = new ArrayList<>();
            }
            for (ApprovalDetailStepRecordDTO recordDTO : approvalDetailStepRecordDTOS) {
                //整体流程主节点信息
                List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList = new ArrayList<>();
                List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList = new ArrayList<>();
                //所有查询到的用户
                List<ApprovalUserInfoDTO> findUserList = recordDTO.getApprovalUserInfoDTOS();

                ApprovalCreateUserDTO approvalCreateUserDTO = recordDTO.getApprovalCreateUserDTO();
                if (approvalCreateUserDTO == null) {
                    approvalCreateUserDTO = new ApprovalCreateUserDTO();
                }
                previewUserInfoFind(userCode, countryMap, hrUserCodeMap, userCodeMap, findUserList, approvalCreateUserDTO, approvalPreviewErrorUserDTOList, approvalPreviewSuccessUserDTOList);

                approvalCreateUserDTO.setApprovalPreviewErrorUserDTOList(approvalPreviewErrorUserDTOList);
                approvalCreateUserDTO.setApprovalPreviewSuccessUserDTOList(approvalPreviewSuccessUserDTOList);
            }
        }
    }

    private void previewUserInfoFind(String applyUserCode, Map<String, List<CountryDTO>> countryMap, Map<String, List<UserInfoDetailDTO>> hrUserCodeMap, Map<String, List<UserInfoDetailDTO>> userCodeMap, List<ApprovalUserInfoDTO> approvalUserInfoDTOS, ApprovalCreateUserDTO approvalCreateUserDTO, List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList, List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList) {
        List<String> findUserCodeList = approvalUserInfoDTOS.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
        //所有指定人员
        List<ApprovalUserInfoDTO> appointUserList = approvalCreateUserDTO.getApprovalUserInfos();
        if (CollectionUtils.isEmpty(appointUserList)) {
            appointUserList = new ArrayList<>();
        }
        for (ApprovalUserInfoDTO userInfoDTO : appointUserList) {
            List<UserInfoDetailDTO> userInfoDetailApiDTOList = userCodeMap.get(userInfoDTO.getUserCode());
            if (CollectionUtils.isEmpty(userInfoDetailApiDTOList)) {
                continue;
            }
            //Todo 待注释-调用HR接口，获取所有人员信息
            UserInfoDetailDTO hrUser = userInfoDetailApiDTOList.get(0);
            String hrUserCode = countryMainPocMap.get(hrUser.getOriginCountry());
            String userHrMessage = hrUserCode;
            if (StringUtils.isNotBlank(hrUserCode)) {
                List<UserInfoDetailDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                    userHrMessage = hrUserDetail.get(0).getUserName();
                }
            }
            if (findUserCodeList.contains(userInfoDTO.getUserCode())) {
                //指定人员存在
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? hrUser.getUserName() + " 是指定员工" : hrUser.getUserName() + " is fixed in process");
                successUserDTO.setUserHrMessage(userHrMessage);
                successUserDTO.setTemp(1);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                continue;
            }
            //指定人员不存在
            if (StringUtils.equalsIgnoreCase(hrUser.getWorkStatus(), "DIMISSION")) {
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工  " + hrUser.getUserName() + " 已经离职" : "The fixed approver " + hrUser.getUserName() + " is Offboard");
                errorUserDTO.setUserHrMessage(userHrMessage);
                errorUserDTO.setTemp(1);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
                continue;
            }
            if (StringUtils.equalsIgnoreCase(hrUser.getStatus(), "DISABLED")) {
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工 " + hrUser.getUserName() + " 已经冻结" : "The fixed approver " + hrUser.getUserName() + " is Disabled");
                errorUserDTO.setUserHrMessage(userHrMessage);
                errorUserDTO.setTemp(1);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
            }
        }

        //所有角色对应人员
        List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = approvalCreateUserDTO.getApprovalCreateRoleUserDTOS();
        if (CollectionUtils.isEmpty(approvalCreateRoleUserDTOS)) {
            approvalCreateRoleUserDTOS = new ArrayList<>();
        }
        //申请人HR相关信息
        //获取HR信息
        List<UserInfoDetailDTO> applyUserInfoDetailApiDTOList = userCodeMap.get(applyUserCode);
        if (CollectionUtils.isEmpty(applyUserInfoDetailApiDTOList)) {
            return;
        }

        for (ApprovalCreateRoleUserDTO roleUserDTO : approvalCreateRoleUserDTOS) {
            List<ApprovalUserInfoDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(roleUserInfos)) {
                //这个角色没有找到人
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                errorUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                errorUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                errorUserDTO.setTemp(2);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    String userMessageEn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescUS() + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);
                    String hrUserCode = countryMainPocMap.get(applyUserInfoDetailApiDTOList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<OrgDeptApiDTO> deptApiDTOList = bizOrganizationInfoApiClient.getDeptByIds(Arrays.asList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + " - " + approvalRoleEnum.getDescCN() + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                continue;
            }

            for (ApprovalUserInfoDTO userInfoDTO : roleUserInfos) {
                List<UserInfoDetailDTO> userInfoDetailApiDTOList = userCodeMap.get(userInfoDTO.getUserCode());
                if (CollectionUtils.isEmpty(userInfoDetailApiDTOList)) {
                    continue;
                }
                //这个角色找到人
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                successUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                successUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                successUserDTO.setUserCode(userInfoDetailApiDTOList.get(0).getUserCode());
                successUserDTO.setTemp(2);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserInfoDetailApiDTOList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<OrgDeptApiDTO> deptApiDTOList = bizOrganizationInfoApiClient.getDeptByIds(Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);

                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                }
            }
        }
    }
}

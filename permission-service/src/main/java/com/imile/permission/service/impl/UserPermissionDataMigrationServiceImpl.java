package com.imile.permission.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.converter.CasbinToUserConvert;
import com.imile.permission.dao.DirectAuthorizationApprovalCollectDAO;
import com.imile.permission.dao.RefactoringPermissionCasbinRuleDAO;
import com.imile.permission.dao.RoleAuthorityApprovalCollectDAO;
import com.imile.permission.domain.applicationApprove.vo.UserDirectAuthCollectVO;
import com.imile.permission.domain.applicationApprove.vo.UserRoleCollectVO;
import com.imile.permission.domain.casbin.CasbinLikeQuery;
import com.imile.permission.domain.entity.DirectAuthorizationApprovalCollectDO;
import com.imile.permission.domain.entity.RefactoringPermissionCasbinRuleDO;
import com.imile.permission.domain.entity.RoleAuthorityApprovalCollectDO;
import com.imile.permission.domain.user.param.UserMigrationParam;
import com.imile.permission.manage.AuthorityApprovalManage;
import com.imile.permission.service.UserPermissionDataMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Service
@Slf4j
public class UserPermissionDataMigrationServiceImpl implements UserPermissionDataMigrationService {

    @Resource
    private RefactoringPermissionCasbinRuleDAO casbinRuleDAO;
    @Resource
    private AuthorityApprovalManage authorityApprovalManage;
    @Resource
    private RoleAuthorityApprovalCollectDAO roleAuthorityApprovalCollectDAO;
    @Resource
    private DirectAuthorizationApprovalCollectDAO directAuthorizationApprovalCollectDAO;

    @Override
    public void userRoleDataMigration(UserMigrationParam param) {
        // 1. 查casbin中的 用户角色关系数据
        CasbinLikeQuery query = new CasbinLikeQuery();
        query.setPtype(BusinessConstant.CASBIN_PTYPE_G);
        // U:20001
        query.setVoUserCode(param.getVoUserCode());
        query.setV1LikePrefix(BusinessConstant.CASBIN_R);
        query.setV0LikePrefix(BusinessConstant.CASBIN_U);
        int pageSize = param.getShowCount();
        int pageNum = 1;
        int total = 0;
        do {
            Page<RefactoringPermissionCasbinRuleDO> page = PageHelper.startPage(pageNum, pageSize);
            List<RefactoringPermissionCasbinRuleDO> result = casbinRuleDAO.queryCasbinLike(query);

            total = (int) page.getTotal();
            if (CollectionUtils.isEmpty(result)) {
                break;
            }
            // 转换并累积数据
            List<RoleAuthorityApprovalCollectDO> batchData = CasbinToUserConvert.convertUserRoleData(result);
            // 批量处理
            batchData.forEach(data -> authorityApprovalManage.userRoleUpdateOrSave(data));
            pageNum++;
            log.info("UserPermissionDataMigrationServiceImpl | userRoleDataMigration | " +
                    "总条数：{}，当前页：{}，每页展示条数：{}", total, pageNum, pageSize);
        } while ((pageNum - 1) * pageSize < total);
    }

    @Override
    public void userAuthDataMigration(UserMigrationParam param) {
        CasbinLikeQuery query = new CasbinLikeQuery();
        query.setPtype(BusinessConstant.CASBIN_PTYPE_P);
        // U:20001
        query.setVoUserCode(param.getVoUserCode());
        // 模糊查询
        query.setV0LikePrefix(BusinessConstant.CASBIN_U);
        int pageSize = param.getShowCount();
        int pageNum = 1;
        int total = 0;
        do {
            Page<RefactoringPermissionCasbinRuleDO> page = PageHelper.startPage(pageNum, pageSize);
            List<RefactoringPermissionCasbinRuleDO> casbinRuleDOList = casbinRuleDAO.queryCasbinLike(query);

            if (CollectionUtils.isEmpty(casbinRuleDOList)) {
                break;
            }
            total = (int) page.getTotal();
            // 2. 将用户关系角色数据进行转换
            List<DirectAuthorizationApprovalCollectDO> directAuthorizationApprovalCollectDOList = CasbinToUserConvert.convertUserAuthData(casbinRuleDOList);
            // 3. upsert到 direct_authorization_approval_collect 表中
            directAuthorizationApprovalCollectDOList.forEach(collectDO -> authorityApprovalManage.directUserUpdateOrSave(collectDO));
            pageNum++;
            log.info("UserPermissionDataMigrationServiceImpl | userRoleDataMigration | " +
                    "总条数：{}，当前页：{}，每页展示条数：{}", total, pageNum, pageSize);
        } while ((pageNum - 1) * pageSize < total);
    }

    @Override
    public void fullUserRoleDataMigration() {
        // 1. 查casbin中的 用户角色关系数据
        CasbinLikeQuery query = new CasbinLikeQuery();
        query.setPtype(BusinessConstant.CASBIN_PTYPE_G);
        // U:20001
        query.setV1LikePrefix(BusinessConstant.CASBIN_R);
        query.setV0LikePrefix(BusinessConstant.CASBIN_U);
        int pageSize = 1000;
        int pageNum = 1;
        int total = 0;
        log.info("UserPermissionDataMigrationServiceImpl | fullUserRoleDataMigration | 开始全量用户角色数据迁移");
        do {
            Page<RefactoringPermissionCasbinRuleDO> page = PageHelper.startPage(pageNum, pageSize);
            List<RefactoringPermissionCasbinRuleDO> result = casbinRuleDAO.queryCasbinLike(query);

            total = (int) page.getTotal();
            if (CollectionUtils.isEmpty(result)) {
                break;
            }
            // 转换并累积数据
            List<RoleAuthorityApprovalCollectDO> batchData = CasbinToUserConvert.convertUserRoleData(result);
            // 批量处理
            authorityApprovalManage.saveBatchRoleAuthorityApprovalCollect(batchData);
            pageNum++;

            log.info("UserPermissionDataMigrationServiceImpl | userRoleDataMigration | " +
                    "总条数：{}，当前页：{}，每页展示条数：{}", total, pageNum, pageSize);
        } while ((pageNum - 1) * pageSize < total);
        log.info("UserPermissionDataMigrationServiceImpl | fullUserRoleDataMigration | 结束全量用户角色数据迁移");

    }

    @Override
    public void fullUserAuthDataMigration() {
        CasbinLikeQuery query = new CasbinLikeQuery();
        query.setPtype(BusinessConstant.CASBIN_PTYPE_P);
        // 模糊查询
        query.setV0LikePrefix(BusinessConstant.CASBIN_U);
        int pageSize = 1000;
        int pageNum = 1;
        int total = 0;
        log.info("UserPermissionDataMigrationServiceImpl | fullUserAuthDataMigration | 开始全量用户授权数据迁移");
        do {
            Page<RefactoringPermissionCasbinRuleDO> page = PageHelper.startPage(pageNum, pageSize);
            List<RefactoringPermissionCasbinRuleDO> casbinRuleDOList = casbinRuleDAO.queryCasbinLike(query);

            if (CollectionUtils.isEmpty(casbinRuleDOList)) {
                break;
            }
            total = (int) page.getTotal();
            // 2. 将用户关系角色数据进行转换
            List<DirectAuthorizationApprovalCollectDO> directAuthorizationApprovalCollectDOList = CasbinToUserConvert.convertUserAuthData(casbinRuleDOList);

            authorityApprovalManage.saveDirectAuthorizationApprovalCollect(directAuthorizationApprovalCollectDOList);
            pageNum++;

            log.info("UserPermissionDataMigrationServiceImpl | userRoleDataMigration | " +
                    "总条数：{}，当前页：{}，每页展示条数：{}", total, pageNum, pageSize);
        } while ((pageNum - 1) * pageSize < total);
        log.info("UserPermissionDataMigrationServiceImpl | fullUserAuthDataMigration | 结束全量用户授权数据迁移");

    }

    @Override
    public void userRoleAndDirectDeduplication(Integer code) {
        // 获取userRole的去重数据
        List<UserRoleCollectVO> userRoleCollectList = authorityApprovalManage.getUserRoleCollectList();
        // 获取userDirectAuth的去重数据
        List<UserDirectAuthCollectVO> userDirectAuthList = authorityApprovalManage.getUserDirectAuthList();

        // 处理userRole的去重数据
        doUserRoleDeduplication(userRoleCollectList);
        // 处理userDirectAuth的去重数据
        doUserDirectAuthDeduplication(userDirectAuthList);
    }

    private void doUserDirectAuthDeduplication(List<UserDirectAuthCollectVO> userDirectAuthList) {
        if (CollectionUtils.isEmpty(userDirectAuthList)) {
            return;
        }
        // 每100个分组操作
        Lists.partition(userDirectAuthList, 100).forEach(userDirectAuthSubList -> {
            Map<String, List<Long>> deleteIdMap = Maps.newHashMap();
            userDirectAuthSubList.forEach(userDirectAuth -> {
                // 数量大于1的数据，需要去重；
                if (userDirectAuth.getDuplicateCount() > 1) {
                    deleteIdMap.put(String.join("_", userDirectAuth.getUserCode(), userDirectAuth.getSourceType(),
                            userDirectAuth.getSourceId()), getDeleteIds(userDirectAuth.getDuplicateIds()));
                }
            });

            // 批量删除重复数据
            log.info("UserPermissionDataMigrationServiceImpl | doUserDirectAuthDeduplication | 批量删除重复数据：{}", deleteIdMap);

            // 将deleteIdMap中的value值累计到一个list中
            List<Long> deleteIds = deleteIdMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
            // 批量删除
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                directAuthorizationApprovalCollectDAO.batchDelete(deleteIds);
            }
        });
    }

    private List<Long> getDeleteIds(String duplicationIds) {
        String[] split = duplicationIds.split(",");
        // 保留数组中的第一个id
        if (split.length > 1) {
            // 获取第一个元素之后的所有元素
            String[] restOfTheArray = Arrays.copyOfRange(split, 1, split.length);
            // 现在restOfTheArray包含原数组中除第一个元素以外的所有元素
            return Arrays.asList(restOfTheArray).stream().map(Long::parseLong).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private void doUserRoleDeduplication(List<UserRoleCollectVO> userRoleCollectList) {
        if (CollectionUtils.isEmpty(userRoleCollectList)) {
            return;
        }
        // 每100个分组操作
        Lists.partition(userRoleCollectList, 100).forEach(userDirectAuthSubList -> {
            Map<String, List<Long>> deleteIdMap = Maps.newHashMap();
            userDirectAuthSubList.forEach(userDirectAuth -> {
                // 数量大于1的数据，需要去重；
                if (userDirectAuth.getDuplicateCount() > 1) {
                    deleteIdMap.put(String.join("_", userDirectAuth.getUserCode(),
                                    userDirectAuth.getRoleId().toString()),
                            getDeleteIds(userDirectAuth.getDuplicateIds()));
                }
            });

            // 批量删除重复数据
            log.info("UserPermissionDataMigrationServiceImpl | doUserRoleDeduplication | 批量删除重复数据：{}", deleteIdMap);

            // 将deleteIdMap中的value值累计到一个list中
            List<Long> deleteIds = deleteIdMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
            // 批量删除
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                roleAuthorityApprovalCollectDAO.batchDelete(deleteIds);
            }
        });
    }
}

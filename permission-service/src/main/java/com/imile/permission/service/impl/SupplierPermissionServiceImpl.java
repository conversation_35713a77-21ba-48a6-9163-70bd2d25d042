package com.imile.permission.service.impl;

import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.lm.express.base.api.station.dto.StationResultApiDTO;
import com.imile.permission.context.RequestInfoHolder;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.role.param.RoleIdPageQueryParam;
import com.imile.permission.domain.supplier.AddCspSupplierAdminRoleParam;
import com.imile.permission.domain.supplier.AddDriverRoleParam;
import com.imile.permission.domain.supplier.AddOutletRoleParam;
import com.imile.permission.domain.supplier.AddSupplierRoleParam;
import com.imile.permission.domain.supplier.CspSupplierAdminRoleBasicUpdateParam;
import com.imile.permission.domain.supplier.CspSupplierAdminRoleDetailVO;
import com.imile.permission.domain.supplier.CspSupplierAdminRoleQuery;
import com.imile.permission.domain.supplier.CspSupplierAdminRoleVO;
import com.imile.permission.domain.supplier.DisableOutletRoleParam;
import com.imile.permission.domain.supplier.DriverRoleDetailVO;
import com.imile.permission.domain.supplier.DriverRoleQuery;
import com.imile.permission.domain.supplier.DriverRoleVO;
import com.imile.permission.domain.supplier.OutletRoleDetailVO;
import com.imile.permission.domain.supplier.OutletRoleQuery;
import com.imile.permission.domain.supplier.OutletRoleVO;
import com.imile.permission.domain.supplier.OutletVO;
import com.imile.permission.domain.supplier.SupplierAccountQuery;
import com.imile.permission.domain.supplier.SupplierAccountVO;
import com.imile.permission.domain.supplier.SupplierRoleBindingAccountVO;
import com.imile.permission.domain.supplier.SupplierRoleDetailVO;
import com.imile.permission.domain.supplier.SupplierRoleQuery;
import com.imile.permission.domain.supplier.SupplierRoleVO;
import com.imile.permission.domain.supplier.SupplierSubAccountAuthorizeParam;
import com.imile.permission.domain.supplier.UpdateCspSupplierAdminRoleParam;
import com.imile.permission.domain.supplier.UpdateDriverRoleParam;
import com.imile.permission.domain.supplier.UpdateOutletRoleParam;
import com.imile.permission.domain.supplier.UpdateSupplierRoleParam;
import com.imile.permission.domain.supplier.CspSupplierAdminRoleBasicParam;
import com.imile.permission.domain.supplier.DriverRoleBasicParam;
import com.imile.permission.domain.supplier.OutletRoleBasicParam;
import com.imile.permission.domain.supplier.SupplierRoleBasicParam;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.enums.RoleTypeEnum;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.integration.dict.DictIntegration;
import com.imile.permission.integration.lmExpressBase.StationApiIntegration;
import com.imile.permission.manage.PermissionEventLogManage;
import com.imile.permission.manage.SupplierPermissionRoleManage;
import com.imile.permission.service.SupplierPermissionService;
import com.imile.resource.api.dto.ResSystemResourceTreeApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Slf4j
@Service
public class SupplierPermissionServiceImpl implements SupplierPermissionService {

    @Autowired
    private StationApiIntegration stationApiIntegration;

    @Autowired
    private SupplierPermissionRoleManage supplierPermissionRoleManage;

    @Autowired
    private PermissionEventLogManage permissionEventLogManage;

    @Autowired
    private DictIntegration dictIntegration;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addCspSupplierAdminRole(AddCspSupplierAdminRoleParam addCspSupplierAdminRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(addCspSupplierAdminRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        CspSupplierAdminRoleBasicParam roleBasicParam = addCspSupplierAdminRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(addCspSupplierAdminRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);

        // 先实例化内部对象的属性
        List<String> systemCodeList = roleBasicParam.getSystemCodeList();
        Integer authScene = roleBasicParam.getAuthScene();
        Integer roleType = roleBasicParam.getRoleType();
        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();
        List<String> roleCountryList = roleBasicParam.getRoleCountryList();
        String adminOrg = roleBasicParam.getAdminOrg();
        String description = roleBasicParam.getDescription();
        String descriptionEn = roleBasicParam.getDescriptionEn();
        Integer isAccessApprovalFlow = roleBasicParam.getIsAccessApprovalFlow();

        // 对内部对象属性进行判空
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(systemCodeList), PermissionErrorCodeEnums.SYSTEM_CODE_LIST_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(authScene), PermissionErrorCodeEnums.AUTH_SCENE_IS_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleType), PermissionErrorCodeEnums.ROLE_TYPE_IS_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(roleCountryList), PermissionErrorCodeEnums.ROLE_COUNTRY_LIST_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(adminOrg), PermissionErrorCodeEnums.ADMIN_ORG_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(description), PermissionErrorCodeEnums.DESCRIPTION_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(descriptionEn), PermissionErrorCodeEnums.DESCRIPTION_EN_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(isAccessApprovalFlow), PermissionErrorCodeEnums.IS_ACCESS_APPROVAL_FLOW_NOT_NULL);

        // 调用 supplierPermissionRoleManage 方法，实现 系统+角色适用国家+机构类型 唯一校验
        List<SysRoleDO> roleList = supplierPermissionRoleManage.getCSPSupplierAdminRole(systemCodeList, roleCountryList, adminOrg);
        if (CollectionUtils.isNotEmpty(roleList)) {
            BusinessLogicException.checkTrue(true, PermissionErrorCodeEnums.CSP_ADMIN_ROLE_EXIST, roleCountryList.get(0), adminOrg, roleList.get(0).getRoleName());
        }
        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(roleName, roleNameEn);

        // 调用 supplierPermissionRoleManage 方法，实现 角色新增
        Long id = supplierPermissionRoleManage.addCspSupplierAdminRole(roleBasicParam, menuPermissionDTOList);

        // 调用 permissionEventLogManage 记录角色新增日志，包括角色ID、菜单权限、请求的参数的json对象
        permissionEventLogManage.addRoleMenuPermissionLog(id, menuPermissionDTOList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCspSupplierAdminRole(UpdateCspSupplierAdminRoleParam updateCspSupplierAdminRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(updateCspSupplierAdminRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        Long id = updateCspSupplierAdminRoleParam.getId();
        CspSupplierAdminRoleBasicUpdateParam roleBasicParam = updateCspSupplierAdminRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(updateCspSupplierAdminRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);

        // 先实例化内部对象的属性
        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();
        String description = roleBasicParam.getDescription();
        String descriptionEn = roleBasicParam.getDescriptionEn();

        // 对内部对象属性进行判空
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(description), PermissionErrorCodeEnums.DESCRIPTION_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(descriptionEn), PermissionErrorCodeEnums.DESCRIPTION_EN_NOT_NULL);


        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(id, roleName, roleNameEn);

        supplierPermissionRoleManage.updateCspSupplierAdminRole(id, roleBasicParam, menuPermissionDTOList);

    }

    @Override
    public CspSupplierAdminRoleDetailVO getCspSupplierAdminRole(Long id) {
        return supplierPermissionRoleManage.getCspSupplierAdminRole(id);
    }

    @Override
    public PaginationResult<CspSupplierAdminRoleVO> listCspSupplierAdminRole(CspSupplierAdminRoleQuery cspSupplierAdminRoleQuery) {
        return supplierPermissionRoleManage.listCspSupplierAdminRole(cspSupplierAdminRoleQuery);
    }

    @Override
    public void addDriverRole(AddDriverRoleParam addDriverRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(addDriverRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        DriverRoleBasicParam roleBasicParam = addDriverRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(addDriverRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);

        // 先实例化内部对象的属性
        List<String> systemCodeList = roleBasicParam.getSystemCodeList();
        Integer authScene = roleBasicParam.getAuthScene();
        Integer roleType = roleBasicParam.getRoleType();
        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();
        List<String> roleCountryList = roleBasicParam.getRoleCountryList();
        String functional = roleBasicParam.getFunctional();
        String description = roleBasicParam.getDescription();
        String descriptionEn = roleBasicParam.getDescriptionEn();
        Integer isAccessApprovalFlow = roleBasicParam.getIsAccessApprovalFlow();

        // 对内部对象属性进行判空
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(systemCodeList), PermissionErrorCodeEnums.SYSTEM_CODE_LIST_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(authScene), PermissionErrorCodeEnums.AUTH_SCENE_IS_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleType), PermissionErrorCodeEnums.ROLE_TYPE_IS_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(roleCountryList), PermissionErrorCodeEnums.ROLE_COUNTRY_LIST_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(functional), PermissionErrorCodeEnums.FUNCTIONAL_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(description), PermissionErrorCodeEnums.DESCRIPTION_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(descriptionEn), PermissionErrorCodeEnums.DESCRIPTION_EN_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(isAccessApprovalFlow), PermissionErrorCodeEnums.IS_ACCESS_APPROVAL_FLOW_NOT_NULL);

        // 调用 supplierPermissionRoleManage 方法，实现 系统+角色适用国家+机构类型 唯一校验
        BusinessLogicException.checkTrue(supplierPermissionRoleManage.checkDriverUnique(systemCodeList, roleCountryList, functional), PermissionErrorCodeEnums.ROLE_EXIST);
        List<SysRoleDO> roleList = supplierPermissionRoleManage.getDriverRole(systemCodeList, roleCountryList, functional);
        if (CollectionUtils.isNotEmpty(roleList)) {
            BusinessLogicException.checkTrue(true, PermissionErrorCodeEnums.DRIVER_ROLE_EXIST, roleCountryList.get(0), functional, roleList.get(0).getRoleName());
        }

        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(roleName, roleNameEn);

        // 调用 supplierPermissionRoleManage 方法，实现 角色新增
        Long id = supplierPermissionRoleManage.addDriverRole(roleBasicParam, menuPermissionDTOList);

        // 调用 permissionEventLogManage 记录角色新增日志，包括角色ID、菜单权限、请求的参数的json对象
        permissionEventLogManage.addRoleMenuPermissionLog(id, menuPermissionDTOList);


    }

    @Override
    public void updateDriverRole(UpdateDriverRoleParam updateDriverRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(updateDriverRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        Long id = updateDriverRoleParam.getId();
        DriverRoleBasicParam roleBasicParam = updateDriverRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(updateDriverRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);

        // 先实例化内部对象的属性
        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();
        String description = roleBasicParam.getDescription();
        String descriptionEn = roleBasicParam.getDescriptionEn();

        // 对内部对象属性进行判空
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(description), PermissionErrorCodeEnums.DESCRIPTION_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(descriptionEn), PermissionErrorCodeEnums.DESCRIPTION_EN_NOT_NULL);

        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(id, roleName, roleNameEn);

        supplierPermissionRoleManage.updateDriverRole(id, roleBasicParam, menuPermissionDTOList);
    }

    @Override
    public DriverRoleDetailVO getDriverRole(Long id) {
        return supplierPermissionRoleManage.getDriverRole(id);
    }

    @Override
    public PaginationResult<DriverRoleVO> listDriverRole(DriverRoleQuery driverRoleQuery) {
        return supplierPermissionRoleManage.listDriverRole(driverRoleQuery);
    }

    @Override
    public List<ResSystemResourceTreeApiDTO> cspSupplierAdminMenuTree(String lang, String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        return supplierPermissionRoleManage.cspSupplierAdminMenuTree(lang, userCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addOutletRole(AddOutletRoleParam addOutletRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(addOutletRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        OutletRoleBasicParam roleBasicParam = addOutletRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(addOutletRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);

        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);

        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(roleName, roleNameEn);
        // 调用 supplierPermissionRoleManage 方法，实现 角色新增
        Long id = supplierPermissionRoleManage.addOutletRole(roleBasicParam, menuPermissionDTOList);

        // 调用 permissionEventLogManage 记录角色新增日志，包括角色ID、菜单权限、请求的参数的json对象
        permissionEventLogManage.addRoleMenuPermissionLog(id, menuPermissionDTOList);

    }

    @Override
    public void updateOutletRole(UpdateOutletRoleParam updateOutletRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(updateOutletRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        Long id = updateOutletRoleParam.getId();
        OutletRoleBasicParam roleBasicParam = updateOutletRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(updateOutletRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);


        // 先实例化内部对象的属性
        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();
        String description = roleBasicParam.getDescription();
        String descriptionEn = roleBasicParam.getDescriptionEn();

        // 对内部对象属性进行判空
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(description), PermissionErrorCodeEnums.DESCRIPTION_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(descriptionEn), PermissionErrorCodeEnums.DESCRIPTION_EN_NOT_NULL);


        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(id, roleName, roleNameEn);

        supplierPermissionRoleManage.updateOutletRole(id, roleBasicParam, menuPermissionDTOList);


    }

    @Override
    public OutletRoleDetailVO getOutletRole(Long id) {
        return supplierPermissionRoleManage.getOutletRole(id);
    }

    @Override
    public PaginationResult<OutletRoleVO> listOutletRole(OutletRoleQuery outletRoleQuery) {
        return supplierPermissionRoleManage.listOutletRole(outletRoleQuery);
    }

    @Override
    public void disableOutletRole(DisableOutletRoleParam disableOutletRoleParam) {
        supplierPermissionRoleManage.disableOutletRole(disableOutletRoleParam);
    }

    @Override
    public void addSupplierRole(AddSupplierRoleParam addSupplierRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(addSupplierRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        SupplierRoleBasicParam roleBasicParam = addSupplierRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(addSupplierRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);

        // 先实例化内部对象的属性
        List<String> systemCodeList = roleBasicParam.getSystemCodeList();
        Integer authScene = roleBasicParam.getAuthScene();
        Integer roleType = roleBasicParam.getRoleType();
        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();
        List<String> roleCountryList = roleBasicParam.getRoleCountryList();
        String supplierType = roleBasicParam.getSupplierType();
        Integer isAccessApprovalFlow = roleBasicParam.getIsAccessApprovalFlow();

        // 对内部对象属性进行判空
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(systemCodeList), PermissionErrorCodeEnums.SYSTEM_CODE_LIST_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(authScene), PermissionErrorCodeEnums.AUTH_SCENE_IS_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleType), PermissionErrorCodeEnums.ROLE_TYPE_IS_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(roleCountryList), PermissionErrorCodeEnums.ROLE_COUNTRY_LIST_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(supplierType), PermissionErrorCodeEnums.SUPPLIER_TYPE_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(isAccessApprovalFlow), PermissionErrorCodeEnums.IS_ACCESS_APPROVAL_FLOW_NOT_NULL);


        if (roleType.equals(RoleTypeEnum.SUPPLIER.getCode())) {
            // 系统+供应商类型角色+供应商类型唯一
            List<SysRoleDO> roleList = supplierPermissionRoleManage.getSupplierRole(systemCodeList, supplierType);
            if (CollectionUtils.isNotEmpty(roleList)) {
                String codeDataValue = dictIntegration.getByTypeCodeDataValue("supplierType", supplierType);
                String userName = null;
                SysRoleDO roleDO = roleList.get(0);
                if (RequestInfoHolder.isChinese()) {
                    userName = roleDO.getRoleName();
                } else {
                    userName = roleDO.getRoleNameEn();
                }
                BusinessLogicException.checkTrue(true, PermissionErrorCodeEnums.SUPPLIER_ROLE_EXIST, codeDataValue, supplierType, userName);
            }

        } else if (roleType.equals(RoleTypeEnum.COUNTRY_SUPPLIER.getCode())) {
            // 系统+国家供应商类型角色+国家+供应商类型唯一
            List<SysRoleDO> roleList = supplierPermissionRoleManage.getCountrySupplierRole(systemCodeList, roleCountryList, supplierType);
            if (CollectionUtils.isNotEmpty(roleList)) {
                String codeDataValue = dictIntegration.getByTypeCodeDataValue("supplierType", supplierType);
                String userName = null;
                SysRoleDO roleDO = roleList.get(0);
                if (RequestInfoHolder.isChinese()) {
                    userName = roleDO.getRoleName();
                } else {
                    userName = roleDO.getRoleNameEn();
                }
                BusinessLogicException.checkTrue(true, PermissionErrorCodeEnums.COUNTRY_SUPPLIER_ROLE_EXIST, roleCountryList.get(0), codeDataValue, supplierType, userName);
            }
        } else {
            BusinessLogicException.checkTrue(true, PermissionErrorCodeEnums.ROLE_TYPE_NOT_EXIST);
        }

        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(roleName, roleNameEn);

        // 调用 supplierPermissionRoleManage 方法，实现 角色新增
        Long id = supplierPermissionRoleManage.addSupplierRole(roleBasicParam, menuPermissionDTOList);

        // 调用 permissionEventLogManage 记录角色新增日志，包括角色ID、菜单权限、请求的参数的json对象
        permissionEventLogManage.addRoleMenuPermissionLog(id, menuPermissionDTOList);
    }

    @Override
    public String updateSupplierRole(UpdateSupplierRoleParam updateSupplierRoleParam) {
        BusinessLogicException.checkTrue(Objects.isNull(updateSupplierRoleParam), PermissionErrorCodeEnums.PARAM_NOT_NULL);

        // 先实例化内部对象
        Long id = updateSupplierRoleParam.getId();
        SupplierRoleBasicParam roleBasicParam = updateSupplierRoleParam.getRoleBasicParam();
        List<MenuPermissionDTO> menuPermissionDTOList = Optional.ofNullable(updateSupplierRoleParam.getMenuPermissionDTOList()).orElse(Lists.newArrayList());

        // 对内部对象进行判空
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleBasicParam), PermissionErrorCodeEnums.ROLE_BASIC_PARAM_NOT_NULL);

        // 先实例化内部对象的属性
        String roleName = roleBasicParam.getRoleName();
        String roleNameEn = roleBasicParam.getRoleNameEn();

        // 对内部对象属性进行判空
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLENAMECN_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLENAMEEN_NOT_NULL);


        // 调用 supplierPermissionRoleManage 方法，实现 名称重复校验
        supplierPermissionRoleManage.checkRoleNameAndRoleNameEnUnique(id, roleName, roleNameEn);

        return supplierPermissionRoleManage.updateSupplierRole(id, roleBasicParam, menuPermissionDTOList);
    }

    @Override
    public SupplierRoleDetailVO getSupplierRole(Long id) {
        return supplierPermissionRoleManage.getSupplierRole(id);
    }

    @Override
    public PaginationResult<SupplierRoleVO> listSupplierRole(SupplierRoleQuery supplierRoleQuery) {
        return supplierPermissionRoleManage.listSupplierRole(supplierRoleQuery);
    }

    @Override
    public PaginationResult<SupplierAccountVO> listSupplierAccount(SupplierAccountQuery supplierAccountQuery) {
        return supplierPermissionRoleManage.listSupplierAccount(supplierAccountQuery);
    }

    @Override
    public List<ResSystemResourceTreeApiDTO> mainAccountMenuTree(String lang, String primaryAccountCode) {
        return supplierPermissionRoleManage.mainAccountMenuTree(lang, primaryAccountCode);
    }

    @Override
    public void accountAuthorize(SupplierSubAccountAuthorizeParam supplierSubAccountAuthorizeParam) {
        supplierPermissionRoleManage.accountAuthorize(supplierSubAccountAuthorizeParam);
    }

    @Override
    public List<MenuPermissionDTO> accountPermission(String accountCode) {
        return supplierPermissionRoleManage.accountPermission(accountCode);
    }

    @Override
    public List<OutletVO> outlet(String country) {
        if (StringUtils.isBlank(country)) {
            return Lists.newArrayList();
        }
        List<StationResultApiDTO> stationResultApiDTOS = stationApiIntegration.listStationByCountry(country);
        return stationResultApiDTOS.stream()
                .map(
                        e -> {
                            OutletVO outletVO = new OutletVO();
                            outletVO.setCode(e.getStationCode());
                            outletVO.setName(e.getStationName());
                            return outletVO;
                        }
                ).collect(Collectors.toList());
    }

    @Override
    public Boolean cspSupplierAdminCheck(String userCode) {
        return supplierPermissionRoleManage.cspSupplierAdminCheck(userCode);
    }

    @Override
    public List<String> cspSupplierAdminCountry(String userCode) {
        return supplierPermissionRoleManage.cspSupplierAdminCountry(userCode);
    }

    @Override
    public PaginationResult<SupplierRoleBindingAccountVO> getSupplierRoleBindingAccount(RoleIdPageQueryParam rolePageQueryParam) {
        return supplierPermissionRoleManage.getSupplierRoleBindingAccount(rolePageQueryParam);
    }
}

package com.imile.permission.service.impl;

import com.google.common.collect.Lists;
import com.imile.permission.dao.RefactoringPermissionCasbinRuleDAO;
import com.imile.permission.domain.common.IdListParam;
import com.imile.permission.domain.dataProcessing.DuplicationDTO;
import com.imile.permission.domain.dataProcessing.DuplicationIdVO;
import com.imile.permission.domain.dataProcessing.DuplicationParam;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.service.CasBinDataProcessingService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/5/14
 */
@Service
public class CasBinDataProcessingServiceImpl implements CasBinDataProcessingService {
    @Autowired
    RefactoringPermissionCasbinRuleDAO refactoringPermissionCasbinRuleDAO;

    @Override
    public void deleteById(IdListParam idListParam) {
        List<String> idList = idListParam.getIdList();
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(idList), PermissionErrorCodeEnums.ID_NOT_NULL);
        BusinessLogicException.checkTrue(idList.size() > 10000, PermissionErrorCodeEnums.ID_TOO_LONG);
        refactoringPermissionCasbinRuleDAO.removeByIds(idList);
    }

    @Override
    public DuplicationIdVO getDuplication(DuplicationParam duplicationParam) {
        Integer limitSize = Optional.ofNullable(duplicationParam.getLimitSize()).orElse(10000);
        BusinessLogicException.checkTrue(limitSize > 10000, PermissionErrorCodeEnums.LIMIT_SIZE_TOO_LONG);
        List<DuplicationDTO> duplicationDTO = refactoringPermissionCasbinRuleDAO.getDuplication(limitSize);
        DuplicationIdVO duplicationIdVO = new DuplicationIdVO();
        List<Long> allList = new ArrayList<>();
        List<Long> retainList = new ArrayList<>();
        List<Long> deleteList = new ArrayList<>();

        for (DuplicationDTO dto : duplicationDTO) {
            String idListStr = dto.getIdListStr();
            String[] split = idListStr.split(",");
            for (int i = 0; i < split.length; i++) {
                allList.add(Long.parseLong(split[i]));
                if (i == 0) {
                    retainList.add(Long.parseLong(split[i]));
                } else {
                    deleteList.add(Long.parseLong(split[i]));
                }
            }
        }

        duplicationIdVO.setAllList(allList);
        duplicationIdVO.setRetainList(retainList);
        duplicationIdVO.setDeleteList(deleteList);
        return duplicationIdVO;
    }

    @Override
    public DuplicationIdVO deleteDuplication(DuplicationParam duplicationParam) {
        DuplicationIdVO duplication = getDuplication(duplicationParam);
        List<Long> delete = Optional.ofNullable(duplication.getDeleteList()).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(delete)) {
            refactoringPermissionCasbinRuleDAO.removeByIds(delete);
        }
        return duplication;

    }
}

package com.imile.permission.service.rpc;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.imile.permission.api.DataApi;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.PermissionApiDTO;
import com.imile.permission.domain.entity.RuleDynamicDataPermissionDO;
import com.imile.permission.domain.mq.PermissionDynamicDataOperationDTO;
import com.imile.permission.enums.DynamicDataDataTypeEnum;
import com.imile.permission.enums.DynamicDataOperationTypeEnum;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.manage.DataPermissionManage;
import com.imile.permission.manage.EsWithCacheManage;
import com.imile.permission.service.PermissionCacheService;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */
@Slf4j
@Service(version = "1.0.0")
public class DataApiService implements DataApi {

    @Resource
    private EsWithCacheManage esWithCacheManage;

    @Resource
    private DataPermissionManage dataPermissionManage;

    @Resource
    private PermissionCacheService permissionCacheService;


    @Override
    public RpcResult<Map<String, List<String>>> getByUserCode(String systemCode, String userCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(systemCode), PermissionErrorCodeEnums.SYSTEM_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        PermissionApiDTO userAuthorizationAndDB = esWithCacheManage.getUserAuthorizationAndDB(userCode);
        List<DataPermissionApiDTO> dataPermissionApiDTOS = Optional.ofNullable(userAuthorizationAndDB.getDataPermissionDTOList())
                .orElse(Collections.emptyList());
        List<String> typeCode = dataPermissionApiDTOS.stream()
                .map(DataPermissionApiDTO::getTypeCode)
                .map(e -> e.replace("$", ""))
                .collect(Collectors.toList());
        List<String> filterTypeCode = dataPermissionManage.getSystemTypeCode(systemCode, typeCode);
        List<RuleDynamicDataPermissionDO> dynamicList = dataPermissionManage.listDynamicDataByUserAndTypeCode(userCode, filterTypeCode);
        Map<String, List<RuleDynamicDataPermissionDO>> map = dynamicList.stream()
                .collect(Collectors.groupingBy(RuleDynamicDataPermissionDO::getTypeCode));
        Map<String, List<String>> result = new HashMap<>();

        dataPermissionApiDTOS.stream()
                .filter(e -> filterTypeCode.contains(e.getTypeCode().replace("$", "")))
                .forEach(
                        e -> {
                            List<RuleDynamicDataPermissionDO> ruleDynamicDataPermissionDOS = map.get(e.getTypeCode().replace("$", ""));
                            List<String> mainValueList = result.getOrDefault(e.getTypeCode().replace("$", ""), Lists.newArrayList());
                            if (CollectionUtils.isNotEmpty(ruleDynamicDataPermissionDOS)) {
                                for (RuleDynamicDataPermissionDO ruleDynamic : ruleDynamicDataPermissionDOS) {
                                    String dataContent = ruleDynamic.getDataContent();
                                    if (StringUtils.isNotBlank(dataContent)) {
                                        try {
                                            List<String> dataList = JSONArray.parseArray(dataContent, String.class);
                                            if (CollectionUtils.isNotEmpty(dataList)) {
                                                mainValueList.addAll(dataList);
                                            }
                                        } catch (Exception ex) {
                                            log.info("json parse error", ex);
                                        }
                                    }
                                }
                            }
                            if (!e.getTypeCode().startsWith("$")) {
                                List<String> dataCodeList = Optional.ofNullable(e.getDataCodeList()).orElse(Collections.emptyList());
                                mainValueList.addAll(dataCodeList);
                            }
                            mainValueList = mainValueList.stream().distinct().collect(Collectors.toList());
                            result.put(e.getTypeCode().replace("$", ""), mainValueList);
                        }
                );

        PermissionDynamicDataOperationDTO permissionDynamicDataOperationDTO = new PermissionDynamicDataOperationDTO();
        permissionDynamicDataOperationDTO.setOperationType(DynamicDataOperationTypeEnum.REFRESH.getCode());
        permissionDynamicDataOperationDTO.setSkipRedisCheck(false);
        permissionDynamicDataOperationDTO.setDataType(DynamicDataDataTypeEnum.USERCODE.getCode());
        permissionDynamicDataOperationDTO.setUserCode(Lists.newArrayList(userCode));
        permissionCacheService.dynamicDataPermissionOperation(DynamicDataOperationTypeEnum.REFRESH.getCode(), "U:" + userCode, permissionDynamicDataOperationDTO);
        return RpcResult.ok(result);
    }

    @Override
    public RpcResult<Map<String, List<String>>> getByUserCodeTypeCode(String userCode, String typeCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(StringUtils.isBlank(typeCode), PermissionErrorCodeEnums.TYPE_CODE_NOT_NULL);

        PermissionApiDTO userAuthorizationAndDB = esWithCacheManage.getUserAuthorizationAndDB(userCode);
        List<DataPermissionApiDTO> dataPermissionApiDTOS = Optional.ofNullable(userAuthorizationAndDB.getDataPermissionDTOList())
                .orElse(Collections.emptyList());

        List<RuleDynamicDataPermissionDO> dynamicList = dataPermissionManage.listDynamicDataByUserAndTypeCode(userCode, Arrays.asList(typeCode));
        Map<String, List<RuleDynamicDataPermissionDO>> map = dynamicList.stream()
                .collect(Collectors.groupingBy(RuleDynamicDataPermissionDO::getTypeCode));
        Map<String, List<String>> result = new HashMap<>();
        dataPermissionApiDTOS.stream()
                .filter(e -> typeCode.equals(e.getTypeCode().replace("$", "")))
                .forEach(
                        e -> {
                            List<RuleDynamicDataPermissionDO> ruleDynamicDataPermissionDOS = map.get(e.getTypeCode().replace("$", ""));
                            List<String> mainValueList = result.getOrDefault(e.getTypeCode().replace("$", ""), Lists.newArrayList());
                            if (CollectionUtils.isNotEmpty(ruleDynamicDataPermissionDOS)) {
                                for (RuleDynamicDataPermissionDO ruleDynamic : ruleDynamicDataPermissionDOS) {
                                    String dataContent = ruleDynamic.getDataContent();
                                    if (StringUtils.isNotBlank(dataContent)) {
                                        try {
                                            List<String> dataList = JSONArray.parseArray(dataContent, String.class);
                                            if (CollectionUtils.isNotEmpty(dataList)) {
                                                mainValueList.addAll(dataList);
                                            }
                                        } catch (Exception ex) {
                                            log.info("json parse error", ex);
                                        }
                                    }
                                }
                            }
                            if (!e.getTypeCode().startsWith("$")) {
                                List<String> dataCodeList = Optional.ofNullable(e.getDataCodeList()).orElse(Collections.emptyList());
                                mainValueList.addAll(dataCodeList);
                            }
                            mainValueList = mainValueList.stream().distinct().collect(Collectors.toList());
                            result.put(e.getTypeCode().replace("$", ""), mainValueList);
                        }
                );

        PermissionDynamicDataOperationDTO permissionDynamicDataOperationDTO = new PermissionDynamicDataOperationDTO();
        permissionDynamicDataOperationDTO.setOperationType(DynamicDataOperationTypeEnum.REFRESH.getCode());
        permissionDynamicDataOperationDTO.setSkipRedisCheck(false);
        permissionDynamicDataOperationDTO.setDataType(DynamicDataDataTypeEnum.USERCODE.getCode());
        permissionDynamicDataOperationDTO.setUserCode(Lists.newArrayList(userCode));
        permissionCacheService.dynamicDataPermissionOperation(DynamicDataOperationTypeEnum.REFRESH.getCode(), "U:" + userCode, permissionDynamicDataOperationDTO);
        return RpcResult.ok(result);
    }
}

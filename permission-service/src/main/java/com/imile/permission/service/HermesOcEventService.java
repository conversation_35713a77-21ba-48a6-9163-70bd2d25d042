package com.imile.permission.service;

import com.imile.permission.domain.common.OcInfoPayloadDTO;

/**
 * 网点变更的处理逻辑
 * <AUTHOR>
 * @since 2024/8/8
 */
public interface HermesOcEventService {

    /**
     * 删除网点的处理逻辑
     * @param ocInfoPayloadDTO
     */
    void handlerOcDelete(OcInfoPayloadDTO ocInfoPayloadDTO);

    /**
     * 新增网点处理逻辑
     * @param ocInfoPayloadDTO
     */
    void handlerOcAdd(OcInfoPayloadDTO ocInfoPayloadDTO);
}

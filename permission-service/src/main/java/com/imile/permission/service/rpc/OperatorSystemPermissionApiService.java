package com.imile.permission.service.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.permission.api.dto.MenuPermissionApiDTO;
import com.imile.permission.api.operatorSystem.OperatorSystemPermissionApi;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemRoleAddApiDTO;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemRoleDetailApiDTO;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemRoleListApiDTO;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemRoleQueryApiDTO;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemRoleUpdateApiDTO;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemRoleUserApiDTO;
import com.imile.permission.api.operatorSystem.dto.OperatorSystemUpdateRoleStatusApiDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.operatorSystem.OperatorSystemRoleListDTO;
import com.imile.permission.domain.operatorSystem.OperatorSystemRoleQueryDTO;
import com.imile.permission.domain.operatorSystem.OperatorSystemRoleUserDTO;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.manage.OperatorSystemPermissionManage;
import com.imile.permission.util.OrikaUtil;
import com.imile.permission.util.PageUtil;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/2/19
 */
@Slf4j
@Service(version = "1.0.0")
public class OperatorSystemPermissionApiService implements OperatorSystemPermissionApi {

    @Resource
    private OperatorSystemPermissionManage operatorSystemPermissionManage;

    @Override
    public RpcResult<Boolean> createRole(OperatorSystemRoleAddApiDTO role) {
        // orgId
        Long orgId = role.getOrgId();
        BusinessLogicException.checkTrue(Objects.isNull(orgId), PermissionErrorCodeEnums.ORG_ID_NOT_NULL);

// code
        String code = role.getCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(code), PermissionErrorCodeEnums.CODE_NOT_NULL);
        BusinessLogicException.checkTrue(code.getBytes(StandardCharsets.UTF_8).length > 64, PermissionErrorCodeEnums.CODE_TOO_LONG);

// roleName
        String roleName = role.getRoleName();
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLE_NAME_NOT_NULL);
        BusinessLogicException.checkTrue(roleName.getBytes(StandardCharsets.UTF_8).length > 255, PermissionErrorCodeEnums.ROLE_NAME_TOO_LONG);

// roleNameEn
        String roleNameEn = role.getRoleNameEn();
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLE_NAME_EN_NOT_NULL);
        BusinessLogicException.checkTrue(roleNameEn.getBytes(StandardCharsets.UTF_8).length > 255, PermissionErrorCodeEnums.ROLE_NAME_EN_TOO_LONG);

// isDisable
        Integer isDisable = role.getIsDisable();
        BusinessLogicException.checkTrue(Objects.isNull(isDisable), PermissionErrorCodeEnums.IS_DISABLE_NOT_NULL);

// adminOrg
        String adminOrg = role.getAdminOrg();
        BusinessLogicException.checkTrue(StringUtils.isBlank(adminOrg), PermissionErrorCodeEnums.ADMIN_ORG_NOT_NULL);

// ownStationCode
        String ownStationCode = role.getOwnStationCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(ownStationCode), PermissionErrorCodeEnums.OWN_STATION_CODE_NOT_NULL);

// description
        String description = Optional.ofNullable(role.getDescription()).orElse("");
        BusinessLogicException.checkTrue(description.getBytes(StandardCharsets.UTF_8).length > 500, PermissionErrorCodeEnums.CODE_TOO_LONG);


// menuPermissionApiDTOList
        List<MenuPermissionApiDTO> menuPermissionApiDTOList = Optional.ofNullable(role.getMenuPermissionApiDTOList()).orElse(Lists.newArrayList());

// countryCode
        String countryCode = role.getCountryCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(countryCode), PermissionErrorCodeEnums.COUNTRY_CODE_NOT_NULL);

// xmileRoleType
        Integer xmileRoleType = role.getXmileRoleType();
        BusinessLogicException.checkTrue(Objects.isNull(xmileRoleType), PermissionErrorCodeEnums.XMILE_ROLE_TYPE_NOT_NULL);

        String jsonString = JSON.toJSONString(Arrays.asList("iMile"));

        SysRoleDO roleDO = new SysRoleDO();
        roleDO.setOrgId(orgId);
        roleDO.setCode(code);
        roleDO.setRoleName(roleName);
        roleDO.setRoleNameEn(roleNameEn);
        roleDO.setIsDisable(isDisable);
        roleDO.setAdminOrg(adminOrg);
        roleDO.setOwnStationCode(ownStationCode);
        roleDO.setDescription(description);
        roleDO.setCountryCode(countryCode);
        roleDO.setXmileRoleType(xmileRoleType);
        roleDO.setSystemPlatformJson(jsonString);
        roleDO.setMultipleSystem(jsonString);
        roleDO.setRolePropertiesJson("{\"employmentTypeList\":[\"Employee\",\"SubEmployee\",\"OSFixedsalary\",\"Intern\",\"PartTimer\",\"OSPerdelivered\",\"Freelancer\",\"Consultant\"]}");

        operatorSystemPermissionManage.saveRole(roleDO);
        operatorSystemPermissionManage.saveRoleMenu(roleDO.getId(), OrikaUtil.mapAsList(menuPermissionApiDTOList, MenuPermissionDTO.class));
        return RpcResult.ok(Boolean.TRUE);
    }

    @Override
    public RpcResult<Boolean> updateRole(OperatorSystemRoleUpdateApiDTO role) {
// id
        Long id = role.getId();
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);

// roleName
        String roleName = role.getRoleName();
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleName), PermissionErrorCodeEnums.ROLE_NAME_NOT_NULL);
        BusinessLogicException.checkTrue(roleName.getBytes(StandardCharsets.UTF_8).length > 255, PermissionErrorCodeEnums.ROLE_NAME_TOO_LONG);

// roleNameEn
        String roleNameEn = role.getRoleNameEn();
        BusinessLogicException.checkTrue(StringUtils.isBlank(roleNameEn), PermissionErrorCodeEnums.ROLE_NAME_EN_NOT_NULL);
        BusinessLogicException.checkTrue(roleNameEn.getBytes(StandardCharsets.UTF_8).length > 255, PermissionErrorCodeEnums.ROLE_NAME_EN_TOO_LONG);

// description
        String description = Optional.ofNullable(role.getDescription()).orElse("");
        BusinessLogicException.checkTrue(description.getBytes(StandardCharsets.UTF_8).length > 500, PermissionErrorCodeEnums.DESCRIPTION_TOO_LONG);

// menuPermissionApiDTOList
        List<MenuPermissionApiDTO> menuPermissionApiDTOList = Optional.ofNullable(role.getMenuPermissionApiDTOList()).orElse(Lists.newArrayList());

        SysRoleDO roleDO = new SysRoleDO();

        roleDO.setId(id);
        roleDO.setRoleName(roleName);
        roleDO.setRoleNameEn(roleNameEn);
        roleDO.setDescription(description);

        operatorSystemPermissionManage.updateRoleById(roleDO);
        operatorSystemPermissionManage.updateRoleMenu(roleDO.getId(), OrikaUtil.mapAsList(menuPermissionApiDTOList, MenuPermissionDTO.class));
        return RpcResult.ok(Boolean.TRUE);

    }

    @Override
    public RpcResult<Boolean> updateRoleStatus(OperatorSystemUpdateRoleStatusApiDTO operatorSystemUpdateRoleStatusApiDTO) {
        Long id = operatorSystemUpdateRoleStatusApiDTO.getId();
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);

        Integer isDisable = operatorSystemUpdateRoleStatusApiDTO.getIsDisable();
        BusinessLogicException.checkTrue(Objects.isNull(isDisable), PermissionErrorCodeEnums.IS_DISABLE_NOT_NULL);

        SysRoleDO roleDO = new SysRoleDO();
        roleDO.setId(id);
        roleDO.setIsDisable(isDisable);
        operatorSystemPermissionManage.updateRoleById(roleDO);

        return RpcResult.ok(Boolean.TRUE);
    }

    @Override
    public RpcResult<PaginationResult<OperatorSystemRoleListApiDTO>> getRolesPaginated(OperatorSystemRoleQueryApiDTO operatorSystemRoleApiQuery) {
        OperatorSystemRoleQueryDTO operatorSystemRoleQuery = OrikaUtil.map(operatorSystemRoleApiQuery, OperatorSystemRoleQueryDTO.class);

        // orgId
        Long orgId = operatorSystemRoleQuery.getOrgId();
        BusinessLogicException.checkTrue(Objects.isNull(orgId), PermissionErrorCodeEnums.ORG_ID_NOT_NULL);
        PaginationResult<OperatorSystemRoleListDTO> paginationResult = operatorSystemPermissionManage.getRolesPaginated(operatorSystemRoleQuery);

        List<OperatorSystemRoleListApiDTO> operatorSystemRoleListApiDTOS = OrikaUtil.mapAsList(paginationResult.getResults(), OperatorSystemRoleListApiDTO.class);
        return RpcResult.ok(PageUtil.getPageResult(operatorSystemRoleListApiDTOS, operatorSystemRoleQuery, paginationResult.getPagination()));
    }

    @Override
    public RpcResult<OperatorSystemRoleDetailApiDTO> getRoleById(Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);
        SysRoleDO roleDO = operatorSystemPermissionManage.getRoleById(id);
        BusinessLogicException.checkTrue(Objects.isNull(roleDO), PermissionErrorCodeEnums.ROLE_NOT_EXIST);
        List<MenuPermissionDTO> menuPermissionDTO = operatorSystemPermissionManage.getMenuPermissionByRoleId(id);
        List<MenuPermissionApiDTO> menuPermissionApiDTOS = OrikaUtil.mapAsList(menuPermissionDTO, MenuPermissionApiDTO.class);
        OperatorSystemRoleDetailApiDTO operatorSystemRoleDetailApiDTO = OrikaUtil.map(roleDO, OperatorSystemRoleDetailApiDTO.class);
        operatorSystemRoleDetailApiDTO.setMenuPermissionApiDTOList(menuPermissionApiDTOS);
        return RpcResult.ok(operatorSystemRoleDetailApiDTO);
    }

    @Override
    public RpcResult<PaginationResult<String>> getUserCodesByRole(OperatorSystemRoleUserApiDTO param) {
        OperatorSystemRoleUserDTO role = OrikaUtil.map(param, OperatorSystemRoleUserDTO.class);
// 从 role 中获取相关属性
        Long id = role.getId();

// 使用 PermissionErrorCodeEnums 进行异常检查
        BusinessLogicException.checkTrue(Objects.isNull(id), PermissionErrorCodeEnums.ID_NOT_NULL);

        PaginationResult<String> paginationResult = operatorSystemPermissionManage.getUserCodesByRole(role);
        return RpcResult.ok(paginationResult);
    }

    @Override
    public RpcResult<List<OperatorSystemRoleListApiDTO>> getRoleByUser(String userCode, Long orgId) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(userCode), PermissionErrorCodeEnums.USER_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(orgId), PermissionErrorCodeEnums.ORG_ID_NOT_NULL);

        List<OperatorSystemRoleListDTO> list = operatorSystemPermissionManage.getRoleByUser(userCode, orgId);
        List<OperatorSystemRoleListApiDTO> operatorSystemRoleListApiDTOS = OrikaUtil.mapAsList(list, OperatorSystemRoleListApiDTO.class);
        return RpcResult.ok(operatorSystemRoleListApiDTOS);
    }

    @Override
    public RpcResult<List<OperatorSystemRoleListApiDTO>> getRoleByMenuId(List<Long> menuIdList, Long orgId) {
        BusinessLogicException.checkTrue(Objects.isNull(menuIdList), PermissionErrorCodeEnums.MENU_PERMISSION_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(orgId), PermissionErrorCodeEnums.ORG_ID_NOT_NULL);
        BusinessLogicException.checkTrue(menuIdList.size() > 10, PermissionErrorCodeEnums.MENU_PERMISSION_TOO_MUCH);
        List<OperatorSystemRoleListDTO> list = operatorSystemPermissionManage.getRoleByMenuId(menuIdList, orgId);
        List<OperatorSystemRoleListApiDTO> operatorSystemRoleListApiDTOS = OrikaUtil.mapAsList(list, OperatorSystemRoleListApiDTO.class);
        return RpcResult.ok(operatorSystemRoleListApiDTOS);
    }
}

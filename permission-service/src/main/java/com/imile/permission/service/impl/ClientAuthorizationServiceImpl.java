package com.imile.permission.service.impl;

import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.permission.convert.SysRoleConvert;
import com.imile.permission.domain.applicationApprove.vo.EffectiveLossCountVO;
import com.imile.permission.domain.client.param.BindingParentRoleParam;
import com.imile.permission.domain.client.param.ClientRoleRevokeParam;
import com.imile.permission.domain.client.query.ClientRecordPageQuery;
import com.imile.permission.domain.client.query.ClientRoleDetailsQuery;
import com.imile.permission.domain.client.vo.ClientRoleDetailsVO;
import com.imile.permission.domain.client.vo.ClientRoleRecordVO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.entity.ClientRoleAuthorityCollectDO;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.permission.dto.PermissionDTO;
import com.imile.permission.domain.role.vo.ParentChildRoleVO;
import com.imile.permission.domain.user.vo.ParentChildClientRoleMenuVO;
import com.imile.permission.enums.PermissionErrorCodeEnums;
import com.imile.permission.exception.BusinessLogicException;
import com.imile.permission.manage.ClientRoleAuthorityManage;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.manage.RolePostManage;
import com.imile.permission.service.ClientAuthorizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
public class ClientAuthorizationServiceImpl implements ClientAuthorizationService {

    @Autowired
    private ClientRoleAuthorityManage clientRoleAuthorityManage;

    @Autowired
    private RolePostManage rolePostManage;

    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;

    @Override
    public ParentChildRoleVO getBindingRole(String clientCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(clientCode), PermissionErrorCodeEnums.CLIENT_CODE_NOT_NULL);
        List<ClientRoleAuthorityCollectDO> list = clientRoleAuthorityManage.getActiveRoleByClientCode(clientCode, LocalDateTime.now());
        Long parentRoleId = null;
        SysRoleDO role = null;
        if (CollectionUtils.isEmpty(list)) {
            role = rolePostManage.getClientDefaultRole();
            if (Objects.nonNull(role)) {
                parentRoleId = role.getId();
            }
        } else {
            parentRoleId = list.get(0).getRoleId();
        }

        if (Objects.isNull(parentRoleId)) {
            return null;
        }

        if (Objects.isNull(role)) {
            role = rolePostManage.getById(parentRoleId);
        }

        if (Objects.isNull(role)) {
            return null;
        }

        SysRoleDO childRole = rolePostManage.getChildRole(parentRoleId);
        ParentChildRoleVO parentChildRoleVO = new ParentChildRoleVO();
        parentChildRoleVO.setParentRole(SysRoleConvert.sysRoleDOToRoleVO(role));
        parentChildRoleVO.setChildRole(SysRoleConvert.sysRoleDOToRoleVO(childRole));
        return parentChildRoleVO;
    }

    @Override
    public List<ParentChildRoleVO> parentChildRoleList() {
        return rolePostManage.parentChildRoleList();
    }

    @Override
    public void bindingParentRole(BindingParentRoleParam bindingParentRoleParam) {
        Long parentRoleId = bindingParentRoleParam.getParentRoleId();
        String clientCode = bindingParentRoleParam.getClientCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(clientCode), PermissionErrorCodeEnums.CLIENT_CODE_NOT_NULL);
        if (Objects.isNull(parentRoleId)) {
            clientRoleAuthorityManage.cleanRole(clientCode);
            return;
        }
        SysRoleDO role = rolePostManage.getById(parentRoleId);
        BusinessLogicException.checkTrue(Objects.isNull(role), PermissionErrorCodeEnums.ROLE_NOT_EXIST);
        clientRoleAuthorityManage.bindingClientCodeRole(clientCode, parentRoleId);
    }

    @Override
    public PaginationResult<ClientRoleRecordVO> roleRecordPage(ClientRecordPageQuery clientRecordPageQuery) {
        String clientCode = clientRecordPageQuery.getClientCode();
        Boolean isExpired = Optional.ofNullable(clientRecordPageQuery.getIsExpired()).orElse(Boolean.FALSE);
        BusinessLogicException.checkTrue(StringUtils.isBlank(clientCode), PermissionErrorCodeEnums.CLIENT_CODE_NOT_NULL);
        return clientRoleAuthorityManage.roleRecordPage(clientRecordPageQuery, clientCode, isExpired);
    }

    @Override
    public EffectiveLossCountVO getRecordCount(String clientCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(clientCode), PermissionErrorCodeEnums.CLIENT_CODE_NOT_NULL);
        EffectiveLossCountVO count = clientRoleAuthorityManage.getRecordCount(clientCode);
        return count;
    }

    @Override
    public void revokeRole(ClientRoleRevokeParam param) {
        String clientCode = param.getClientCode();
        Long roleId = param.getRoleId();
        BusinessLogicException.checkTrue(StringUtils.isBlank(clientCode), PermissionErrorCodeEnums.CLIENT_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_NOT_EXIST);
        clientRoleAuthorityManage.revokeRole(clientCode, roleId);

    }

    @Override
    public ParentChildClientRoleMenuVO parentChildClientRoleMenu(Long roleId) {
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_NOT_EXIST);
        SysRoleDO role = rolePostManage.getById(roleId);
        BusinessLogicException.checkTrue(Objects.isNull(role), PermissionErrorCodeEnums.ROLE_NOT_EXIST);
        Long parentId = role.getParentId();
        ParentChildClientRoleMenuVO parentChildClientRoleMenuVO = new ParentChildClientRoleMenuVO();
        ParentChildClientRoleMenuVO.ClientRoleMenuVO parentClientRoleMenu = new ParentChildClientRoleMenuVO.ClientRoleMenuVO();
        if (parentId == 0) {
            PermissionDTO parentRole = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(roleId);

            parentClientRoleMenu.setRoleId(roleId);
            parentClientRoleMenu.setRoleName(role.getRoleName());
            parentClientRoleMenu.setRoleNameEn(role.getRoleNameEn());
            parentClientRoleMenu.setMenuPermissionDTOList(Optional.ofNullable(parentRole.getMenuPermissionDTOList()).orElse(Lists.newArrayList()));
            parentChildClientRoleMenuVO.setParentClientRoleMenu(parentClientRoleMenu);

            SysRoleDO childRole = rolePostManage.getChildRole(roleId);
            if (Objects.nonNull(childRole)) {
                PermissionDTO childRolePermission = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(childRole.getId());
                ParentChildClientRoleMenuVO.ClientRoleMenuVO childClientRoleMenu = new ParentChildClientRoleMenuVO.ClientRoleMenuVO();
                childClientRoleMenu.setRoleId(childRole.getId());
                childClientRoleMenu.setRoleName(childRole.getRoleName());
                childClientRoleMenu.setRoleNameEn(childRole.getRoleNameEn());
                childClientRoleMenu.setMenuPermissionDTOList(Optional.ofNullable(childRolePermission.getMenuPermissionDTOList()).orElse(Lists.newArrayList()));
                parentChildClientRoleMenuVO.setChildClientRoleMenu(childClientRoleMenu);
            }
        } else {
            SysRoleDO parent = rolePostManage.getById(parentId);
            if (Objects.nonNull(parent)) {
                PermissionDTO parentRole = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(parent.getId());
                parentClientRoleMenu.setRoleId(parent.getParentId());
                parentClientRoleMenu.setRoleName(parent.getRoleName());
                parentClientRoleMenu.setRoleNameEn(parent.getRoleNameEn());
                parentClientRoleMenu.setMenuPermissionDTOList(Optional.ofNullable(parentRole.getMenuPermissionDTOList()).orElse(Lists.newArrayList()));
                parentChildClientRoleMenuVO.setParentClientRoleMenu(parentClientRoleMenu);
            }

            PermissionDTO childRolePermission = refactoringPermissionCasbinRuleManage.getPermissionByRoleId(roleId);
            ParentChildClientRoleMenuVO.ClientRoleMenuVO childClientRoleMenu = new ParentChildClientRoleMenuVO.ClientRoleMenuVO();
            childClientRoleMenu.setRoleId(role.getId());
            childClientRoleMenu.setRoleName(role.getRoleName());
            childClientRoleMenu.setRoleNameEn(role.getRoleNameEn());
            childClientRoleMenu.setMenuPermissionDTOList(Optional.ofNullable(childRolePermission.getMenuPermissionDTOList()).orElse(Lists.newArrayList()));
            parentChildClientRoleMenuVO.setChildClientRoleMenu(childClientRoleMenu);

        }

        return parentChildClientRoleMenuVO;
    }

    @Override
    public ParentChildClientRoleMenuVO clientMenu(String clientCode) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(clientCode), PermissionErrorCodeEnums.CLIENT_CODE_NOT_NULL);

        List<ClientRoleAuthorityCollectDO> list = clientRoleAuthorityManage.getActiveRoleByClientCode(clientCode, LocalDateTime.now());
        Long parentRoleId = null;
        SysRoleDO role = null;
        if (CollectionUtils.isEmpty(list)) {
            role = rolePostManage.getClientDefaultRole();
            if (Objects.nonNull(role)) {
                parentRoleId = role.getId();
                return parentChildClientRoleMenu(parentRoleId);
            }
        } else {
            parentRoleId = list.get(0).getRoleId();
        }

        if (Objects.isNull(parentRoleId)) {
            return null;
        }

        // 禁用角色检查
        SysRoleDO currentParent = rolePostManage.getById(parentRoleId);
        // 角色禁用查询默认角色
        if (currentParent.getIsDisable().intValue() == 1) {
            role = rolePostManage.getClientDefaultRole();
            if (Objects.nonNull(role)) {
                parentRoleId = role.getId();
                return parentChildClientRoleMenu(parentRoleId);
            }
        }

        return parentChildClientRoleMenu(parentRoleId);
    }

    @Override
    public List<ClientRoleDetailsVO> getRoleApplicationDetails(ClientRoleDetailsQuery roleApplicationDetailsQuery) {
        Long roleId = roleApplicationDetailsQuery.getRoleId();
        String clientCode = roleApplicationDetailsQuery.getClientCode();
        BusinessLogicException.checkTrue(StringUtils.isBlank(clientCode), PermissionErrorCodeEnums.CLIENT_CODE_NOT_NULL);
        BusinessLogicException.checkTrue(Objects.isNull(roleId), PermissionErrorCodeEnums.ROLE_NOT_EXIST);
        List<ClientRoleDetailsVO> list = clientRoleAuthorityManage.getRoleApplicationDetails(roleApplicationDetailsQuery);
        return list;
    }
}

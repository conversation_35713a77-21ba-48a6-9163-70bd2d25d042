package com.imile.permission.converter;

import com.google.common.collect.Lists;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.dto.DimensionDataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.vo.DimensionConfigDTO;
import com.imile.permission.domain.dataPermission.vo.MultiDynamicDataConfigValueDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/23
 */
public class DimensionConfigVoToParamConverter {

    public static @NotNull List<MultiDynamicDataConfigValueDTO> convert(List<DimensionConfigDTO> multipleListInVO) {
        List<MultiDynamicDataConfigValueDTO> multipleParam = Lists.newArrayList();
        if (CollectionUtils.isEmpty(multipleListInVO)) {
            return multipleParam;
        }
        for (DimensionConfigDTO dimensionConfigDTO : multipleListInVO) {
            if(dimensionConfigDTO == null){
                continue;
            }
            MultiDynamicDataConfigValueDTO item = new MultiDynamicDataConfigValueDTO();
            item.setTypeCode(dimensionConfigDTO.getTypeCode());
            item.setTypeName(dimensionConfigDTO.getTypeName());
            List<DimensionDataPermissionRuleDTO> dimensionConfigParamList = Lists.newArrayList();
            List<DataPermissionRuleDTO> dimensionConfigList = dimensionConfigDTO.getDimensionConfigList();
            if (CollectionUtils.isNotEmpty(dimensionConfigList)) {
                for (DataPermissionRuleDTO dataPermissionRuleDTO : dimensionConfigList) {
                    if(dataPermissionRuleDTO == null){
                        continue;
                    }
                    DimensionDataPermissionRuleDTO dimensionItem = new DimensionDataPermissionRuleDTO();
                    dimensionItem.setTypeCode(dataPermissionRuleDTO.getTypeCode());
                    dimensionItem.setTypeName(dataPermissionRuleDTO.getTypeName());
                    dimensionItem.setDataCodeList(dataPermissionRuleDTO.getDataCodeList());
                    dimensionItem.setDataExtensionTagList(dataPermissionRuleDTO.getDataExtensionTagList());
                    dimensionConfigParamList.add(dimensionItem);
                }
            }
            item.setDimensionConfigList(dimensionConfigParamList);
            multipleParam.add(item);
        }
        return multipleParam;
    }
}

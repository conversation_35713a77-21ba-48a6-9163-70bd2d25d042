package com.imile.permission.converter;

import com.alibaba.fastjson.JSONArray;
import com.imile.permission.api.dto.HRMSRoleDTO;
import com.imile.permission.api.dto.MenuTreeDTO;
import com.imile.permission.domain.role.dto.UserRoleDTO;
import com.imile.resource.api.dto.ResSystemResourceTreeApiDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/7
 */
public class HRMSOperationConvert {
    public static HRMSRoleDTO getHrmsRoleDTO(UserRoleDTO roleDTO) {
        if (roleDTO == null) {
            return null;
        }
        HRMSRoleDTO role = new HRMSRoleDTO();
        role.setRoleId(roleDTO.getRoleId());
        role.setRoleName(roleDTO.getRoleName());
        role.setRoleNameEn(roleDTO.getRoleNameEn());
        role.setIsDisable(roleDTO.getIsDisable());
        role.setDescription(roleDTO.getDescription());
        role.setDescriptionEn(roleDTO.getDescriptionEn());
        return role;
    }

    public static List<MenuTreeDTO> getMenuTreeDTO(List<ResSystemResourceTreeApiDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return JSONArray.parseArray(JSONArray.toJSONString(list), MenuTreeDTO.class);
    }
}

package com.imile.permission.mq.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.domain.common.MQMessageDTO;
import com.imile.permission.domain.mq.PermissionDynamicDataOperationDTO;
import com.imile.permission.enums.DynamicDataDataTypeEnum;
import com.imile.permission.enums.DynamicDataOperationTypeEnum;
import com.imile.permission.manage.PermissionRefreshManage;
import com.imile.permission.service.PermissionCacheService;
import com.imile.permission.util.CasbinParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class PermissionUserCacheRefreshListener implements MessageListenerConcurrently {

    @Autowired
    private PermissionRefreshManage permissionRefreshManage;

    @Autowired
    private PermissionCacheService permissionCacheService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        MessageExt messageExt = list.get(0);
        log.info("收到 PermissionCacheKeyRefresh 的消息，msgId=" + messageExt.getMsgId() + "msgKey=" + messageExt.getKeys());
        byte[] messageBody = messageExt.getBody();
        if (messageBody == null) {
            log.info("收到 PermissionCacheKeyRefresh 的消息，消息内容为空");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        String body = new String(messageBody);
        if (StringUtils.isBlank(body)) {
            log.info("收到 PermissionCacheKeyRefresh 的消息，消息内容为空, body ={}", body);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        MQMessageDTO mqMessageDTO = null;
        try {
            mqMessageDTO = JSON.parseObject(body, MQMessageDTO.class);
        } catch (Exception e) {
            log.error("消息转换异常，消息内容 body={}", body);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        try {
            String message = mqMessageDTO.getMessage();

            if (StringUtils.isBlank(message)) {
                log.info("收到 PermissionUserCacheRefresh 的消息，消息内容为空, message ={}", message);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            if (!message.startsWith(BusinessConstant.CASBIN_U_COLON)) {
                log.info("收到 PermissionUserCacheRefresh 的消息，消息格式错误, body ={}", message);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            String userCode = CasbinParseUtil.getRealValue(message);
            // 刷新用户缓存
            permissionRefreshManage.pushEsWithCache(userCode);
            // 发送 动态数据权限 MQ 消息
            PermissionDynamicDataOperationDTO permissionDynamicDataOperationDTO = new PermissionDynamicDataOperationDTO();
            permissionDynamicDataOperationDTO.setOperationType(DynamicDataOperationTypeEnum.REFRESH.getCode());
            permissionDynamicDataOperationDTO.setSkipRedisCheck(true);
            permissionDynamicDataOperationDTO.setDataType(DynamicDataDataTypeEnum.USERCODE.getCode());
            permissionDynamicDataOperationDTO.setUserCode(Lists.newArrayList(userCode));
            permissionCacheService.dynamicDataPermissionOperation(DynamicDataOperationTypeEnum.REFRESH.getCode(), "U:" + userCode, permissionDynamicDataOperationDTO);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("接收 PermissionUserCacheRefresh 的 mq 消息发生异常，msgId=" + messageExt.getMsgId(), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }
}

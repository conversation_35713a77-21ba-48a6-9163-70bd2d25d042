package com.imile.permission.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.permission.constants.RedisConstant;
import com.imile.permission.dao.AutoAuthorizeLogDAO;
import com.imile.permission.dao.RefactoringPermissionCasbinRuleDAO;
import com.imile.permission.domain.dataPermission.dto.TypeCodeDataCodeListDTO;
import com.imile.permission.domain.entity.AutoAuthorizeLogDO;
import com.imile.permission.domain.entity.RefactoringPermissionCasbinRuleDO;
import com.imile.permission.enums.AutoAuthorizeEnum;
import com.imile.permission.jcasbin.domain.dto.RefactoringPermissionCasbinRuleDTO;
import com.imile.permission.manage.RoleCacheManage;
import com.imile.permission.mq.basic.mapping.PermissionTagMapping;
import com.imile.permission.util.CasbinParseUtil;
import com.imile.permission.util.OrikaUtil;
import com.imile.util.encoder.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RoleAutoAuthorizeListener implements MessageListenerConcurrently {

    @Autowired
    RefactoringPermissionCasbinRuleDAO refactoringPermissionCasbinRuleDAO;

    @Autowired
    private PermissionTagMapping permissionTagMapping;

    @Autowired
    RoleCacheManage roleCacheManage;

    @Autowired
    AutoAuthorizeLogDAO autoAuthorizeLogDAO;

    @Autowired
    ImileRedisClient imileRedisClient;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        MessageExt messageExt = msgs.get(0);
        try {
            log.info("RoleAutoAuthorizeListener | 收到ROLE-AUTO-AUTHORIZE的消息，msgId=" + messageExt.getMsgId() + "msgKey=" + messageExt.getKeys());
            if (StringUtils.isBlank(messageExt.getTags())) {
                log.info("UserAutoAuthorizeListener | 缺少tags，msgId=" + messageExt.getMsgId() + "msgKey=" + messageExt.getKeys());
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            AutoAuthorizeEnum reason = AutoAuthorizeEnum.getInstance(messageExt.getTags());
            if (reason == null) {
                log.info("UserAutoAuthorizeListener | tags不合法，msgId=" + messageExt.getMsgId() + "msgKey=" + messageExt.getKeys());
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            String roleId = messageExt.getKeys();
            JSONArray jsonArray = JSON.parseObject(new String(messageExt.getBody()), JSONArray.class);
            List<RefactoringPermissionCasbinRuleDTO> dtoList = jsonArray.toJavaList(RefactoringPermissionCasbinRuleDTO.class);
            if (CollectionUtils.isEmpty(dtoList)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            if (reason == AutoAuthorizeEnum.AUTO_AUTHORIZE_MENU_MOVE) {
                menuMoveGetLockAndInsert(dtoList, roleId, messageExt, reason);
            } else if (reason == AutoAuthorizeEnum.AUTO_AUTHORIZE_NEXT_LEVEL) {
                nextLevelGetLockAndInsert(dtoList, roleId, messageExt, reason);
            } else if (reason == AutoAuthorizeEnum.AUTO_AUTHORIZE_SELECT_ALL) {
                selectAllGetLockAndInsert(dtoList, roleId, messageExt, reason);
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("RoleAutoAuthorizeListener | 接收ROLE-AUTO-AUTHORIZE的mq消息发生异常，msgId=" + messageExt.getMsgId(), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }

    private void processMenuMoveAutoAuthorize(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId, MessageExt messageExt, AutoAuthorizeEnum reason) {
        List<RefactoringPermissionCasbinRuleDO> entityList = checkMenuCasbin(dtoList, roleId);
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        refactoringPermissionCasbinRuleDAO.patchAdd(entityList);
        Map<String, List<Long>> map = entityList.stream().collect(Collectors.groupingBy(e -> CasbinParseUtil.getRealValue(e.getV1()), Collectors.mapping(
                e -> {
                    String v3 = e.getV3();
                    return Long.valueOf(CasbinParseUtil.getRealValue(v3));
                },
                Collectors.toList()
        )));
        List<AutoAuthorizeLogDO> logList = Lists.newArrayList();
        AutoAuthorizeLogDO log = new AutoAuthorizeLogDO();
        log.setAuthorizeSubject("ROLE");
        log.setAuthorizeSubjectId(roleId);
        log.setAuthorizeTime(LocalDateTime.now());

        log.setDetail(JSON.toJSONString(map));
        log.setAuthorizeType(messageExt.getTags());
        log.setReason(reason.getDesc());
        if (repeatedSubmitCheck(log)) {
            logList.add(log);
        }
        autoAuthorizeLogDAO.saveBatch(logList);
        roleCacheManage.putRoleCache(Long.valueOf(roleId));
    }

    private List<RefactoringPermissionCasbinRuleDO> checkMenuCasbin(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId) {
        List<RefactoringPermissionCasbinRuleDO> checkList = OrikaUtil.mapAsList(dtoList, RefactoringPermissionCasbinRuleDO.class);
        List<RefactoringPermissionCasbinRuleDO> entityList = Lists.newArrayList();
        Map<String, RefactoringPermissionCasbinRuleDO> menuIdMap = Maps.newHashMap();
        Set<String> v3List = Sets.newHashSet();
        for (RefactoringPermissionCasbinRuleDO item : checkList) {
            menuIdMap.put(item.getV3(), item);
            v3List.add(item.getV3());
        }
        List<String> existV3List = refactoringPermissionCasbinRuleDAO.getExistMenuId(CasbinParseUtil.formatR(Long.valueOf(roleId)), Lists.newArrayList(v3List));
        existV3List.forEach(menuIdMap::remove);
        if (CollectionUtils.isNotEmpty(menuIdMap.values())) {
            entityList.addAll(menuIdMap.values());
        }
        return entityList;
    }

    private @NotNull List<RefactoringPermissionCasbinRuleDO> checkCasbin(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId) {
        List<RefactoringPermissionCasbinRuleDO> checkList = OrikaUtil.mapAsList(dtoList, RefactoringPermissionCasbinRuleDO.class);
        List<RefactoringPermissionCasbinRuleDO> entityList = Lists.newArrayList();
        Map<String, List<RefactoringPermissionCasbinRuleDO>> typeMap = checkList.stream().collect(Collectors.groupingBy(RefactoringPermissionCasbinRuleDO::getV1));
        for (Map.Entry<String, List<RefactoringPermissionCasbinRuleDO>> entry : typeMap.entrySet()) {
            Map<String, RefactoringPermissionCasbinRuleDO> dataCodeMap = Maps.newHashMap();
            Set<String> v2List = Sets.newHashSet();
            for (RefactoringPermissionCasbinRuleDO entity : entry.getValue()) {
                dataCodeMap.put(entity.getV2(), entity);
                v2List.add(entity.getV2());
            }
            List<String> existV2List = refactoringPermissionCasbinRuleDAO.getExistDataCodes(CasbinParseUtil.formatR(Long.valueOf(roleId)), entry.getKey(), Lists.newArrayList(v2List));
            existV2List.forEach(dataCodeMap::remove);
            if (CollectionUtils.isNotEmpty(dataCodeMap.values())) {
                entityList.addAll(dataCodeMap.values());
            }
        }
        return entityList;
    }

    public boolean repeatedSubmitCheck(AutoAuthorizeLogDO log) {
        String key = String.format(RedisConstant.RedisPrefix.REPEATED_SUBMIT_KEY, MD5Utils.md5(log.getAuthorizeSubject() + log.getAuthorizeSubjectId() + log.getDetail()));
        return imileRedisClient.setIfNotExist(key, 0, 5L);
    }

    private void menuMoveGetLockAndInsert(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId, MessageExt messageExt, AutoAuthorizeEnum reason) {
        String lockKey = RedisConstant.ROLE_CASBIN_AUTO_MENU_PREFIX + roleId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean isLocked = false;
            do {
                isLocked = lock.tryLock(2, 5, TimeUnit.SECONDS);
                if (isLocked) {
                    // 获取锁成功，执行补全和插入操作
                    processMenuMoveAutoAuthorize(dtoList, roleId, messageExt, reason);
                } else {
                    // 获取锁失败，处理冲突（如重试或直接返回）
                    log.info("RoleAutoAuthorizeListener | 获取锁失败：roleId: {}, dtoList:{}", roleId, dtoList);
                }
            } while (!isLocked);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("RoleAutoAuthorizeListener | 获取锁时被中断：roleId: {}, dtoList:{}", roleId, dtoList);
        } finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void nextLevelGetLockAndInsert(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId, MessageExt messageExt, AutoAuthorizeEnum reason) {
        String lockKey = RedisConstant.ROLE_CASBIN_AUTO_NEXT_LEVEL_PREFIX + roleId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean isLocked = false;
            do {
                isLocked = lock.tryLock(2, 5, TimeUnit.SECONDS);
                if (isLocked) {
                    // 获取锁成功，执行补全和插入操作
                    processNextLevelAutoAuthorize(dtoList, roleId, messageExt, reason);
                } else {
                    // 获取锁失败，处理冲突（如重试或直接返回）
                    log.info("RoleAutoAuthorizeListener | 获取锁失败：roleId: {}, dtoList:{}", roleId, dtoList);
                }
            } while (!isLocked);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("RoleAutoAuthorizeListener | 获取锁时被中断：roleId: {}, dtoList:{}", roleId, dtoList);
        } finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void selectAllGetLockAndInsert(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId, MessageExt messageExt, AutoAuthorizeEnum reason) {
        String lockKey = RedisConstant.ROLE_CASBIN_AUTO_SELECT_ALL_PREFIX + roleId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean isLocked = false;
            do {
                isLocked = lock.tryLock(2, 5, TimeUnit.SECONDS);
                if (isLocked) {
                    // 获取锁成功，执行补全和插入操作
                    processSelectAllAutoAuthorize(dtoList, roleId, messageExt, reason);
                } else {
                    // 获取锁失败，处理冲突（如重试或直接返回）
                    log.info("RoleAutoAuthorizeListener | 获取锁失败：roleId: {}, dtoList:{}", roleId, dtoList);
                }
            } while (!isLocked);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("RoleAutoAuthorizeListener | 获取锁时被中断：roleId: {}, dtoList:{}", roleId, dtoList);
        } finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void processNextLevelAutoAuthorize(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId, MessageExt messageExt, AutoAuthorizeEnum reason) {
        List<RefactoringPermissionCasbinRuleDO> entityList = checkCasbin(dtoList, roleId);
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        refactoringPermissionCasbinRuleDAO.patchAdd(entityList);
        Map<String, List<RefactoringPermissionCasbinRuleDO>> map = entityList.stream().collect(Collectors.groupingBy(e -> CasbinParseUtil.getRealValue(e.getV1())));
        List<AutoAuthorizeLogDO> logList = Lists.newArrayList();
        for (Map.Entry<String, List<RefactoringPermissionCasbinRuleDO>> entry : map.entrySet()) {
            AutoAuthorizeLogDO log = new AutoAuthorizeLogDO();
            log.setAuthorizeSubject("ROLE");
            log.setAuthorizeSubjectId(roleId);
            log.setAuthorizeTime(LocalDateTime.now());
            TypeCodeDataCodeListDTO dto = new TypeCodeDataCodeListDTO();
            dto.setTypeCode(entry.getKey());
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                List<String> dataCodeList = entry.getValue().stream().map(e -> CasbinParseUtil.getRealValue(e.getV2())).collect(Collectors.toList());
                dto.setDataCodeList(dataCodeList);
            }
            log.setDetail(JSON.toJSONString(dto));
            log.setAuthorizeType(messageExt.getTags());
            log.setReason(reason.getDesc());
            if (repeatedSubmitCheck(log)) {
                logList.add(log);
            }
        }
        autoAuthorizeLogDAO.saveBatch(logList);
        roleCacheManage.putRoleCache(Long.valueOf(roleId));
    }

    private void processSelectAllAutoAuthorize(List<RefactoringPermissionCasbinRuleDTO> dtoList, String roleId, MessageExt messageExt, AutoAuthorizeEnum reason) {
        List<RefactoringPermissionCasbinRuleDO> entityList = checkCasbin(dtoList, roleId);
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        refactoringPermissionCasbinRuleDAO.patchAdd(entityList);
        Map<String, List<RefactoringPermissionCasbinRuleDO>> map = entityList.stream().collect(Collectors.groupingBy(e -> CasbinParseUtil.getRealValue(e.getV1())));
        List<AutoAuthorizeLogDO> logList = Lists.newArrayList();
        for (Map.Entry<String, List<RefactoringPermissionCasbinRuleDO>> entry : map.entrySet()) {
            AutoAuthorizeLogDO log = new AutoAuthorizeLogDO();
            log.setAuthorizeSubject("ROLE");
            log.setAuthorizeSubjectId(roleId);
            log.setAuthorizeTime(LocalDateTime.now());
            TypeCodeDataCodeListDTO dto = new TypeCodeDataCodeListDTO();
            dto.setTypeCode(entry.getKey());
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                List<String> dataCodeList = entry.getValue().stream().map(e -> CasbinParseUtil.getRealValue(e.getV2())).collect(Collectors.toList());
                dto.setDataCodeList(dataCodeList);
            }
            log.setDetail(JSON.toJSONString(dto));
            log.setAuthorizeType(messageExt.getTags());
            log.setReason(reason.getDesc());
            if (repeatedSubmitCheck(log)) {
                logList.add(log);
            }
        }
        autoAuthorizeLogDAO.saveBatch(logList);
        roleCacheManage.putRoleCache(Long.valueOf(roleId));
    }
}

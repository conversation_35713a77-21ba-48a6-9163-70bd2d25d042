package com.imile.permission.mq.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.imile.hrms.api.base.component.DifferField;
import com.imile.hrms.api.user.param.UserEventParam;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.domain.relation.dto.UserPostDTO;
import com.imile.permission.mq.basic.MqMsgBusinessProcessor;
import com.imile.permission.mq.basic.MsgType;
import com.imile.permission.service.SysPostRoleRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class HrmsBaseInfoChangeEventProcessor implements MqMsgBusinessProcessor {

    @Autowired
    private SysPostRoleRelationService sysPostRoleRelationService;


    @Override
    public MsgType getMsgType() {
        return MsgType.HRMS_BASE_INFO_CHANGE_EVENT;
    }

    @Override
    public void execute(MessageExt message) {
        UserEventParam<UserEventParam.BaseInfoChange> entry = JSON.parseObject(new String(message.getBody()),
                new TypeReference<UserEventParam<UserEventParam.BaseInfoChange>>() {});
        String userCode = entry.getUserCode();
        log.info("HrmsBaseInfoChangeEventProcessor | execute | userCode {}", userCode);
        UserEventParam.BaseInfoChange body = entry.getBody();
        if (Objects.isNull(body)) {
            return;
        }
        DifferField differField = Optional.ofNullable(body.getChangeFieldList()).orElse(Lists.newArrayList()).stream().filter(
                changeField -> BusinessConstant.POST_ID.equals(changeField.getFieldName())).findFirst().orElse(null);
        if (Objects.isNull(differField) || differField.getAfterValue().equals(differField.getBeforeValue())) {
            log.info("HrmsBaseInfoChangeEventProcessor | execute | userCode {} postId no change, post {}", userCode, JSON.toJSONString(differField));
            return;
        }
        UserPostDTO userPostDTO = new UserPostDTO();
        userPostDTO.setUserCode(userCode);
        userPostDTO.setBeforePostId(Long.valueOf((Integer)differField.getBeforeValue()));
        userPostDTO.setPostId(Long.valueOf((Integer)differField.getAfterValue()));
        sysPostRoleRelationService.userPostChange(userPostDTO);
    }

}

package com.imile.permission.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.imile.hermes.common.EventConstant;
import com.imile.hermes.common.EventDTO;
import com.imile.hermes.common.payload.OcInfoPayload;
import com.imile.permission.domain.common.OcInfoPayloadDTO;
import com.imile.permission.service.HermesOcEventService;
import com.imile.permission.util.OrikaUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class HermesOcEventListener implements MessageListenerOrderly {

    @Autowired
    private HermesOcEventService hermesOcEventService;


    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
        try {
            MessageExt messageExt = list.get(0);
            log.info("HermesOcEventListener | consumeMessage | 收到网点变更的消息，msgId=" + messageExt.getMsgId() + "msgKey=" + messageExt.getKeys());
            byte[] messageBody = messageExt.getBody();
            if (messageBody == null) {
                log.info("HermesOcEventListener | consumeMessage | 收到网点变更的消息，消息内容为空");
                return ConsumeOrderlyStatus.SUCCESS;
            }
            String str = new String(messageBody);
            if (StringUtils.isBlank(str)) {
                log.info("HermesOcEventListener | consumeMessage | 收到网点变更的消息, str is null");
                return ConsumeOrderlyStatus.SUCCESS;
            }
            log.info("HermesOcEventListener | consumeMessage | 收到网点变更的消息, str is {}", str);

            EventDTO<OcInfoPayload> eventDTO = JSON.parseObject(str, new TypeReference<EventDTO<OcInfoPayload>>(){});
            OcInfoPayload payload = eventDTO.getPayload();
            if (Objects.isNull(payload)) {
                log.info("HermesOcEventListener | consumeMessage | 收到网点变更的消息, payload is null");
                return ConsumeOrderlyStatus.SUCCESS;
            }
            OcInfoPayloadDTO ocInfoPayloadDTO = OrikaUtil.map(payload, OcInfoPayloadDTO.class);
            if (EventConstant.OperateType.ADD_OC.equals(eventDTO.getOperateType())) {
                hermesOcEventService.handlerOcAdd(ocInfoPayloadDTO);
                return ConsumeOrderlyStatus.SUCCESS;
            }

            if (EventConstant.OperateType.DELETE_OC.equals(eventDTO.getOperateType())) {
                hermesOcEventService.handlerOcDelete(ocInfoPayloadDTO);
                return ConsumeOrderlyStatus.SUCCESS;
            }
        } catch (Exception e) {
            return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
        }

        return ConsumeOrderlyStatus.SUCCESS;
    }
}

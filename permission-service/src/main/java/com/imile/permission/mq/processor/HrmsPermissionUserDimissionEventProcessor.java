package com.imile.permission.mq.processor;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.mq.BpmDimissionMqDTO;
import com.imile.permission.domain.applicationApprove.query.AuthorityApprovalPageQuery;
import com.imile.permission.mq.basic.MqMsgBusinessProcessor;
import com.imile.permission.mq.basic.MsgType;
import com.imile.permission.service.AuthorityApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
public class HrmsPermissionUserDimissionEventProcessor implements MqMsgBusinessProcessor {

    @Autowired
    private AuthorityApprovalService authorityApprovalService;

    @Override
    public MsgType getMsgType() {
        return MsgType.HRMS_PERMISSION_USER_DIMISSION_EVENT;
    }

    @Override
    public void execute(MessageExt message) {
        log.info("HrmsPermissionUserDimissionEventProcessor | execute:{}", JSON.toJSON(message));
        BpmDimissionMqDTO bpmDimissionMqDTO = JSON.parseObject(new String(message.getBody()), BpmDimissionMqDTO.class);
        AuthorityApprovalPageQuery query = getAuthorityApprovalPageQuery(bpmDimissionMqDTO);
        authorityApprovalService.recycleUserPermission(query);

    }

    private static @NotNull AuthorityApprovalPageQuery getAuthorityApprovalPageQuery(BpmDimissionMqDTO bpmDimissionMqDTO) {
        AuthorityApprovalPageQuery query = new AuthorityApprovalPageQuery();
        query.setUserCode(bpmDimissionMqDTO.getDimissionUserCode());
        query.setIsExpired(false);
        query.setExpirationDate(LocalDateTime.now());
        return query;
    }
}

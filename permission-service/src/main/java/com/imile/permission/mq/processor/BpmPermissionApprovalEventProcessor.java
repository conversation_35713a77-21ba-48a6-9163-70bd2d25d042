package com.imile.permission.mq.processor;

import com.alibaba.fastjson.JSON;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.permission.mq.basic.MqMsgBusinessProcessor;
import com.imile.permission.mq.basic.MsgType;
import com.imile.permission.service.AuthorityApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: This is a sample class.
 * @author: 
 * @date: 2023/10/16
 */
@Slf4j
@Service
public class BpmPermissionApprovalEventProcessor implements MqMsgBusinessProcessor {

    @Autowired
    private AuthorityApprovalService authorityApprovalService;

    @Override
    public MsgType getMsgType() {
        return MsgType.BPM_PERMISSION_APPROVAL_PUSH_STATUS_EVENT;
    }

    @Override
    public void execute(MessageExt message) {
        log.info("BpmHrApprovalEventProcessor | execute:{}", JSON.toJSON(message));
        ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(new String(message.getBody()), ApprovalPushStatusMsgDTO.class);
        authorityApprovalService.authorityApprovalMqHandler(approvalPushStatusMsgDTO);
    }
}
